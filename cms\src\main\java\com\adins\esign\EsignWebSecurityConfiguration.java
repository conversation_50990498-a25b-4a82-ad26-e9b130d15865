package com.adins.esign;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSessionListener;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.BeanIds;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.endpoint.FrameworkEndpoint;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JdbcTokenStore;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.session.HttpSessionEventPublisher;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import com.adins.framework.service.security.ApiKeyAuthenticationFilter;
import com.adins.framework.service.security.ApiKeyAuthenticator;
import com.adins.framework.service.security.RestAuthenticationEntryPoint;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmMsrole;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsLov;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.impl.provider.EsignAuthenticationToken;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.factory.api.DaoFactory;

@EnableAuthorizationServer
@EnableResourceServer
@EnableWebSecurity
@EnableGlobalMethodSecurity(securedEnabled=true, prePostEnabled=true, jsr250Enabled=true)
@Configuration
public class EsignWebSecurityConfiguration {
	
	EsignWebSecurityConfiguration() {
		
	}
			
	@Order(1)
	@Configuration
	public static class ApiSecurityConfiguration extends WebSecurityConfigurerAdapter {	
		@Value("${security.enable-csrf}")
		private boolean csrfEnabled;
		
		@Autowired
		private ApiKeyAuthenticator apiKeyAuthenticator;
		
		@Bean
		public RestAuthenticationEntryPoint restAuthenticationEntryPoint() {
			return new RestAuthenticationEntryPoint();
		}
		
		@Bean
		public Filter apiKeyAuthenticationFilter() {
			ApiKeyAuthenticationFilter filter = new ApiKeyAuthenticationFilter();
			filter.setRoleGranted("ROLE_CORESYSTEM");
			filter.setApiKeyAuthenticator(apiKeyAuthenticator);
			return filter;
		}

		@Override
		protected void configure(HttpSecurity http) throws Exception {
			http
				.requestMatchers()
				.antMatchers("/services/confins/**", "/services/external/**", "/services/user/generateInvitationLink", "/services/user/generateInvitationLinkV2", "/services/user/registerBM", "/services/user/updateUserData", "/services/document/retryStamping", "/services/data/getListPeruriDocumentType", "/services/data/getDocumentType", "/services/document/stampEmeterai", "/services/document/saveResultSign")
				.and()
				.headers().disable()
				.logout().disable()
				.sessionManagement().disable()
				.authorizeRequests()
				.antMatchers("/services/confins/**", "/services/external/**", "/services/user/generateInvitationLink", "/services/user/generateInvitationLinkV2", "/services/user/registerBM", "/services/user/updateUserData", "/services/document/retryStamping", "/services/data/getListPeruriDocumentType", "/services/data/getDocumentType", "/services/document/stampEmeterai", "/services/document/saveResultSign").hasRole("CORESYSTEM")
				.and()
				.addFilterAfter(apiKeyAuthenticationFilter(), BasicAuthenticationFilter.class);
	
			http
				.sessionManagement()
				.sessionCreationPolicy(SessionCreationPolicy.STATELESS);
	
			if (!csrfEnabled) {
				http.csrf().disable();
			}
		}
		
		@Qualifier("customAuthenticationProvider")
		@Autowired
		private AuthenticationProvider customAuthenticationProvider;
		
	    @Bean(BeanIds.AUTHENTICATION_MANAGER)
	    @Override
	    public AuthenticationManager authenticationManagerBean() throws Exception {
	        return super.authenticationManagerBean();
	    }
	    
	    @Override
	    public void configure(AuthenticationManagerBuilder auth) throws Exception {
	        auth.authenticationProvider(customAuthenticationProvider);
	    }
	}
	
	@Order(20)
	@Configuration
	@FrameworkEndpoint
	public static class OAuth2Configuration extends AuthorizationServerConfigurerAdapter implements ResourceServerConfigurer {		
		
		@Value("${security.enable-csrf}")
		private boolean csrfEnabled;

		@Value("${oauth.token.validity.hour:2}")
		private Integer tokenValidityHours;
		
		@Autowired
		private DataSource dataSource;
		@Autowired
		private AuthenticationManager authenticationManager;
		@Autowired
		private UserLogic userLogic;
		@Autowired
		private CommonLogic commonLogic;
		@Autowired
		private DaoFactory daoFactory;

		@Bean
		public TokenStore jdbcTokenStore() {
			return new JdbcTokenStore(dataSource);
		}
		
		private final int refreshTokenValidityInSeconds = 7 * 60 * 60 * 24; //7 days

		@Override
		public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
			security.allowFormAuthenticationForClients();
		}
				
		@Override
		public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
			clients.inMemory()
				.withClient("frontend").secret("{noop}")
				.authorizedGrantTypes("password", "refresh_token")
				.authorities("ROLE_APP")
				.scopes("trust");
		}

		@Bean
		public AuthorizationServerTokenServices defaultTokenServices() {
			DefaultTokenServices tokenService = new DefaultTokenServices();
			
			tokenService.setAccessTokenValiditySeconds(tokenValidityHours * 60 * 60);
			tokenService.setRefreshTokenValiditySeconds(refreshTokenValidityInSeconds);
			tokenService.setTokenStore(jdbcTokenStore());
			tokenService.setSupportRefreshToken(true);
			
			return tokenService;
		}
		
		public DefaultTokenServices defaultTokenService() {
			DefaultTokenServices tokenService = new DefaultTokenServices();
			
			tokenService.setAccessTokenValiditySeconds(tokenValidityHours * 60 * 60);
			tokenService.setRefreshTokenValiditySeconds(refreshTokenValidityInSeconds);
			tokenService.setTokenStore(jdbcTokenStore());
			tokenService.setSupportRefreshToken(true);
			
			return tokenService;
		}
		
		@RequestMapping(method = RequestMethod.DELETE, value = "/oauth/token")
		@ResponseBody
		public void revokeToken(HttpServletRequest request) {
			DefaultTokenServices tokenService = defaultTokenService();
			String authorization = request.getHeader("Authorization");
			if (authorization != null && authorization.contains("Bearer")){
	            String tokenId = authorization.substring("Bearer".length()+1);

	            // Get user information from token before revoking it
	            try {
	            	OAuth2Authentication auth = (OAuth2Authentication) tokenService.loadAuthentication(tokenId);
	            	if (auth != null && auth.getUserAuthentication() instanceof EsignAuthenticationToken) {
	            		EsignAuthenticationToken esignToken = (EsignAuthenticationToken) auth.getUserAuthentication();
	            		AmMsuser user = esignToken.getProfile();
	            		String loginId = (String) auth.getPrincipal();

	            		if (user != null) {
	            			Map<String, List<String>> mapTenantRole = esignToken.getMapTenantRole();
	            			if (mapTenantRole != null && !mapTenantRole.isEmpty()) {
	            				String firstTenantCode = mapTenantRole.keySet().iterator().next();

	            				MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(firstTenantCode);
	            				List<AmMsrole> roleList = daoFactory.getRoleDao().getListRoleByIdUserTenantCode(
	            						user.getIdMsUser(), firstTenantCode);

	            				if (tenant != null && !roleList.isEmpty()) {
	            					AmMsrole primaryRole = roleList.get(0);
	            					MsLov activityType = commonLogic.getLovByGroupAndCode(
	            							GlobalVal.LOV_GROUP_ACTIVITY_LOG_TYPE,
	            							GlobalVal.CODE_LOV_ACTIVITY_LOG_TYPE_LOGOUT,
	            							new AuditContext(loginId));

	            					if (activityType != null) {
	            						userLogic.insertUserActivityLog(user, tenant, primaryRole, loginId, activityType, new AuditContext(loginId));
	            					}
	            				}
	            			}
	            		}
	            	}
	            } catch (Exception e) {
	            }

	            // Revoke the token
	            tokenService.revokeToken(tokenId);
	        }
		}
		
		@Override
		public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {						
			endpoints.allowedTokenEndpointRequestMethods(HttpMethod.POST)					
					.tokenServices(defaultTokenServices())
					.authenticationManager(authenticationManager);
		}

		@Override
		public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
			resources.tokenStore(jdbcTokenStore()).stateless(true);
		}
		
	    @Bean
	    public CorsConfigurationSource corsConfigurationSource() {
	        CorsConfiguration configuration = new CorsConfiguration();
	        configuration.setAllowedOrigins(Arrays.asList("*"));
	        configuration.setAllowedMethods(Arrays.asList("*"));
	        configuration.setAllowedHeaders(Arrays.asList("*"));
	        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
	        source.registerCorsConfiguration("/**", configuration);
	        return source;
	    }

		@Override
		public void configure(HttpSecurity http) throws Exception {
			http.antMatcher("/services/**")
					.headers().disable()
					.logout().disable()
					.sessionManagement().disable()
					.authorizeRequests()
					.antMatchers("/services/*/s/**").hasRole("APP")
					.anyRequest().permitAll()
					;
			  
			if (!csrfEnabled) {
				http.csrf().disable();
			}
		}
	}
	
	@Order(10)
	@Configuration
	public static class WebPageSecurityConfiguration extends WebSecurityConfigurerAdapter {
		@Value("${security.enable-csrf}")
		private boolean csrfEnabled;
		@Value("${web.security.max-sessions:1}")
		private int maxSessions;
		@Value("${web.security.max-sessions-prevent-login:true}")
		private boolean maxSessionsPreventLogin;

		@Bean
		public SessionRegistry sessionRegistry() {
			return new SessionRegistryImpl();
		}
		
		@Bean
		public ServletListenerRegistrationBean<HttpSessionListener> httpSessionEventPublisher() {
		    return new ServletListenerRegistrationBean<>(new HttpSessionEventPublisher());
		}		
		
	    @Bean
	    public FilterRegistrationBean<CorsFilter> customCorsFilter() {
	        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
	        CorsConfiguration config = new CorsConfiguration();
	        config.setAllowCredentials(true);
	        config.addAllowedOrigin("*");
	        config.addAllowedHeader("*");
	        config.addAllowedMethod("*");
	        source.registerCorsConfiguration("/**", config);
	        
	        FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));

	        //IMPORTANT #2: I didn't stress enough the importance of this line in my original answer, 
	        //but it's here where we tell Spring to load this filter at the right point in the chain
	        //(with an order of precedence higher than oauth2's filters)
	        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
	        return bean;
	    }
		
		@Override
		protected void configure(HttpSecurity http) throws Exception {		
			http.authorizeRequests()
					.antMatchers("/services").permitAll()
					.antMatchers("/services/confins/**", "/services/callback/**").permitAll()
					.antMatchers("/services/swagger-ui.js", "/services/swagger.json", "/services/api-docs/**", "/services/api-docs**",
							"/services/lib/**", "/services/css/**", "/services/images/**").permitAll() //swagger-ui resources
					.antMatchers("/oauth/token").permitAll()
					.antMatchers("/actuator/**", "/monitoring**").permitAll() //spring actuator, javamelody 
					.anyRequest().authenticated();
		
			if (!csrfEnabled) {
				http.csrf().disable();
			}	
		}	
	}
}
