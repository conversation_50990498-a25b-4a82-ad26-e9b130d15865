package com.adins.esign.webservices.frontend.endpoint;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.api.RoleLogic;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.InvitationLinkLogic;
import com.adins.esign.businesslogic.api.LoginUserLogic;
import com.adins.esign.businesslogic.api.RegistrationLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.GenerateInvLinkRequest;
import com.adins.esign.model.custom.GenerateInvLinkResponse;
import com.adins.esign.model.custom.MenuUserBean;
import com.adins.esign.model.custom.SaveActivationDecryptedResultBean;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.RoleValidatorLogic;
import com.adins.esign.webservices.frontend.api.UserService;
import com.adins.esign.webservices.model.*;
import com.adins.esign.webservices.model.digisign.DigisignRegisterEmbedRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterEmbedRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterResponse;
import com.adins.exceptions.LoginException;
import com.adins.exceptions.LoginException.Reason;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

import io.swagger.annotations.Api;

@Component
@Path("/user")
@Api(value = "UserService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericUserServiceEndpoint implements UserService {
	private static final Logger LOG = LoggerFactory.getLogger(GenericUserServiceEndpoint.class);
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private LoginUserLogic loginUserLogic;
	@Autowired private RegistrationLogic registrationLogic;
	@Autowired private RoleLogic roleLogic;
	@Autowired private UserLogic userLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private RoleValidatorLogic roleValidatorLogic;
	@Autowired private InvitationLinkLogic invitationLinkLogic;
	
	@Override
	@POST
	@Path("/s/profiles")
	public LoginUserResponse getUserProfiles(LoginUserRequest loginRequest) throws LoginException {
		AuditContext auditContext = loginRequest.getAudit().toAuditContext();
		auditContext.setCallerId(loginRequest.getUsername());
		LoginUserResponse response = new LoginUserResponse();

		AmMsuser user;
		boolean checkUserExistence = true;
		if (StringUtils.isNumeric(loginRequest.getUsername())) {
			user = userValidatorLogic.validateGetUserByPhone(loginRequest.getUsername(), checkUserExistence, auditContext);
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(loginRequest.getUsername(), checkUserExistence, auditContext);
		}
		
		if(null == user) {
			throw new LoginException("User not found", Reason.LOGIN_INVALID);
		}
		
		String flagPreRegister = loginUserLogic.checkPreRegister(user.getIdMsUser(), loginRequest.getVendorCode(), auditContext);
		List<AmMsrole> roleList = roleLogic.getListRoleByIdUser(user.getIdMsUser(), auditContext);
		String roleIsEmpty = String.format("User %s role is empty", loginRequest.getUsername());
		
		if(roleList.isEmpty()) {
			throw new LoginException(roleIsEmpty, Reason.LOGIN_INVALID);
		}

		if (!roleList.isEmpty()) {
			AmMsrole primaryRole = roleList.get(0);
			MsTenant tenant = primaryRole.getMsTenant();
			MsLov activityType = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_ACTIVITY_LOG_TYPE, GlobalVal.CODE_LOV_ACTIVITY_LOG_TYPE_LOGIN, auditContext);

			userLogic.insertUserActivityLog(user, tenant, primaryRole, loginRequest.getUsername(), activityType, auditContext);
		}

		response.setFlagPreRegister(flagPreRegister);
		response.setUser(this.mapDataUser(user, roleList, auditContext));
		return response;
	}

	@Override
	@POST
	@Path("/s/menu")
	public MenuResponse getUserMenu(MenuRequest menuRequest) {
		AuditContext auditContext = menuRequest.getAudit().toAuditContext();

		List<MenuUserBean> listMenu = commonLogic.listMenuByRoleAndTenant(
				menuRequest.getRoleCode(), menuRequest.getTenantCode(), auditContext);

		// Insert user activity log for LOGIN after role selection
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(auditContext.getCallerId(), true, auditContext);
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(menuRequest.getTenantCode(), true, auditContext);
		AmMsrole role = roleValidatorLogic.validateGetRole(menuRequest.getTenantCode(), menuRequest.getRoleCode(), true, false, auditContext);
		MsLov activityType = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_ACTIVITY_LOG_TYPE, GlobalVal.CODE_LOV_ACTIVITY_LOG_TYPE_LOGIN, auditContext);

		userLogic.insertUserActivityLog(user, tenant, role, auditContext.getCallerId(), activityType, auditContext);

		MenuResponse menuResponse = new MenuResponse();
		menuResponse.setMenuList(listMenu);
		return menuResponse;
	}
	
	@Override
	@POST
	@Path("/s/getUserData")
	public UserAutoFillDataResponse getUserAutoFillData(UserAutoFillDataRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUserDataRegistrationByLoginId(request, audit);
	}

	@Override
	@POST
	@Path("/s/registerUser")
	public RegistrationResponse doRegisterUser(RegistrationRequest regisRequest) throws IOException {
		AuditContext audit = new AuditContext(regisRequest.getAudit().getCallerId());
		return userLogic.registerUser(regisRequest, audit);

	}
	
	@Override
	@POST
	@Path("/registerUserEmbed")
	public RegistrationResponse doRegisterUserEmbed(RegistrationEmbedRequest regisRequest) throws IOException {
		AuditContext audit = new AuditContext(regisRequest.getAudit().getCallerId());
		return userLogic.registerUserEmbed(regisRequest, audit);

	}
	
	@Override
	@POST
	@Path("/s/actlink")
	public ActivationLinkResponse getActivationLink(ActivationLinkRequest activationLinkRequest) {
		AuditContext audit = new AuditContext(activationLinkRequest.getAudit().getCallerId());
		return userLogic.getActivationLink(activationLinkRequest, audit);
	}
	
	@Override
	@POST
	@Path("/actlinkEmbed")
	public ActivationLinkResponse getActivationLinkEmbed(ActivationLinkEmbedRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return userLogic.getActivationLinkEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/actlinkRegInvDigi")
	public ActivationLinkResponse getActivationLinkInvReg(ActivationLinkEmbedRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return userLogic.getActivationLinkRegInv(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/resendActLinkForUser")
	public ResendActLinkResponse resendActivationLinkForUser(ResendActLinkForUserRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId()); 
		return userLogic.resendActivationLinkForUser(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/resendActLinkForInvi")
	public ResendActLinkResponse resendActivationLinkForInvi(ResendActLinkForInviRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId()); 
		return userLogic.resendActivationLinkForInvi(request, audit);
	}

	private Map<String, Object> mapDataUser(AmMsuser user, List<AmMsrole> userRoles, AuditContext audit) {
		
		List<Map<String, Object>> roleMapList = new ArrayList<>();
		
		for (AmMsrole msRole : userRoles) {
			Map<String, Object> roleMap = new HashMap<>();
			MsTenant tenant = tenantLogic.getTenantById(msRole.getMsTenant().getIdMsTenant(), audit);
			roleMap.put("roleName", msRole.getRoleName());
			roleMap.put("roleCode", msRole.getRoleCode());
			roleMap.put("tenantCode", tenant.getTenantCode());
			roleMap.put("tenantName", tenant.getTenantName());
			
			roleMapList.add(roleMap);
		}
		
		HashMap<String, Object> mapUser = new HashMap<>();
		mapUser.put("loginId", user.getLoginId());
		mapUser.put("fullname", user.getFullName());
		mapUser.put("changePwdLogin", user.getChangePwdLogin());
		mapUser.put("roles", roleMapList);
		return mapUser;

	}

	@Override
	@POST
	@Path("/updateActStatus")
	public SaveUserActivationResponse updateUserActivation(SaveUserActivationRequest request) throws IOException {
		LOG.info("encrypted string: {}", request.getMsg());
		String decrypted = digisignLogic.decryptMessage(request.getMsg(), request.getTenantCode());
		
		Gson gson = new Gson();
		SaveActivationDecryptedResultBean bean = gson.fromJson(decrypted, SaveActivationDecryptedResultBean.class);
		
		return userLogic.updateActivationStatus(bean, request.getAudit().toAuditContext());
	}

	@Override
	@POST
	@Path("/s/list")
	public ListInquiryUserResponse getListUser(ListInquiryUserRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getListInquiryUser(request, audit);
	}

	@Override
	@POST
	@Path("/s/changePassword")
	public ChangePasswordResponse changePassword(ChangePasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.changePassword(request, audit);
	}

	@Override
	@POST
	@Path("/forgotPassword")
	public ForgotPasswordResponse forgotPassword(ForgotPasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.forgotPassword(request, audit);
	}

	@Override
	@POST
	@Path("/resetPassword")
	public ResetPasswordResponse resetPassword(ResetPasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.resetPassword(request, audit);
	}

	@Override
	@POST
	@Path("/s/sendOtpEmail")
	public SendOtpEmailResponse sendOtpEmail(SendOtpEmailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sendOtp(request, audit);
	}
	
	@Override
	@POST
	@Path("/sendOtpEmailEmbed")
	public SendOtpEmailResponse sendOtpEmailEmbed(SendOtpEmailEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sendOtpEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/sendOtpEmailInvitation")
	public SendOtpEmailResponse sendOtpEmailInvitation(SendOtpEmailEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sendOtpInvitation(request, audit);
	}

	@Override
	@POST
	@Path("/s/checkOtpEmail")
	public OtpVerificationResponse verifyOtp(OtpVerificationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyOtp(request, audit);
	}
	
	@Override
	@POST
	@Path("/checkOtpEmailEmbed")
	public OtpVerificationResponse verifyOtpEmbed(OtpVerificationEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyOtpEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/checkOtpEmailInvitation")
	public OtpVerificationResponse verifyOtpInvitation(OtpVerificationEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyOtpInvitation(request, audit);
	}

	@Override
	@POST
	@Path("/checkResetCode")
	public ResetCodeVerificationResponse verifyResetCode(ResetCodeVerificationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyResetCode(request, audit);
	}

	@Override
	@POST
	@Path("/checkActStatus")
	public ActivationStatusResponse getUserActivationStatus(ActivationStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUserActivationStatus(request, audit);
	}

	@Override
	@POST
	@Path("/s/checkEmailService")
	public EmailServiceStatusResponse checkUserEmailService(EmailServiceStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkUserEmailService(request, audit);
	}
	
	@Override
	@POST
	@Path("/checkEmailServiceEmbed")
	public EmailServiceStatusResponse checkUserEmailServiceEmbed(EmailServiceStatusEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkUserEmailServiceEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/preRegister")
	public PreRegisterResponse preRegisterEsignUser(PreRegisterRequest request) throws NoSuchAlgorithmException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.preRegisterEsignUser(request, audit);
	}

	@Override
	@POST
	@Path("/getResetPasswordLink")
	public LinkResetPasswordResponse getResetPasswordLink(LinkResetPasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getResetPasswordLink(request, audit);
	}

	@Override
	@POST
	@Path("/generateInvitationLink")
	public GenerateInvLinkResponse generateAndSendInvitationLink(GenerateInvLinkRequest request) throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.generateAndSendRegInvLink(request, false, audit);
	}

	@Override
	@POST
	@Path("/invitationRegisterData")
	public InvitationRegisterDataResponse checkInvitationCode(InvitationRegisterDataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getInvitationRegisterData(request, audit);
	}

	@Override
	@POST
	@Path("/checkInvitationRegisterStatus")
	public InvitationRegisterStatusResponse checkInvitationRegisterStatus(InvitationRegisterStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkInvitationRegisterStatus(request, audit);
	}
	
	/*
	 * Deprecated karena hanya dipakai untuk lokal dev environment untuk pengambilan file foto dari DB byte array
	 */
	@Deprecated
	@Override
	@POST
	@Path("/getUserPhoto")
	public GetUserPhotoResponse getUserPhoto(GetUserPhotoRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getPhoto(request, audit);
	}

	@Override
	@POST
	@Path("/registerBM")
	public RegistrationResponse registerBM(RegistrationRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.registerBM(request, audit);
	}

	@Override
	@POST
	@Path("/s/sendOTPChangeProfile")
	public SendChangeProfileOTPResponse sendChangeProfileOtpResponse(SendChangeProfileOTPRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sendChangeProfileOtp(request, audit);
	}
	
	@Override
	@POST
	@Path("/changeProfileData")
	public ChangeProfileDataResponse changeUserProfileData(ChangeProfileDataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.changeUserProfileData(request, audit);
	}

	@Override
	@POST
	@Path("/s/verifyOtpChangeProfile")
	public OtpVerifChangeProfileResponse verifyOtpChangeProfile(OtpVerifChangeProfileRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.otpVerifChangeProfile(request, audit);
	}

	@Override
	@POST
	@Path("/updateUserData")
	public UpdateUserDataResponse updateUserData(UpdateUserDataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader("x-api-key");
		
		return userLogic.updateUserData(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/s/resendInvitation")
	public ResendRegisterInvitationResponse resendRegisterInvitation(ResendRegisterInvitationRequest request) throws UnsupportedEncodingException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.resendRegisterInvitation(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getDataInvRegis")
	public DataInvRegisResponse getDataInvRegis(DataInvRegisRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getDataInvRegis(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/updateDataInvRegis")
	public UpdateInvDataResponse updateDataInvRegis(UpdateInvDataRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.updateDataInvRegis(request, audit);
	}

	@Override
	@POST
	@Path("/s/listInvitation")
	public ListInvitationResponse getListInvitation(ListInvitationRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getListInvitation(request, audit);
	}

	@Override
	@POST
	@Path("/s/getInvitationLink")
	public InvitationLinkResponse getInvitationLink(InvitationLinkRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getInvitationLink(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/listInvitationReport")
	public ListInvitationExcelReportResponse exportInvitationListReport(ListInvitationExcelReportRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.exportListInvitationReport(request, audit);
	}

	@Override
	@POST
	@Path("/s/getProfile")
	public MyProfileResponse getMyProfile(MyProfileRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getMyProfile(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/updateDataUser")
	public UpdateUserResponse updateUser(UpdateUserRequest request) throws ParseException, IOException {
		// Edit Pencarian Pengguna
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.updateUser(request, audit);
	}

	@Override
	@POST
	@Path("/s/inquiryEditUser")
	public InquiryEditUserResponse getInquiryEditUser(InquiryEditUserRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getInquiryEditUser(request, audit);
	}

	@Override
	@POST
	@Path("/createEmail")
	public CreateEmailResponse createEmail(CreateEmailRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.createEmail(request, audit);
	}

	//return link activation TekenAja
	@Override
	@POST
	@Path("/returnlinkAktivasiTekenAja")
	public ReturnlinkAktivasiTekenAjaResponse returnLinkAktivasi(ReturnlinkAktivasiTekenAjaRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkKodeEncryptValid(request, audit);
	}

	@Override
	@POST
	@Path("/s/registerTekenAja")
	public TekenAjaRegisterResponse registerTekenAja(TekenAjaRegisterRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.registerNormalTekenAja(request, audit);
	}

	@Override
	@POST
	@Path("/registerEmbedTekenAja")
	public RegisterResponse registerEmbedTekenAja(TekenAjaRegisterEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.registerEmbedTekenAja(request, audit);
	}

	@Override
	@POST
	@Path("/s/generateInvitationLinkSecured")
	public GenerateInvLinkResponse generateAndSendInvitationLinkSecured(GenerateInvLinkRequest request)
			throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.generateAndSendRegInvLink(request, true, audit);
	}
	
	@Override
	@POST
	@Path("/s/generateInvitationLinkSecuredV2")
	public GenerateInvLinkResponse generateAndSendInvitationLinkSecuredV2(GenerateInvLinkRequest request)
			throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.generateAndSendRegInvLinkV2(request, true, audit);
	}

	@Override
	@POST
	@Path("/s/resetOtpCode")
	public ResetOtpCodeResponse resetOtpCode(ResetOtpCodeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.resetOtpCode(request, audit);
	}

	@Override
	@POST
	@Path("/updateReregistration")
	public UpdateReregisResponse updateReregistrationUser(UpdateReregisRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.updateReregister(request, audit);
	}

	@Override
	@POST
	@Path("/generateInvitationLinkV2")
	public GenerateInvLinkResponse generateAndSendInvitationLinkV2(GenerateInvLinkRequest request)
			throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.generateAndSendRegInvLinkV2(request, false, audit);
	}
	
	@Override
	@POST
	@Path("/getUserActivationStatusResetPassword")
	public ActivationStatusResetPasswordResponse getUserActivationStatusResetPassword(ActivationStatusResetPasswordRequest request){
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUserActivationStatusForgetPassword(request, audit);
	}
	
	@Override
	@POST
	@Path("/registerByInvitation")
	public RegisterResponse registerByInvitation(RegistrationByInvitationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return registrationLogic.registerByInvitation(request, audit);
	}

	@Override
	@POST
	@Path("/registerUserEmbedV2")
	public RegisterResponse registerEmbedV2(DigisignRegisterEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.registerEmbedDigisign(request, audit);
	}

	@Override
	@POST
	@Path("/updateReregisEmbed")
	public UpdateReregisResponse updateReregistrationUserEmbed(UpdateReregisRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.updateReregisterEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/updateActStatusV2")
	public SaveUserActivationResponse updateUserActivationV2(SaveUserActivationRequest request) throws IOException {
		LOG.info("encrypted string: {}", request.getMsg());
		String decrypted = digisignLogic.decryptMessage(request.getMsg(), request.getTenantCode());
		
		Gson gson = new Gson();
		SaveActivationDecryptedResultBean bean = gson.fromJson(decrypted, SaveActivationDecryptedResultBean.class);
		
		return userLogic.updateActivationStatusV2(bean,request.getTenantCode() ,request.getAudit().toAuditContext());
	}

	@Override
	@POST
	@Path("/s/checkRegisterStatus")
	public CheckRegisterStatusResponse checkRegisterStatus(CheckRegisterStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkRegisterStatus(request, audit);
	}

	@Override
	@POST
	@Path("/s/checkRegisterAutoFill")
	public CheckRegisterAutoFillResponse checkRegisterAutoFill(CheckRegisterAutoFillRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkRegisterAutoFill(request, audit);
	}
	
	@Override
	@POST
	@Path("/getAllUserData")
	public GetAllUserResponse getAllUserData() {
		return userLogic.getAllUserData();
	}

	@Override
	@POST
	@Path("/s/updateDataSigner")
	public UpdateDataUserResponse updateDataUser(UpdateDataUserRequest request) {
		// Edit Data Signer
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.updateDataUser(request, audit);
	}

	@Override
	@POST
	@Path("/s/editActivationStatus")
	public EditActivationStatusResponse editActivationStatus(EditActivationStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		
		// Set IP address from header x-real-ip
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xRealIp = httpRequest.getHeader(HttpHeaders.X_REAL_IP);
		if (StringUtils.isNotBlank(xRealIp)) {
			request.setIpAddress(xRealIp);
		}
		
		return userLogic.editActivationStatus(request, audit);
	}

	@Override
	@POST
	@Path("/checkPasswordComplexity")
	public CheckPasswordComplexityResponse checkPasswordComplexity(CheckPasswordComplexityRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkPasswordComplexity(request, audit);
	}

	@Override
	@POST
	@Path("/s/getSignerDetail")
	public GetSignerDetailResponse getSignerDetail(GetSignerDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getSignerDetail(request, audit);
	}

	@Override
	@POST
	@Path("/getUserActData")
	public GetUserActDataResponse getUserActData(GetUserActDataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUserActData(request, audit);
	}

	@Override
	@POST
	@Path("/s/signerDataVerification")
	public SignerDataVerificationResponse signerDataVerification(SignerDataVerificationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.signerDataVerification(request, audit);
	}

	@Override
	@POST
	@Path("/sentOtpActivationUser")
	public SentOtpActivationUserResponse sentOtpActivationUser(SentOtpActivationUserRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sentOtpActivationUser(request, audit);
	}

	@Override
	@POST
	@Path("/verifyOtpActivationUser")
	public VerifyOtpActivationUserResponse verifyOtpActivationUser(VerifyOtpActivationUserRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyOtpActivationUser(request, audit);
	}

	@Override
	@POST
	@Path("/s/sentOtpSigningVerification")
	public SentOtpSigningVerificationResponse sentOtpSigningVerification(SentOtpSigningVerificationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sentOtpSigningVerification(request, audit);
	}

	@Override
	@POST
	@Path("/s/verifyOtpSigningVerification")
	public VerifyOtpSigningVerificationResponse verifyOtpSigningVerification(VerifyOtpSigningVerificationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyOtpSigningVerification(request, audit);
	}

	@Override
	@POST
	@Path("/updateActivationUser")
	public UpdateActivationUserResponse updateActivationUser(UpdateActivationUserRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.updateActivationUser(request, audit);
	}

	@Override
	@POST
	@Path("/s/verifyLivenessFaceCompare")
	public VerifyLivenessFaceCompareResponse verifyLivenessFaceCompare(VerifyLivenessFaceCompareRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyLivenessFaceCompare(request, audit);
	}

	@Override
	@POST
	@Path("/s/getSignerDetailWebview")
	public GetSignerDetailWebviewResponse getSignerDetailWebview(GetSignerDetailWebviewRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getSignerDetailWebview(request, audit);
	}

	@Override
	@POST
	@Path("/s/regenerateInvitation")
	public RegenerateInvitationResponse regenerateInvitation(RegenerateInvitationRequest request) throws MessagingException, IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.regenerateInvitation(request, audit);
	}

	@Override
	@POST
	@Path("/getUrlForwarder")
	public GetUrlForwarderResponse getUrlForwarder(GetUrlForwarderRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUrlForwarder(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListUserViewOtp")
	public ListUserViewOtpResponse getListUserViewOtp(ListUserViewOtpRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getListUserViewOtp(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/viewOtp")
	public ViewOtpResponse viewOtp(ViewOtpRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.viewOtp(request, audit);
	}

	@Override
	@POST
	@Path("/s/viewResetCode")
	public ViewResetCodeResponse viewResetCode(ViewResetCodeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.viewResetCode(request, audit);
	}

	@Override
	@POST
	@Path("/s/generateInvitationLinkECertExpired")
	public GenerateInvitationLinkForExpiredCertResponse generateInvLinkECertExpired(
			GenerateInvitationLinkForExpiredCertRequest request) throws UnsupportedEncodingException {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.generateInvLinkExpiredCert(request, audit);
	}

	@Override
	@POST
	@Path("/s/decryptUserData")
	public decryptUserDataResponse decryptUserData(decryptUserDataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.decryptKTP(request, audit);
	}
}
