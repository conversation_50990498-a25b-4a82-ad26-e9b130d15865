package com.adins.esign.businesslogic.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;
import javax.mail.MessagingException;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsUserHistory;
import com.adins.am.model.AmMsdistrict;
import com.adins.am.model.AmMsprovince;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMssubdistrict;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.am.model.AmUserPersonalDataHistory;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.CallbackLogic;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.EmailLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.businesslogic.api.InvitationLinkLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.SmsOtpLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.businesslogic.impl.interfacing.GenericDigisignLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.TekenAjaConstant;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.job.QueuePublisher;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsNotificationtypeoftenant;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendorRegisteredUserHistory;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.TrJobCheckRegisterStatus;
import com.adins.esign.model.TrOtp;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.TrReregistrationUser;
import com.adins.esign.model.TrSignLinkRequest;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.TrUrlForwarder;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.model.TrUserActivityLog;
import com.adins.esign.model.custom.ActivationDigisignResponseBean;
import com.adins.esign.model.custom.ActivationDocumentBean;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiRequest;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiResponse;
import com.adins.esign.model.custom.ChangeEmailPhoneRequestBean;
import com.adins.esign.model.custom.CheckDigiCertExpDateResponse;
import com.adins.esign.model.custom.CheckRegistrationBean;
import com.adins.esign.model.custom.DataUserOptionalBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.GenerateInvLinkRequest;
import com.adins.esign.model.custom.GenerateInvLinkUserBean;
import com.adins.esign.model.custom.InquiryEditUserBean;
import com.adins.esign.model.custom.InquiryUserBean;
import com.adins.esign.model.custom.InvLinkBean;
import com.adins.esign.model.custom.InvitationReportBean;
import com.adins.esign.model.custom.LovBean;
import com.adins.esign.model.custom.MyProfileBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.RegisterDigisignResponseBean;
import com.adins.esign.model.custom.RegisterDigisignResponseDataBean;
import com.adins.esign.model.custom.SaveActivationDecryptedResultBean;
import com.adins.esign.model.custom.SignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.UserDecryptedBean;
import com.adins.esign.model.custom.UserViewOtpBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsResponse;
import com.adins.esign.model.custom.tekenaja.TekenAjaUserBean;
import com.adins.esign.model.queuebean.UpdErrHistRerunProcessBean;
import com.adins.esign.model.custom.InvitationBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.DistrictValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.validatorlogic.api.ProvinceValidatorLogic;
import com.adins.esign.validatorlogic.api.SignLinkValidatorLogic;
import com.adins.esign.validatorlogic.api.SubdistrictValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.*;
import com.adins.esign.webservices.model.digisign.DigisignRegisterEmbedRequest;
import com.adins.esign.webservices.model.digisign.DigisignRegisterRequest;
import com.adins.esign.webservices.model.external.DownloadUserCertificateRequest;
import com.adins.esign.webservices.model.external.DownloadUserCertificateResponse;
import com.adins.esign.webservices.model.privy.PrivyLivenessUrlResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpRequestSigningResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpValidationResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaDownloadCertificateResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterApiResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterEmbedRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterResponse;
import com.adins.exceptions.ChangePasswordException;
import com.adins.exceptions.ChangePasswordException.Reason;
import com.adins.exceptions.CheckPasswordComplexityException.ReasonCheckPasswordComplexity;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.CheckPasswordComplexityException;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.FormatException;
import com.adins.exceptions.FormatException.ReasonFormat;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.PrivyException;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.UserManagementException;
import com.adins.exceptions.UserManagementException.ReasonUserManagement;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.exceptions.DigisignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.ServicesUserException;
import com.adins.exceptions.SignConfirmationDocumentException;
import com.adins.exceptions.SignConfirmationDocumentException.ReasonSignConfirmationDokumen;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TekenajaException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.VidaException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.util.GenerateSignPicture;
import com.adins.util.ZipCompressionUtils;
import com.google.gson.Gson;

@Component
public class GenericUserLogic extends BaseLogic implements UserLogic {

	@Autowired private Gson gson;
// Test oleh howen
	// Business logic
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private MessageTemplateLogic msgTemplateLogic;
	@Autowired private SmsLogic smsLogic;
	@Autowired private SmsOtpLogic smsOtpLogic;
	@Autowired private EmailLogic emailLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private ExcelLogic excelLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private InvitationLinkLogic invLinkLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired private CallbackLogic callbackLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	
	// PSrE / Vendor Logic
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private TekenAjaLogic tekenAjaLogic;
	@Autowired private PrivyLogic privyLogic;
	@Autowired private PrivyGeneralLogic privyGeneralLogic;

	// Validator logic
	@Autowired private DistrictValidatorLogic districtValidatorLogic;
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private ProvinceValidatorLogic provinceValidatorLogic;
	@Autowired private SubdistrictValidatorLogic subdistrictValidatorLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private SignLinkValidatorLogic signLinkValidatorLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncryptionLogic;
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericUserLogic.class);
	private static final String ACTIVE = "Active";
	private static final String ACTIVE_DASH = "Active - ";
	private static final String NOT_ACTIVE = "Not Active";
	private static final String INACTIVE = "Inactive";
	private static final String FULLNAME = "fullname";
	private static final String VFIRST_ERR28681 = "28681";
	private static final String VFIRST_ERR28682 = "28682";
	private static final String VFIRST_ERR408 = "408";

	private static final String REGISTER_BY_INVITATION_PASSWORD = "newInv";

	private static final String MSG_NO_UPDATE = "businesslogic.user.noupdate";
	private static final String MSG_VENDORCODE_INVALID = "businesslogic.vendor.vendorcodeinvalid";
	private static final String MSG_VENDOR_CANNOT_SEND_PHONE = "businesslogic.vendor.cannotsendphone";
	private static final String MSG_DIGI_REGISTERED_NOTACTIVATED = "businesslogic.digisign.registerednotactivated";
	private static final String MSG_USER_PHONE_USEDBYOTHER = "businesslogic.user.phoneusedbyother";
	
	private static final String ACT_STATUS_NOT_REGIS = "Belum Registrasi";
	private static final String ACT_STATUS_NOT_ACT = "Belum Aktivasi";
	private static final String ACT_STATUS_HAS_ACT = "Sudah Aktivasi";
	private static final String TENANT_NAME = "tenantName";
	private static final String CALLBACK_ACTIVATION_COMPLETE_MESSAGE = "Success";
	private static final String CONST_SEND_OTP_FORGOT_PASS_ESIGN = "%s : Send OTP SMS Forgot Password Esign";
	private static final String CONST_TENANT	= "Tenant";
	private static final String CONST_EMAIL	= "Email";
	private static final String CONST_OFF	= "(off)";
	private static final String CONST_INSERT_VERIFICATION_BALMUT_SKIP = "Insert verification balance mutation skipped.";
	private static final String CONST_SIGNING_LIVENESS_FAILED		 = "Signing Liveness Failed";
	private static final String CONST_SIGNING_FACE_COMPARE_FAILED	 = "Signing Face Compare Failed";
	private static final String CONST_VENDOR = "Vendor";
	private static final String CONST_CODE_OTP = "Code OTP";
	private static final String CONST_USER_NOT_FOUND = "User tidak ditemukan!";
	private static final String CONST_INVITE = "invite";
	private static final String CONST_SENDING_POINT_OPTION = "Sending Point Option";
	
	private String headerContentType = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
	private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;

	@Value("${spring.mail.username}") private String fromEmailAddr;
	@Value("${regist.inv.url}") private String regInvLink;
	@Value("${base.esg.url}") private String baseEsignUrl;
	@Value("${user.photo.path}") private String resultPhotoPath;
	@Value("${esign.login.uri}") private String loginUrl;
	@Value("${esign.regex.phone}") private String regexPhone;
	@Value("${esign.regex.email}") private String regexEmail;
	@Value("${verification.url}") private String urlVerification;
	@Value("${verification.liveness.facecompare.url}") private String urlLivenessFacecompare;
	@Value("${link.embed.dashboard}") private String embedDashboardUrl;
	

	private String buildVendorActivationStatus(MsVendorRegisteredUser registeredUser) {
		return labelRegistrationStatus(registeredUser) + " - " + registeredUser.getMsVendor().getVendorName();
	}

	private String buildVendorAutosignStatus(MsVendorRegisteredUser registeredUser) {
		return (null != registeredUser.getVendorUserAutosignKey() ? ACTIVE : INACTIVE) + " - "
				+ registeredUser.getMsVendor().getVendorName();
	}
	
	private String buildVendorCertExpiredDate(MsVendorRegisteredUser registeredUser) {
		if (null == registeredUser.getCertExpiredDate()) {
			return StringUtils.EMPTY;
		}
		
		return MssTool.formatDateToStringIn(registeredUser.getCertExpiredDate(), GlobalVal.DATE_FORMAT_MON_IN);
	}
	
	private String buildVendorCertPoaExpiredDate(MsVendorRegisteredUser registeredUser) {
		if (null == registeredUser.getCertPoaExpiredDate()) {
			return StringUtils.EMPTY;
		}
		
		return MssTool.formatDateToStringIn(registeredUser.getCertPoaExpiredDate(), GlobalVal.DATE_FORMAT_MON_IN);
	}

	@Override
	public UserAutoFillDataResponse getUserDataRegistrationByLoginId(UserAutoFillDataRequest request, AuditContext audit) throws IOException {
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		String loginId = request.getUserData().getLoginId();
		userValidatorLogic.validateGetUserDataCaller(loginId, tenant.getTenantCode(), audit);
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(loginId, true, audit);

		PersonalDataBean personalDataBean = null;
		if ("1".equals(request.getIsPhoto())) {
			personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		} else {
			personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		}
		
		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());

		UserBean registrationData = new UserBean();
		registrationData.setLoginId(registeredUser.getSignerRegisteredEmail());
		registrationData.setUserName(user.getFullName());
		registrationData.setStatus(buildVendorActivationStatus(registeredUser));
		registrationData.setAutosignStatus(buildVendorAutosignStatus(registeredUser));
		
		registrationData.setCertPoaExp(buildVendorCertPoaExpiredDate(registeredUser));
		
		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(registeredUser.getMsVendor().getVendorCode())) {
			try {
				MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, registeredUser.getMsVendor());
				CheckDigiCertExpDateResponse response = digisignLogic.checkCertExpDate(registeredUser.getSignerRegisteredEmail(), vot, audit);
				registrationData.setCertExp(this.buildCertExpired(response));
			} catch (Exception e) {
				registrationData.setCertExp(buildVendorCertExpiredDate(registeredUser));
			}
		} else {
			registrationData.setCertExp(buildVendorCertExpiredDate(registeredUser));
		}
		
		registrationData.setEmail(registeredUser.getSignerRegisteredEmail());
		registrationData.setUserPob(personalDataBean.getUserPersonalData().getPlaceOfBirth());
		registrationData.setUserGender(personalDataBean.getUserPersonalData().getGender());
		registrationData.setSelfPhoto((null == personalDataBean.getSelfPhotoRaw()) ? null : Base64.getEncoder().encodeToString(personalDataBean.getSelfPhotoRaw()));
		registrationData.setUserDob(MssTool.formatDateToStringIn(personalDataBean.getUserPersonalData().getDateOfBirth(), GlobalVal.DATE_FORMAT));

		// zipcode
		if (null != personalDataBean.getUserPersonalData().getZipcodeBean()) {
			registrationData.setProvinsi(personalDataBean.getUserPersonalData().getZipcodeBean().getProvinsi());
			registrationData.setKecamatan(personalDataBean.getUserPersonalData().getZipcodeBean().getKecamatan());
			registrationData.setKelurahan(personalDataBean.getUserPersonalData().getZipcodeBean().getKelurahan());
			registrationData.setKota(personalDataBean.getUserPersonalData().getZipcodeBean().getKota());
			registrationData.setZipcode(personalDataBean.getUserPersonalData().getZipcodeBean().getZipcode());
		}

		// from decrypt
		registrationData.setUserPhone(personalDataEncLogic.decryptToString(registeredUser.getPhoneBytea()));
		registrationData.setIdNo(personalDataBean.getIdNoRaw());
		registrationData.setUserAddress(personalDataBean.getAddressRaw());
		registrationData.setIdPhoto((null == personalDataBean.getPhotoIdRaw()) ? null : Base64.getEncoder().encodeToString(personalDataBean.getPhotoIdRaw()));
		
		//certificateStatus
		String certificateStatus = ACTIVE;

		if (null == registeredUser.getCertExpiredDate()) {
			certificateStatus = NOT_ACTIVE;
		}
		
		if (null != registeredUser.getCertExpiredDate() && GlobalVal.VENDOR_CODE_VIDA.equals(registeredUser.getMsVendor().getVendorCode()) && userValidatorLogic.isCertifExpiredForInquiry(registeredUser)) {
			certificateStatus = GlobalVal.CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS;
		}
		
		registrationData.setCertificateStatus(certificateStatus);
		
		//certificatePoaStatus
		String certificatePoaStatus = ACTIVE;

		if (null == registeredUser.getCertPoaExpiredDate()) {
			certificatePoaStatus = NOT_ACTIVE;
		}
		
		if (null != registeredUser.getCertPoaExpiredDate() && userValidatorLogic.isCertifPoaExpiredForInquiry(registeredUser)) {
			certificatePoaStatus = GlobalVal.CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS;
		}
		
		registrationData.setCertificateStatus(certificateStatus);
		registrationData.setCertificatePoaStatus(certificatePoaStatus);
		
		UserAutoFillDataResponse response = new UserAutoFillDataResponse();
		response.setVendorCode(vendor.getVendorCode());
		response.setUserData(registrationData);
		return response;
	}

	private String buildCertExpired(CheckDigiCertExpDateResponse response) {
		StringBuilder msg = new StringBuilder();

		if ("expired".equals(response.getInfo())) {
			msg.append("Expired - ");
		} else if ("certificate not found".equals(response.getInfo())) {
			return INACTIVE;
		} else {
			msg.append(ACTIVE_DASH);
		}

		String sExpDate = "";
		if (StringUtils.isNotBlank(response.getExpiredTime())) {
			Date expDate = MssTool.formatStringToDate(response.getExpiredTime(), "yyyy-MM-dd'T'HH:mm:ss'+07:00'");
			sExpDate = MssTool.formatDateToStringIn(expDate, "dd-MMM-yyyy");
		}

		msg.append(sExpDate);
		return msg.toString();
	}

	private void prepareDataRegister(RegistrationRequest request, AuditContext audit) {
		// LOV MALE/FEMALE
		if (GlobalVal.CODE_LOV_MALE.equals(request.getUserData().getUserGender())) {
			request.getUserData().setUserGender(GlobalVal.CODE_DIGISIGN_MALE);
		} else if (GlobalVal.CODE_LOV_FEMALE.equals(request.getUserData().getUserGender())) {
			request.getUserData().setUserGender(GlobalVal.CODE_DIGISIGN_FEMALE);
		} else {
			throw new DigisignException(this.messageSource.getMessage("businesslogic.user.invalidparam",
					new Object[] { "Jenis Kelamin" }, this.retrieveLocaleAudit(audit)));
		}

		// DATE OF BIRTH (Format ke dd-MM-yyyy)
		Date userDob = MssTool.formatStringToDate(request.getUserData().getUserDob(), GlobalVal.DATE_FORMAT);
		String userDobDigisign = MssTool.formatDateToStringIn(userDob, GlobalVal.DATE_FORMAT_DASH_IN);
		request.getUserData().setUserDob(userDobDigisign);

		// EMAIL ADMIN
		MsVendoroftenant vendorTenant = vendorLogic.getVendorTenantByCode(request.getTenantCode(),
				request.getVendorCode(), audit);
		request.getUserData().setAdminEmail(vendorTenant.getEmailPartner());

		// HASIL GENERATE GAMBAR TTD
		GenerateSignPicture generateSignPic = new GenerateSignPicture();
		byte[] ttdPic = generateSignPic.generateSignatureSpecimen(request.getUserData().getUserName());
		String ttdBase64 = Base64.getEncoder().encodeToString(ttdPic);
		request.getUserData().setTtd(ttdBase64);
	}

	private void prepareDataRegisterV2(DigisignRegisterRequest request, AuditContext audit) {
		// LOV MALE/FEMALE
		if (GlobalVal.CODE_LOV_MALE.equals(request.getUserData().getUserGender())) {
			request.getUserData().setUserGender(GlobalVal.CODE_DIGISIGN_MALE);
		} else if (GlobalVal.CODE_LOV_FEMALE.equals(request.getUserData().getUserGender())) {
			request.getUserData().setUserGender(GlobalVal.CODE_DIGISIGN_FEMALE);
		} else {
			throw new DigisignException(this.messageSource.getMessage("businesslogic.user.invalidparam",
					new Object[] { "Jenis Kelamin" }, this.retrieveLocaleAudit(audit)));
		}

		// DATE OF BIRTH (Format ke dd-MM-yyyy)
		Date userDob = MssTool.formatStringToDate(request.getUserData().getUserDob(), GlobalVal.DATE_FORMAT);
		String userDobDigisign = MssTool.formatDateToStringIn(userDob, GlobalVal.DATE_FORMAT_DASH_IN);
		request.getUserData().setUserDob(userDobDigisign);

		// EMAIL ADMIN
		MsVendoroftenant vendorTenant = vendorLogic.getVendorTenantByCode(request.getTenantCode(),
				request.getVendorCode(), audit);
		request.getUserData().setAdminEmail(vendorTenant.getEmailPartner());

		// HASIL GENERATE GAMBAR TTD
		GenerateSignPicture generateSignPic = new GenerateSignPicture();
		byte[] ttdPic = generateSignPic.generateSignatureSpecimen(request.getUserData().getUserName());
		String ttdBase64 = Base64.getEncoder().encodeToString(ttdPic);
		request.getUserData().setTtd(ttdBase64);
	}

	@Override
	public UserBean prepareDataActivationLink(ActivationLinkRequest activationLinkRequest, AuditContext audit) {
		MsTenant tenant = tenantLogic.getTenantByCode(activationLinkRequest.getTenantCode(), audit);
		MsVendor vendor = vendorLogic.getVendorByCode(activationLinkRequest.getVendorCode(), audit);
		MsVendoroftenant vendorTenant = vendorLogic.getVendorTenant(tenant, vendor, audit);

		UserBean userBean = new UserBean();
		userBean.setEmail(activationLinkRequest.getEmail());
		userBean.setAdminEmail(vendorTenant.getEmailPartner());
		userBean.setToken(vendorTenant.getToken());

		return userBean;
	}

	@Override
	public String generateRandomPassword() {
		
		String upperCaseLetters = MssTool.generateRandomCharacters(GlobalVal.UPPER_CHRS, 2);
		String totalChars = MssTool.generateRandomCharacters(GlobalVal.CHRS, 2); // random characters (lowercase, uppercase, or number)
		String lowerCaseLetters = MssTool.generateRandomCharacters(GlobalVal.LOWER_CHRS, 2);
		String numbers = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 2);
			
		String combinedChars = upperCaseLetters.concat(lowerCaseLetters).concat(numbers).concat(totalChars);
		List<Character> pwdChars = combinedChars.chars().mapToObj(c -> (char) c).collect(Collectors.toList());
		Collections.shuffle(pwdChars);
		return pwdChars.stream().collect(StringBuilder::new, StringBuilder::append, StringBuilder::append).toString();
	}

	@Override
	public AmMsuser getUserByLoginId(String loginId) {
		return daoFactory.getUserDao().getUserByLoginId(loginId);
	}

	@Override
	public void insertBalanceMutationVerif(String vendorCode, String tenantCode, String loginId, AuditContext audit) {
		MsLov balanceTypeVrf = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxTypeVrf = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(tenantCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(loginId);
		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		Date trxDate = new Date();
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String refNo = StringUtils.EMPTY;
		int qty = -1;
		String notes = personalDataBean.getPhoneRaw();

		saldoLogic.insertBalanceMutation(null, null, null, balanceTypeVrf, trxTypeVrf, tenant, vendor, trxDate, refNo,
				qty, String.valueOf(nextTrxNo), user, notes, null, audit);
	}

	@Override
	public void insertPasswordHist(AmMsuser newUser, AuditContext audit) {
		AmUserpwdhistory pwdHist = new AmUserpwdhistory();
		pwdHist.setAmMsuser(newUser);
		pwdHist.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		pwdHist.setDtmCrt(new Date());
		pwdHist.setPassword(newUser.getPassword());
		pwdHist.setMsLov(daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE,
				GlobalVal.CODE_LOV_PWD_CHANGE_TYPE_NEW));
		daoFactory.getUserDao().insertUserPwdhistory(pwdHist);
	}

	@Override
	public void insertRoleNewUser(AmMsuser newUser, AmMsrole role, AuditContext audit) {
		AmMemberofrole memberOfRole = new AmMemberofrole();
		memberOfRole.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		memberOfRole.setDtmCrt(new Date());
		memberOfRole.setAmMsuser(newUser);

		memberOfRole.setAmMsrole(role);
		daoFactory.getRoleDao().insertMemberOfRole(memberOfRole);
	}

	@Override
	public void insertNewUserPersonalData(AmMsuser newUser, SignerBean signerBean, AuditContext audit) {
		ZipcodeCityBean zipcodeBean = new ZipcodeCityBean();
		zipcodeBean.setProvinsi(StringUtils.upperCase(signerBean.getProvinsi()));
		zipcodeBean.setKelurahan(StringUtils.upperCase(signerBean.getKelurahan()));
		zipcodeBean.setKecamatan(StringUtils.upperCase(signerBean.getKecamatan()));
		zipcodeBean.setKota(StringUtils.upperCase(signerBean.getKota()));
		zipcodeBean.setZipcode(StringUtils.upperCase(signerBean.getZipcode()));

		PersonalDataBean personalDataBean = new PersonalDataBean();
		AmUserPersonalData newUserPersonalData = new AmUserPersonalData();
		newUserPersonalData.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		newUserPersonalData.setDtmCrt(new Date());
		newUserPersonalData.setAmMsuser(newUser);
		newUserPersonalData.setGender(StringUtils.upperCase(signerBean.getUserGender()));
		newUserPersonalData.setZipcodeBean(zipcodeBean);
		newUserPersonalData.setPlaceOfBirth(StringUtils.upperCase(signerBean.getUserPob()));
		newUserPersonalData.setDateOfBirth(MssTool.formatStringToDate(signerBean.getUserDob(), GlobalVal.DATE_FORMAT));
		newUserPersonalData.setEmail(StringUtils.upperCase(signerBean.getEmail()));

		if (StringUtils.isNotBlank(signerBean.getSignerSelfPhoto())) {
			byte[] selfPhotoByteArray = MssTool.imageStringToByteArray(signerBean.getSignerSelfPhoto());
			personalDataBean.setSelfPhotoRaw(selfPhotoByteArray);
		}

		personalDataBean.setUserPersonalData(newUserPersonalData);

		// to be encrypt
		personalDataBean.setAddressRaw(StringUtils.upperCase(signerBean.getUserAddress()));
		if (StringUtils.isNotBlank(signerBean.getIdPhoto())) {
			byte[] idPhoto = MssTool.imageStringToByteArray(signerBean.getIdPhoto());
			personalDataBean.setPhotoIdRaw(idPhoto);
		}

		personalDataBean.setIdNoRaw(StringUtils.upperCase(signerBean.getIdNo()));
		personalDataBean.setPhoneRaw(signerBean.getUserPhone());

		daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
	}

	@Override
	public void insertNewUser(AmMsuser newUser, SignerBean signerBean, String randomPassword, MsOffice office,
			AuditContext audit) {
		newUser.setIsActive("1");
		newUser.setIsDeleted("0");
		newUser.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		newUser.setDtmCrt(new Date());
		newUser.setLoginId(StringUtils.upperCase(signerBean.getLoginId()));
		newUser.setFullName(StringUtils.upperCase(signerBean.getUserName()));
		String[] separated = signerBean.getUserName().split(" ");
		newUser.setInitialName(StringUtils.upperCase(separated[0]));
		newUser.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
		newUser.setPassword(PasswordHash.createHash(randomPassword));
		newUser.setChangePwdLogin("1");
		newUser.setFailCount(0);
		newUser.setIsLoggedIn("0");
		newUser.setIsLocked("0");
		newUser.setIsDormant("0");
		newUser.setMsOffice(office);
		newUser.setEmailService(signerBean.getEmailService());
		newUser.setMsEmailHosting(daoFactory.getEmailDao().getEmailHostingById(signerBean.getIdEmailHosting()));

		if (StringUtils.isNotBlank(signerBean.getIdNo())) {
			String idKtp = MssTool.getHashedString(signerBean.getIdNo());
			newUser.setHashedIdNo(idKtp); // save as hashed version
		}

		if (StringUtils.isNotBlank(signerBean.getUserPhone())) {
			String phone = MssTool.getHashedString(signerBean.getUserPhone());
			newUser.setHashedPhone(phone); // save as hashed version
		}
		
		AmMsuser userNIK = daoFactory.getUserDao().getUserByIdNo(signerBean.getIdNo());
		
		
		if (null == userNIK) {
			daoFactory.getUserDao().insertUser(newUser);
		}
		else {
			newUser.setIdMsUser(userNIK.getIdMsUser());
		}
		
	}

	private void insertNewUserInv(AmMsuser newUser, SignerBean signerBean, MsOffice office, AuditContext audit) {
		newUser.setIsActive("1");
		newUser.setIsDeleted("0");
		newUser.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		newUser.setDtmCrt(new Date());
		newUser.setLoginId(StringUtils.upperCase(signerBean.getLoginId()));
		newUser.setFullName(StringUtils.upperCase(signerBean.getUserName()));
		String[] separated = signerBean.getUserName().split(" ");
		newUser.setInitialName(StringUtils.upperCase(separated[0]));
		newUser.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
		newUser.setPassword(REGISTER_BY_INVITATION_PASSWORD);
		newUser.setChangePwdLogin("1");
		newUser.setFailCount(0);
		newUser.setIsLoggedIn("0");
		newUser.setIsLocked("0");
		newUser.setIsDormant("0");
		newUser.setMsOffice(office);
		newUser.setEmailService(signerBean.getEmailService());
		newUser.setMsEmailHosting(daoFactory.getEmailDao().getEmailHostingById(signerBean.getIdEmailHosting()));

		if (StringUtils.isNotBlank(signerBean.getIdNo())) {
			String idKtp = MssTool.getHashedString(signerBean.getIdNo());
			newUser.setHashedIdNo(idKtp); // save as hashed version
		}

		if (StringUtils.isNotBlank(signerBean.getUserPhone())) {
			String phone = MssTool.getHashedString(signerBean.getUserPhone());
			newUser.setHashedPhone(phone); // save as hashed version
		}

		daoFactory.getUserDao().insertUser(newUser);
	}

	@Override
	public SaveUserActivationResponse updateActivationStatus(SaveActivationDecryptedResultBean decryptedBean,
			AuditContext audit) throws IOException {
		SaveUserActivationResponse response = new SaveUserActivationResponse();
		Status status = new Status();
		if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(decryptedBean.getResult())) {
			status.setMessage(decryptedBean.getNotif());
			response.setStatus(status);
			return response;
		}
		AmMsuser user;
		boolean checkUserExistence = true;

		user = userValidatorLogic.validateGetUserByEmailv2(decryptedBean.getEmailUser(), checkUserExistence, audit);

		MsVendorRegisteredUser vRUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), GlobalVal.VENDOR_CODE_DIGISIGN);
		vRUser.setIsActive("1");
		vRUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

		Calendar cal = Calendar.getInstance();
		Date date = cal.getTime();
		vRUser.setActivatedDate(date);
		vRUser.setDtmUpd(date);

		cal.add(Calendar.YEAR, 1);
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vRUser);

		TrDocumentH docH = daoFactory.getDocumentDao().getEarliestAgreement(user);
		SignDocumentDigisignResponseBean documentDigisign = new SignDocumentDigisignResponseBean();
		if (null != docH) {

			List<ActivationDocumentBean> lDocBean = daoFactory.getDocumentDao().getActivationDocumentByDocHAndUser(docH,
					user);

			if (lDocBean.size() == 1) {
				SignDocumentRequest signDocReq = new SignDocumentRequest();
				signDocReq.setDocumentId(lDocBean.get(0).getDocumentId());
				signDocReq.setEmail(user.getLoginId());
				documentDigisign = digisignLogic.signDocument(signDocReq, null, audit);
			}

			if (lDocBean.size() > 1) {
				response.setDocs(lDocBean);
			} else if (lDocBean.size() == 1) {
				if (documentDigisign.getLink() != null) {
					response.setSignLink(documentDigisign.getLink());
				} else {
					if (documentDigisign.getNotif() == null) {
						documentDigisign.setNotif("Tidak ada Agreement");
					}
				}
			} else if (lDocBean.isEmpty()) {
				response.setSignLink("");
				ActivationDocumentBean bean = new ActivationDocumentBean();
				bean.setDocumentId("");
				lDocBean.add(bean);
				response.setDocs(lDocBean);
			}

		} else {
			response.setSignLink("");
			ActivationDocumentBean bean = new ActivationDocumentBean();
			bean.setDocumentId("");
			List<ActivationDocumentBean> lDocBean = new ArrayList<>();
			lDocBean.add(bean);
			response.setDocs(lDocBean);
		}

		TrInvitationLink invLink = daoFactory.getInvitationLinkDao()
				.getInvitationLinkByRecieverDetailV2(user.getLoginId(),GlobalVal.VENDOR_CODE_DIGISIGN);
		if (invLink == null) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
			invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetail(personalData.getPhoneRaw());
		}
		response.setIdNo(invLink.getIdNo());
		response.setFullName(invLink.getFullName());
		response.setEmail(user.getLoginId());
		response.setPhoneNum(invLink.getPhone());

		if (invLink != null) {
			invLink.setIsActive("0");
			invLink.setGender(null);
			invLink.setKelurahan(null);
			invLink.setKecamatan(null);
			invLink.setKota(null);
			invLink.setZipCode(null);
			invLink.setPlaceOfBirth(null);
			invLink.setDateOfBirth(null);
			invLink.setProvinsi(null);
			invLink.setEmail(null);
			invLink.setPhotoSelf(null);
			invLink.setPhotoId(null);
			invLink.setIdNo(null);
			invLink.setUsrUpd(audit.getCallerId());
			invLink.setPhone(null);
			invLink.setAmMsuser(null);
			invLink.setAddress(null);
			invLink.setDtmUpd(new Date());
			daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		}

		// Add to queue for updating tr_error_history.rerun_process later
		UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(decryptedBean.getNik(),
				invLink.getMsVendor().getIdMsVendor());
		QueuePublisher.queueUpdErrHistRerunProcess(bean);

		status.setMessage(documentDigisign.getNotif());
		response.setStatus(status);

		return response;
	}

	@Override
	public ListInquiryUserResponse getListInquiryUser(ListInquiryUserRequest request, AuditContext audit)
			throws ParseException {
		if (StringUtils.isBlank(request.getRequestType())) {
			throw new UserException(this.messageSource.getMessage("businesslogic.global.mandatory",
					new String[] { "Request type" }, this.retrieveLocaleAudit(audit)), ReasonUser.REQUEST_TYPE_EMPTY);
		}
		String requestType = request.getRequestType();
		if (!GlobalVal.INQUIRY_USER_REQUEST_TYPE_CUSTOMER.equalsIgnoreCase(requestType)
				&& !GlobalVal.INQUIRY_USER_REQUEST_TYPE_EMPLOYEE.equalsIgnoreCase(requestType)) {
			throw new UserException(this.messageSource.getMessage("businesslogic.document.invalidinquirytype", null,
					this.retrieveLocaleAudit(audit)), ReasonUser.REQUEST_TYPE_INVALID);
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);

		ListInquiryUserResponse response = new ListInquiryUserResponse();
		List<InquiryUserBean> userList = new ArrayList<>();

		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);
		Integer rowPerPage = Integer.parseInt(gsValue);

		int start = ((request.getPage() - 1) * rowPerPage) + 1;
		int end = request.getPage() * rowPerPage;

		BigInteger totalResult = daoFactory.getUserDao().countListInquiryUser(request, requestType, tenant.getIdMsTenant());
		long total = totalResult.longValue();
		long totalPage = (total % rowPerPage == 0) ? total / rowPerPage : (total / rowPerPage) + 1;

		List<Map<String, Object>> userIds = daoFactory.getUserDao().getListInquiryUserId(request, requestType, tenant.getIdMsTenant(), start,
				end);

		Iterator<Map<String, Object>> itr = userIds.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();

			BigInteger idMsVendorRegisteredUser = (BigInteger) map.get("d0");
			BigInteger idMsUser = (BigInteger) map.get("d1");
			AmMsuser user = daoFactory.getUserDao().getUserByIdMsUser(idMsUser.longValue());
			MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByIdMsVendorRegisteredUser(idMsVendorRegisteredUser.longValue());

			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);

			InquiryUserBean bean = new InquiryUserBean();
			bean.setEmail(registeredUser.getSignerRegisteredEmail());
			bean.setNama(user.getFullName());
			StringBuilder status = new StringBuilder();
			String activatedDate = "";
			Date prevActDate = null;
			String certificateStatus = ACTIVE;
			String certificatePoaActiveStatus = ACTIVE;

			if ("0".equals(registeredUser.getIsRegistered())) {
				status.append("Not Registered");
			} else if ("0".equals(registeredUser.getIsActive()) && "1".equals(registeredUser.getIsRegistered())) {
				status.append(NOT_ACTIVE);
			} else if ("1".equals(registeredUser.getIsActive())) {
				status.append(ACTIVE);
			}
			
			if (null == registeredUser.getCertExpiredDate()) {
				certificateStatus = NOT_ACTIVE;
			}
			
			if (null != registeredUser.getCertExpiredDate() && GlobalVal.VENDOR_CODE_VIDA.equals(registeredUser.getMsVendor().getVendorCode()) && userValidatorLogic.isCertifExpiredForInquiry(registeredUser)) {
				certificateStatus = GlobalVal.CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS;
			}
			
			if (null == registeredUser.getCertPoaExpiredDate()) {
				certificatePoaActiveStatus = NOT_ACTIVE;
			}
			
			if (null != registeredUser.getCertPoaExpiredDate() && GlobalVal.VENDOR_CODE_VIDA.equals(registeredUser.getMsVendor().getVendorCode()) && userValidatorLogic.isCertifPoaExpiredForInquiry(registeredUser)) {
				certificatePoaActiveStatus = GlobalVal.CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS;
			}

			// pengecekan resend link aktivasi
			String resendlinkaktivasi = "0";
			if (StringUtils.isNotBlank(registeredUser.getIsRegistered())
					&& StringUtils.isNotBlank(registeredUser.getIsActive())) {
				if (registeredUser.getIsRegistered().equals("0") || registeredUser.getIsActive().equals("1")) {
					resendlinkaktivasi = "0";
				} else {
					resendlinkaktivasi = registeredUser.getMsVendor().getResendActivationLink();
				}
			}

			bean.setResendActivationLink(resendlinkaktivasi);
			bean.setVendorCode(registeredUser.getMsVendor().getVendorCode());
			bean.setVendorName(registeredUser.getMsVendor().getVendorName());

			if (null != registeredUser.getActivatedDate()) {
				if (StringUtils.isNotBlank(request.getActivatedDateEnd())) {
					Date startDate = sdf.parse(request.getActivatedDateStart());
					Date endDate = sdf.parse(request.getActivatedDateEnd());
					Date formattedActivatedDate = sdf.parse(sdf.format(registeredUser.getActivatedDate()));
					if (startDate.compareTo(formattedActivatedDate) <= 0
							&& endDate.compareTo(formattedActivatedDate) >= 0) {
						activatedDate = sdf.format(registeredUser.getActivatedDate());
					}
				} else if (null == prevActDate || prevActDate.before(registeredUser.getActivatedDate())) {
					activatedDate = sdf.format(registeredUser.getActivatedDate());
					prevActDate = registeredUser.getActivatedDate();
				}

			}

			bean.setStatus(status.toString());
			bean.setActivationDate(activatedDate);
			bean.setCertificateStatus(certificateStatus);
			bean.setCertificatePoaActiveStatus(certificatePoaActiveStatus);

			userList.add(bean);
		}

		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult((int) total);
		response.setListUser(userList);
		return response;
	}

	@Override
	public ChangePasswordResponse changePassword(ChangePasswordRequest request, AuditContext audit) {
		if (!request.getLoginId().equalsIgnoreCase(audit.getCallerId())) {
			throw new ChangePasswordException(this.messageSource
					.getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_LOGIN_ID, null, this.retrieveLocaleAudit(audit)),
					Reason.INVALID_USER);
		}

		AmMsuser user;
		boolean checkUserExistence = false;
		if (StringUtils.isNumeric(request.getLoginId())) {
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		}

		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new String[] { "User" }, this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		if ("0".equals(user.getIsActive())) {
			throw new ChangePasswordException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_INACTIVE_OR_NOT_EXIST, null,
							this.retrieveLocaleAudit(audit)),
					Reason.INACTIVE_USER);
		}

		if (request.getNewPassword().length() < 8) {
			throw new ChangePasswordException(this.messageSource.getMessage("businesslogic.user.minlengthpassword",
					null, this.retrieveLocaleAudit(audit)), Reason.CHANGE_PASSWORD_VIOLATION);
		}

		if (!isNewPasswordValid(request.getNewPassword(), audit)) {
			throw new ChangePasswordException(this.messageSource
					.getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_NEW_PASS, null, this.retrieveLocaleAudit(audit)),
					Reason.INVALID_NEW_PASSWORD);
		}

		if (validatePassword(request.getOldPassword(), request.getNewPassword(), user, audit)) {
			user.setPassword(PasswordHash.createHash(request.getNewPassword()));
			user.setChangePwdLogin("0");
			user.setDtmUpd(new Date());
			user.setUsrUpd(String.valueOf(user.getIdMsUser()));
			daoFactory.getUserDao().updateUser(user);

			AmUserpwdhistory history = new AmUserpwdhistory();
			MsLov lovPwChangeType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE,
					GlobalVal.CODE_LOV_PWD_CHANGE_TYPE_CHANGE);
			history.setAmMsuser(user);
			history.setDtmCrt(new Date());
			history.setPassword(PasswordHash.createHash(request.getNewPassword()));
			history.setUsrCrt(audit.getCallerId());
			history.setMsLov(lovPwChangeType);
			daoFactory.getUserDao().insertUserPwdhistory(history);
		} else {
			throw new ChangePasswordException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NEW_PASS_USED,
					null, this.retrieveLocaleAudit(audit)), Reason.INVALID_NEW_PASSWORD);
		}
		return new ChangePasswordResponse();
	}

	private boolean validatePassword(String oldPassword, String newPassword, AmMsuser user, AuditContext audit) {
		List<Map<String, Object>> passwordHistory = null;
		boolean valid = true;
		if (newPassword.length() < 8) {
			throw new ChangePasswordException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_CHANGE_PW_MIN_CHAR_ERROR, null,
							this.retrieveLocaleAudit(audit)),
					Reason.CHANGE_PASSWORD_VIOLATION);
		}
		if (PasswordHash.validatePassword(oldPassword, user.getPassword())) {
			passwordHistory = daoFactory.getUserDao().getUserPasswordHistoryList(user.getIdMsUser());
			if (null == passwordHistory) {
				passwordHistory = Collections.emptyList();
			}
			for (int i = 0; i < passwordHistory.size(); i++) {
				Map<String, Object> map = passwordHistory.get(i);
				if (PasswordHash.validatePassword(newPassword, (String) map.get("d0"))) {
					valid = false;
					break;
				}
			}
		} else {
			throw new ChangePasswordException(this.messageSource
					.getMessage(GlobalKey.MESSAGE_ERROR_CHANGE_PW_WRONG_PASS, null, this.retrieveLocaleAudit(audit)),
					Reason.INVALID_OLD_PASSWORD);
		}
		return valid;
	}

	private boolean isUserExist(String loginId, String vendorCode, AuditContext audit) {

		boolean usePhone = StringUtils.isNumeric(loginId);
		
		AmMsuser user;
		if (usePhone) {
			user = userValidatorLogic.validateGetUserByPhone(loginId, false, audit);
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(loginId, false, audit);
		}

		if (null == user) {
			return false;
		}
		
		MsVendorRegisteredUser registeredUser;
		if (usePhone) {
			registeredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(loginId, vendorCode);
		} else {
			registeredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(loginId, vendorCode);
		}

		if (null == registeredUser) {
			return false;
		}
		return "1".equals(registeredUser.getIsRegistered());
	}

	private boolean isNewPasswordValid(String newPassword, AuditContext audit) {
		// chomp untuk hapus \n diakhir string, sebelumnya ada \n di regex password
		String regex = StringUtils.chomp(commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PASSWORD_FORMAT, audit));
		return newPassword.matches(regex);
	}
	
	private short getForgotPasswordDefaultMaxAttempt(AuditContext audit) {
		try {
			String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_OTP_RESET_PWD_DAILY, audit);
			return Short.parseShort(gsValue);
		} catch (Exception e) {
			return 3;
		}
	}

	@Override
	public ForgotPasswordResponse forgotPassword(ForgotPasswordRequest request, AuditContext audit) {		
		MsOffice office = null;
		MsBusinessLine businessLine = null;
		MsTenant tenant = null;
		
		MsVendorRegisteredUser vendorUser = getVendorUserForSendOtpForgotPassword(request, audit);

		String messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit);

		commonValidatorLogic.validateNotNull(vendorUser, messageValidation, StatusCode.USER_NOT_FOUND);
		
		AmMsuser user = vendorUser.getAmMsuser();
		
		TrDocumentD latestDocument = daoFactory.getDocumentDao().getLatestDocumentDetailBySigner(user);
		if (null != latestDocument) {
			tenant = latestDocument.getMsTenant();
			office = latestDocument.getTrDocumentH().getMsOffice();
			businessLine = latestDocument.getTrDocumentH().getMsBusinessLine();
		} else {
			String receiverDetail = getReceiverDetailForgotPassword(user, vendorUser);
			TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getLatestInvitationLinkByReceiverDetail(receiverDetail);
			if (null != invLink) {
				tenant = invLink.getMsTenant();
				office = invLink.getMsOffice();
				businessLine = invLink.getMsBusinessLine();
			} else {
				MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getLatestUserTenant(user.getLoginId());
				tenant = useroftenant.getMsTenant();
			}
		}

		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIN_USER_DORMANT_1, null, audit);

		commonValidatorLogic.validateMustNotEquals(user.getIsDormant(), "1", messageValidation, StatusCode.INACTIVE_USER);
		
		if (user.getPassword().equals(REGISTER_BY_INVITATION_PASSWORD)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_DOES_NOT_HAVE_ANY_DOC, null, audit),
					ReasonUser.USER_DOES_NOT_HAVE_ANY_DOCUMENT);
		}
		
		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		
		validateForgetPasswordRequestNum(resetCodeRequestNum, tenant, user, audit);
		
		String resetCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		Date currentDate = new Date();
		user.setResetCode(resetCode);
		user.setResetCodeRequestDate(currentDate);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(currentDate);
		if (null != resetCodeRequestNum) {
			Short value = (short) (resetCodeRequestNum + 1);
			user.setResetCodeRequestNum(value);
		} else {
			user.setResetCodeRequestNum((short) 1);
		}
		daoFactory.getUserDao().updateUser(user);
		
		MsNotificationtypeoftenant notificationType = daoFactory.getNotificationtypeoftenantDao().getNotificationType(tenant, NotificationSendingPoint.RESET_PASSWORD.toString());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		String sendingPointSMSGateway = getNotifSmsGateway(notificationType, tenant, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
		String sendingPointWAGateway = getNotifWaGateway(notificationType, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);		
		
		String durationResendOtp = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_DEFAULT_DURATION_RESEND_OTP);
		int resendOtpDuration = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_DURATION_RESEND_OTP_RESET_PASS, Integer.parseInt(durationResendOtp));
		
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_RESET_PASSW0RD);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP);
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();

		if (vendorUser.getPhoneBytea() == null && (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(request.getSendingPointOption().toUpperCase()) || GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(request.getSendingPointOption().toUpperCase()))) {
			throw new UserException(getMessage("businesslogic.user.phonenoisempty", null, audit), ReasonUser.PHONE_NO_EMPTY);
		} else if (vendorUser.getPhoneBytea() != null) {
			String phoneNum = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			auditTrailBean.setPhone(phoneNum);
		}
		
		auditTrailBean.setEmail(user.getLoginId());
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setNotes("Request OTP Reset Password");
		auditTrailBean.setOtpCode(resetCode);
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(user);
		auditTrailBean.setVendorPsre(vendorUser.getMsVendor());

		return sendOtpForgotPassowordBaseSendingPoint(request.getSendingPointOption(), resetCode, sendingPointSMSGateway, sendingPointWAGateway, resendOtpDuration, user, vendorUser, tenant, vendor, office, businessLine, auditTrailBean, audit);
	}

	// Main method that handles sending OTP based on different options
	public ForgotPasswordResponse sendOtpForgotPassowordBaseSendingPoint(String sendingPointOptions, String resetCode, String sendingPointSMSGateway, String sendingPointWAGateway, int resendOtpDuration, AmMsuser user, MsVendorRegisteredUser vendorUser, MsTenant tenant, MsVendor vendor, MsOffice office, MsBusinessLine businessLine, SigningProcessAuditTrailBean auditTrail, AuditContext audit) {
		
		ForgotPasswordResponse response = new ForgotPasswordResponse();

		response.setDurationResendOTP(resendOtpDuration);

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equals(sendingPointOptions.toUpperCase())) {
			if (null == vendorUser.getEmailService() ? user.getEmailService().equals("1") : vendorUser.getEmailService().equals("1")) {
				throw new UserException(getMessage("businesslogic.user.userregisterwithoutemail", null, audit),
						ReasonUser.USER_REGISTERED_WITHOUT_EMAIL);
			}
			return sendResetPasswordEmail(vendorUser, tenant, resetCode, auditTrail, resendOtpDuration);
		}

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOptions.toUpperCase())) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, audit);
			return sendingPointWAGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP) 
					? sendResetPasswordWhatsApp(vendorUser, tenant, office, businessLine, resetCode, resendOtpDuration, auditTrail, audit)
					: sendResetPasswordWhatsAppHalosis(vendorUser, tenant, office, businessLine, resetCode, resendOtpDuration, auditTrail, audit);
		}

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(sendingPointOptions.toUpperCase())) {
			String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, tenant, vendor, audit);
			if (GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS.equals(sendingPointSMSGateway)) {
				sendResetPasswordJatis(vendorUser, tenant, office, businessLine, resetCode, auditTrail, audit);
			} else {
				sendResetPasswordVFirst(vendorUser, tenant, office, businessLine, resetCode, auditTrail, audit);
			}
			response.setRecipient(phoneNumber);
			return response;
		}

		throw new UserException(getMessage("businesslogic.user.otpsendingmedianotvalid", null, audit), ReasonUser.OTP_SENDING_MEDIA_NOT_VALID);
	}

	public void validateForgetPasswordRequestNum(Short resetCodeRequestNum, MsTenant tenant, AmMsuser user, AuditContext audit) {
		Date currentDate = new Date();
		if (null == user.getResetCodeRequestDate() || !DateUtils.isSameDay(currentDate, user.getResetCodeRequestDate())) {
			resetCodeRequestNum = (short) 0;
		}

		// Validate reset password request amount
		Short maxResetPasswordRequest = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_OTP_RESET_PWD_DAILY, getForgotPasswordDefaultMaxAttempt(audit));		
		if (null != resetCodeRequestNum && resetCodeRequestNum >= maxResetPasswordRequest) {
			throw new UserException(getMessage("businesslogic.user.maxresetpasswordreached", null, audit), ReasonUser.MAX_RESET_PASSWORD_REACHED);
		}
	}

	public String getReceiverDetailForgotPassword(AmMsuser user, MsVendorRegisteredUser vendorUser) {
		if (vendorUser.getEmailService() == null) {
			if ("1".equals(user.getEmailService())) {
				return personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			} else {
				return vendorUser.getSignerRegisteredEmail();
			}
		} else {
			if ("1".equals(vendorUser.getEmailService())) {
				return personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			} else {
				return vendorUser.getSignerRegisteredEmail();
			}
		}
	}

	private String getNotifSmsGateway(MsNotificationtypeoftenant notificationType, MsTenant tenant, String defaultValue) {
		if (notificationType != null) {
			return (notificationType.getLovSmsGateway() == null) ? defaultValue : notificationType.getLovSmsGateway().getCode();
		} else {
			return (tenant.getLovSmsGateway() == null) ? defaultValue : tenant.getLovSmsGateway().getCode();
		}
	}
	
	private String getNotifWaGateway(MsNotificationtypeoftenant notificationType, String defaultValue) {
		if (notificationType != null) {
			return (notificationType.getLovWaGateway() == null) ? defaultValue : notificationType.getLovWaGateway().getCode();
		}
		return defaultValue;
	}
	
	private ForgotPasswordResponse sendResetPasswordEmail(MsVendorRegisteredUser vendorUser, MsTenant tenant, String resetCode, SigningProcessAuditTrailBean auditTrail, int resendOtpDuration) {
		
		boolean useOtpDuration = null != tenant.getOtpActiveDuration() && tenant.getOtpActiveDuration() > 0;
		
		Map<String, Object> userMap = new HashMap<>();
		userMap.put(FULLNAME, vendorUser.getAmMsuser().getFullName());
		userMap.put("otp", resetCode);
		if (useOtpDuration) {
			userMap.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
		}
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userMap);
		
		String templateCode = useOtpDuration ? GlobalVal.TEMPLATE_OTP_VERIF_EMAIL_WITH_DURATION : GlobalVal.TEMPLATE_KODE_OTP;
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(templateCode, templateParameters);
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(new String[] { vendorUser.getSignerRegisteredEmail() });
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setSubject(template.getSubject());
		
		try {
			if (auditTrail.getPhone() == null) {
				emailSenderLogic.sendEmail(emailInfo, null);
			} else {
				String phone = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
				auditTrail.setPhone(phone);
				emailSenderLogic.sendEmail(emailInfo, null, auditTrail);
			}
		} catch (MessagingException e) {
			LOG.error("Failed to send reset password email", e);
		}
		
		ForgotPasswordResponse response = new ForgotPasswordResponse();
		
		response.setDurationResendOTP(resendOtpDuration);
		response.setRecipient(vendorUser.getSignerRegisteredEmail());
		return response;
	}
	
	private ForgotPasswordResponse sendResetPasswordWhatsApp(MsVendorRegisteredUser vendorUser, MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String resetCode, int resendOtpDuration, SigningProcessAuditTrailBean auditTrail,  AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, audit);
		
		boolean useOtpDuration = null != tenant.getOtpActiveDuration() && tenant.getOtpActiveDuration() > 0;
		String templateCode = useOtpDuration ? GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration() : GlobalVal.TEMPLATE_OTP_WA; 
		
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateCode);
		
		String buttonText = resetCode;
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(resetCode);
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
		
		String notes = phoneNumber + GlobalVal.SEND_OTP_WA_FORGOT_PASSWORD;
		
		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(template);
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(tenant);
		request.setMsBusinessLine(businessLine);
		request.setMsOffice(office);
		request.setPhoneNumber(phoneNumber);
		request.setAmMsuser(vendorUser.getAmMsuser());
		request.setRemoveHeader(true);
		request.setNotes(notes);
		request.setIsOtp(true);

		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_WA_FORPASS);

		
		if ("1".equals(gs.getGsValue())) {
			whatsAppLogic.sendMessage(request, auditTrail, audit);
		} else {
			LOG.info("Reset Password OTP sent via WhatsApp with template : {}", template.getTemplateCode());
		}
		
		ForgotPasswordResponse response = new ForgotPasswordResponse();
		
		response.setDurationResendOTP(resendOtpDuration);
		response.setRecipient(phoneNumber);
		return response;
	}
	
	private ForgotPasswordResponse sendResetPasswordWhatsAppHalosis(MsVendorRegisteredUser vendorUser, MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String resetCode, int resendOtpDuration, SigningProcessAuditTrailBean auditTrail, AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, audit);
		
		boolean useOtpDuration = null != tenant.getOtpActiveDuration() && tenant.getOtpActiveDuration() > 0;
		String templateCode = useOtpDuration ? GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration() : GlobalVal.TEMPLATE_OTP_WA; 
		
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateCode);
		
		String buttonText = resetCode;
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(resetCode);
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());

		String notes = phoneNumber + GlobalVal.SEND_OTP_WA_FORGOT_PASSWORD;
		
		HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(template);
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(tenant);
		request.setMsBusinessLine(businessLine);
		request.setMsOffice(office);
		request.setPhoneNumber(phoneNumber);
		request.setAmMsuser(vendorUser.getAmMsuser());
		request.setNotes(notes);
		request.setIsOtp(true);

		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_WA_FORPASS);

		
		if ("1".equals(gs.getGsValue())) {
			whatsAppHalosisLogic.sendMessage(request, auditTrail, audit);
		} else {
			LOG.info("Reset Password OTP sent via WhatsApp with template : {}", template.getTemplateCode());
		}
		
		ForgotPasswordResponse response = new ForgotPasswordResponse();

		response.setDurationResendOTP(resendOtpDuration);		
		response.setRecipient(phoneNumber);
		return response;
	}
	
	private void sendResetPasswordJatis(MsVendorRegisteredUser vendorUser, MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String resetCode, SigningProcessAuditTrailBean auditTrail, AuditContext audit) {
		
		boolean useOtpDuration = null != tenant.getOtpActiveDuration() && tenant.getOtpActiveDuration() > 0;
		
		Map<String, Object> userMap = new HashMap<>();
		userMap.put("otp", resetCode);
		if (useOtpDuration) {
			userMap.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
		}

		Map<String, Object> param = new HashMap<>();
		param.put("user", userMap);
		
		String templateCode = useOtpDuration ? GlobalVal.TEMPLATE_RESET_PASS_ESIGN_EXPIRED : GlobalVal.TEMPLATE_RESET_PASS_ESIGN;
		MsMsgTemplate msgTemplate = msgTemplateLogic.getAndParseContent(templateCode, param);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_FORPASS);
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String phoneNum = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());	
		
		if ("1".equals(gs.getGsValue())) {
			JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, office, businessLine, phoneNum, msgTemplate.getBody(), String.valueOf(nextTrxNo), true);
			String notes = String.format(CONST_SEND_OTP_FORGOT_PASS_ESIGN, phoneNum);
			jatisSmsLogic.sendSmsAndCutBalance(request, null, null, vendorUser.getAmMsuser(), notes, audit, auditTrail);
		} else {
			LOG.info("SMS Forgot Password Jatis Sent.");
		}
	}
	
	private void sendResetPasswordVFirst(MsVendorRegisteredUser vendorUser, MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String resetCode, SigningProcessAuditTrailBean auditTrail,  AuditContext audit) {
		boolean useOtpDuration = null != tenant.getOtpActiveDuration() && tenant.getOtpActiveDuration() > 0;
		
		Map<String, Object> userMap = new HashMap<>();
		userMap.put("otp", resetCode);
		if (useOtpDuration) {
			userMap.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
		}
		
		Map<String, Object> param = new HashMap<>();
		param.put("user", userMap);
		
		String templateCode = useOtpDuration ? GlobalVal.TEMPLATE_RESET_PASS_ESIGN_EXPIRED : GlobalVal.TEMPLATE_RESET_PASS_ESIGN;
		MsMsgTemplate msgTemplate = msgTemplateLogic.getAndParseContent(templateCode, param);
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_FORPASS);
		String phoneNum = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());

		SendSmsResponse response = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNum, msgTemplate.getBody(), tenant);
				
		if ("1".equals(gs.getGsValue())) {
			response = smsLogic.sendSms(sendSmsValueFirstRequestBean, auditTrail);
		} else {
			LOG.info("SMS Forgot Password sent.");
			response.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		String notes = String.format(CONST_SEND_OTP_FORGOT_PASS_ESIGN, phoneNum);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		if (response.getErrorCode() == null || 
				(!response.getErrorCode().equals(VFIRST_ERR28682)
				&& !response.getErrorCode().equals(VFIRST_ERR28681)
				&& !response.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					-1, String.valueOf(response.getTrxNo()), vendorUser.getAmMsuser(), notes, response.getGuid(), office, businessLine, audit);
		} else {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					0, String.valueOf(response.getTrxNo()), vendorUser.getAmMsuser(), notes + GlobalVal.BALMUT_ERROR + response.getErrorCode(),
					response.getGuid(), office, businessLine, audit);
		}
	}

	@Override
	public ResetPasswordResponse resetPassword(ResetPasswordRequest request, AuditContext audit) {
		AmMsuser user;
		if (StringUtils.isNumeric(request.getLoginId())) {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
		} else {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		}

		if (null == user) {
			throw new ChangePasswordException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_INACTIVE_OR_NOT_EXIST, null,
							this.retrieveLocaleAudit(audit)),
					Reason.INACTIVE_USER);
		}

		if (!request.getResetCode().equals(user.getResetCode())) {
			throw new ChangePasswordException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_INCORRECT_RESET_CODE, null,
							this.retrieveLocaleAudit(audit)),
					Reason.INCORRECT_RESET_CODE);
		}

		if (!validatePasswordForReset(request.getNewPassword(), user, audit)) {
			throw new ChangePasswordException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NEW_PASS_USED,
					null, this.retrieveLocaleAudit(audit)), Reason.INVALID_NEW_PASSWORD);
		}

		user.setIsLocked("0");
		user.setFailCount(0);
		user.setResetCode(null);
		user.setChangePwdLogin("0");
		user.setPassword(PasswordHash.createHash(request.getNewPassword()));
		daoFactory.getUserDao().updateUser(user);

		MsLov lovPwChangeType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE,
				GlobalVal.CODE_LOV_PWD_CHANGE_TYPE_RESET);
		AmUserpwdhistory newUserPwdhistory = new AmUserpwdhistory();
		newUserPwdhistory.setAmMsuser(user);
		newUserPwdhistory.setDtmCrt(new Date());
		newUserPwdhistory.setPassword(PasswordHash.createHash(request.getNewPassword()));
		newUserPwdhistory.setUsrCrt(audit.getCallerId());
		newUserPwdhistory.setMsLov(lovPwChangeType);
		daoFactory.getUserDao().insertUserPwdhistory(newUserPwdhistory);

		return new ResetPasswordResponse();
	}

	private boolean validatePasswordForReset(String newPassword, AmMsuser user, AuditContext audit) {
		List<Map<String, Object>> passwordHistory = null;
		boolean valid = true;
		if (newPassword.length() < 8) {
			throw new ChangePasswordException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_CHANGE_PW_MIN_CHAR_ERROR, null,
							this.retrieveLocaleAudit(audit)),
					Reason.CHANGE_PASSWORD_VIOLATION);
		}
		if (!isNewPasswordValid(newPassword, audit)) {
			throw new ChangePasswordException(this.messageSource
					.getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_NEW_PASS, null, this.retrieveLocaleAudit(audit)),
					Reason.INVALID_NEW_PASSWORD);
		}
		passwordHistory = daoFactory.getUserDao().getUserPasswordHistoryList(user.getIdMsUser());
		if (null == passwordHistory) {
			passwordHistory = Collections.emptyList();
		}
		for (int i = 0; i < passwordHistory.size(); i++) {
			Map<String, Object> map = passwordHistory.get(i);
			if (PasswordHash.validatePassword(newPassword, (String) map.get("d0"))) {
				valid = false;
				break;
			}
		}
		return valid;
	}

	@Override
	public SendOtpEmailResponse sendOtp(SendOtpEmailRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}

		int flag = 1; // kalo flag = 0 artinya by phone
		AmMsuser user;
		boolean checkUserExistence = false;
		if (StringUtils.isNumeric(request.getLoginId())) {
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
			flag = 0;
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		}

		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		MsVendorRegisteredUser mvru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());

		if ("1".equals(mvru.getEmailService())) {
			flag = 0;
		}

		if (this.isUserExist(request.getLoginId(), request.getVendorCode(), audit)) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_REGISTERED, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.ALREADY_REGISTERED);
		}

		if (null == request.getVendorCode()) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new String[] { CONST_VENDOR }, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_NOT_FOUND);
		}

		MsVendor mv = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());

		if (mvru.getEmailService().equals("1") && mv.getVerifPhone().equals("0")) {
			throw new InvitationLinkException(
					this.messageSource.getMessage(MSG_VENDOR_CANNOT_SEND_PHONE,
							new Object[] { mv.getVendorCode() }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.CANNOT_SEND_OTP_TO_PHONE);
		}

		if (mv.getVerifPhone().equals("0") && StringUtils.isNumeric(request.getLoginId())) { // kalo verifPhone 0 dan
																								// param lempar no telp
																								// ke be masuk sini
			throw new InvitationLinkException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_CANNOT_SEND_OTP_TO_EMAIL,
							new Object[] { request.getLoginId() }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.CANNOT_SEND_OTP_TO_PHONE);
		}
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		LOG.info(otpCode);

		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		if (flag == 0) {
			MsTenant mt = daoFactory.getTenantDao().getTenantByUser(user.getLoginId());
			this.sendOtpSms(personalDataEncLogic.decryptToString(mvru.getPhoneBytea()), otpCode, null, mt, user,null,null,null,null, null, audit);
		} else {
			this.sendOtpEmail(user.getFullName(), user.getLoginId(), otpCode, audit);
		}
		return new SendOtpEmailResponse();
	}

	@Override
	public SendOtpEmailResponse sendOtpEmbed(SendOtpEmailEmbedRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}

		if (StringUtils.isBlank(request.getFullname())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] { FULLNAME }, this.retrieveLocaleAudit(audit)), ReasonUser.FULLNAME_EMPTY);
		}

		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new String[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}

		int flag = 1; // kalo flag = 0 artinya by phone
		AmMsuser user;
		boolean checkUserExistence = false;
		if (StringUtils.isNumeric(request.getLoginId())) {
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
			flag = 0;
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		}

		if (this.isUserExist(request.getLoginId(), request.getVendorCode(), audit)) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_REGISTERED, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.ALREADY_REGISTERED);
		}

		MsVendor mv = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());

		if (mv.getVerifPhone().equals("0") && StringUtils.isNumeric(request.getLoginId())) { // kalo verifPhone 0 dan
																								// param lempar no telp
																								// ke be masuk sini
			throw new InvitationLinkException(
					this.messageSource.getMessage(MSG_VENDOR_CANNOT_SEND_PHONE,
							new Object[] { mv.getVendorCode() }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.CANNOT_SEND_OTP_TO_PHONE);
		}
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		LOG.info(otpCode);
		TrOtp otp = daoFactory.getOtpDao().getOtpByLoginId(request.getLoginId());
		if (null != otp) {
			otp.setOtpCode(otpCode);
			daoFactory.getOtpDao().updateOtp(otp);
		} else {
			otp = new TrOtp();
			otp.setLoginId(StringUtils.upperCase(request.getLoginId()));
			otp.setOtpCode(otpCode);
			otp.setUsrCrt(audit.getCallerId());
			otp.setDtmCrt(new Date());
			daoFactory.getOtpDao().insertOtp(otp);
		}
		if (flag == 1) {
			this.sendOtpEmail(request.getFullname(), request.getLoginId(), otpCode, audit);
		} else {
			MsTenant mt;
			mt = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
			this.sendOtpSms(request.getLoginId(), otpCode, null,  mt, user,null,null,null,null,null, audit);
		}
		return new SendOtpEmailResponse();
	}
	
	private boolean canSendOtpRegis(String userIdentifier, String vendorCode,TrInvitationLink invLink, AuditContext audit) {
		MsVendorRegisteredUser vendorUser = null;
		if (StringUtils.isNumeric(userIdentifier)) {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(userIdentifier, vendorCode);
		} else {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(userIdentifier, vendorCode);
		}
		
		if (null == vendorUser) {
			return true;
		}
		if (StringUtils.isBlank(vendorUser.getVendorRegistrationId())) {
			userValidatorLogic.validateVendorRegisteredUserDataState(vendorUser, invLink.getIdNo(), invLink.getEmail(), invLink.getPhone(), audit);
			return true;
		}
		
		if (!GlobalVal.VENDOR_CODE_VIDA.equals(vendorUser.getMsVendor().getVendorCode())) {
			return false;
		}
		
		// User can send OTP for register if VIDA certif is expired
		return userValidatorLogic.isCertifExpiredForRegister(vendorUser, audit);
	}

	@Override
	public SendOtpEmailResponse sendOtpInvitation(SendOtpEmailEmbedRequest request, AuditContext audit) {

		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, audit), ReasonUser.LOGIN_ID_EMPTY);
		}

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invitationLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.OTP_EMAIL_INVITATION.getLovCode());
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setUsrCrt(audit.getCallerId());
		auditTrail.setDtmCrt(new Date());
		auditTrail.setEmail(invitationLink.getEmail());
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(invitationLink.getPhone()));
		auditTrail.setHashedPhoneNo( MssTool.getHashedString(invitationLink.getPhone()));
		auditTrail.setMsTenant(invitationLink.getMsTenant());
		auditTrail.setMsVendor(invitationLink.getMsVendor());
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setTrInvitationLink(invitationLink);
		auditTrail.setNotes("Register Email Invitation");
		
		boolean usePhone = StringUtils.isNumeric(request.getLoginId());
		MsVendor vendor = invitationLink.getMsVendor();

		if (!canSendOtpRegis(request.getLoginId().toUpperCase(), vendor.getVendorCode(),invitationLink, audit)) {
			String user = invLinkLogic.getDataUserInvitation(request.getLoginId(), vendor.getVendorCode(), audit);
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_ALREADY_REGISTEREDV2,
					new Object[] { user, vendor.getVendorCode() }, audit), ReasonUser.ALREADY_REGISTERED);
		}
		
		if (!"1".equals(vendor.getVerifPhone()) && usePhone) {
			auditTrail.setResultStatus("0");
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			
			throw new InvitationLinkException(getMessage(MSG_VENDOR_CANNOT_SEND_PHONE,
					new Object[] { vendor.getVendorCode() }, audit), ReasonInvitationLink.CANNOT_SEND_OTP_TO_PHONE);
		}

		if (!request.getLoginId().equalsIgnoreCase(invitationLink.getReceiverDetail())) {
			throw new InvitationLinkException(getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_CANNOT_SEND_OTP_TO_EMAIL,
					new Object[] { request.getLoginId() }, audit), ReasonInvitationLink.CANNOT_SEND_OTP_TO_EMAIL);
		}
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		LOG.info(otpCode);
		invitationLink.setDtmUpd(new Date());
		invitationLink.setUsrUpd(audit.getCallerId());
		invitationLink.setOtpCode(otpCode);
		daoFactory.getInvitationLinkDao().updateInvitationLink(invitationLink);
		auditTrail.setOtpCode(otpCode);
		
		if (usePhone) {
			sendOtpSms(request.getLoginId(), otpCode, null, invitationLink.getMsTenant(), null,null,null,null,null,auditTrail, audit);
		} else {
			try {
				sendOtpEmail(invitationLink.getFullName(), invitationLink.getReceiverDetail(), otpCode, audit);	
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
				auditTrail.setResultStatus("1");
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			} catch (UserException e) {
				auditTrail.setResultStatus("0");
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				
				throw e;
			}
		}

		String durationResendOtp = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_DEFAULT_DURATION_RESEND_OTP);
		int resendOtpDuration = tenantSettingsLogic.getSettingValue(invitationLink.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_DURATION_DURATION_RESEND_OTP_EMAIL_ACTIVATION, Integer.parseInt(durationResendOtp));
		
		SendOtpEmailResponse response = new SendOtpEmailResponse();

		response.setDurationResendOTP(resendOtpDuration);

		return response;
	}

	@Override
	public OtpVerificationResponse verifyOtp(OtpVerificationRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}
		MsVendorRegisteredUser vuser;
		AmMsuser user;
		boolean checkUserExistence = false;

		if (StringUtils.isNumeric(request.getLoginId())) { // kalo loginId isinya bukan no hp
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
			vuser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(request.getLoginId(),
					request.getVendorCode());
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
			vuser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(request.getLoginId(),
					request.getVendorCode());
		}

		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		if (vuser.getIsRegistered().equals("1")) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_REGISTERED, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.ALREADY_REGISTERED);
		}
		if (StringUtils.isBlank(request.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { CONST_CODE_OTP }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}
		if (!request.getOtpCode().equals(user.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
		}

		user.setOtpCode(null);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		return new OtpVerificationResponse();
	}

	@Override
	public OtpVerificationResponse verifyOtpEmbed(OtpVerificationEmbedRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}
		if (StringUtils.isBlank(request.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "OTP" }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}

		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}

		TrOtp otp = daoFactory.getOtpDao().getOtpByLoginId(request.getLoginId());
		if (null == otp) {
			throw new UserException("", ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		if (!otp.getOtpCode().equals(request.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
		}
		daoFactory.getOtpDao().deleteOtp(otp);
		return new OtpVerificationResponse();
	}

	@Override
	public OtpVerificationResponse verifyOtpInvitation(OtpVerificationEmbedRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}
		if (StringUtils.isBlank(request.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { CONST_CODE_OTP }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);

		if (StringUtils.isBlank(invCode)) {
			OtpVerificationResponse response = new OtpVerificationResponse();
			Status status = new Status();
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.embedmsg.invalidmsg", new Object[] {},
					this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		TrInvitationLink invitationLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.SEND_DOC.getLovCode());
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setTrInvitationLink(invitationLink);
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(audit.getCallerId());
		auditTrail.setEmail(invitationLink.getEmail());
		if (StringUtils.isNotBlank(invitationLink.getPhone())) {
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(invitationLink.getPhone()));
			auditTrail.setHashedPhoneNo( MssTool.getHashedString(invitationLink.getPhone()));
		}
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setMsTenant(invitationLink.getMsTenant());
		auditTrail.setMsVendor(invitationLink.getMsVendor());
		auditTrail.setOtpCode(request.getOtpCode());

//		if (GlobalVal.INV_BY_SMS.equalsIgnoreCase(invitationLink.getInvitationBy())) {
//			throw new InvitationLinkException(
//					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_PROCESS_ONLY_FOR_INV_BY_EMAIL,
//							new Object[] { "Verify OTP" }, this.retrieveLocaleAudit(audit)),
//					ReasonInvitationLink.PROCESS_FOR_INV_BY_EMAIL_ONLY);
//		}

		if (!request.getLoginId().equalsIgnoreCase(invitationLink.getReceiverDetail())) {
			throw new InvitationLinkException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_CANNOT_VERIFY_OTP_TO_EMAIL,
							new Object[] { request.getLoginId() }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.CANNOT_VERIFY_OTP_TO_EMAIL);
		}

		if (!request.getOtpCode().equalsIgnoreCase(invitationLink.getOtpCode())) {
			auditTrail.setResultStatus("0");
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
		}

//		if(!request.getVendorCode().equals(invitationLink.getMsVendor().getVendorCode())) {
//			throw new UserException(
//					this.messageSource.getMessage("businesslogic.vendor.vendorcodeinvlink",
//							new Object[] { request.getVendorCode() }, this.retrieveLocaleAudit(audit)),
//					ReasonUser.VERIFY_NOT_MATCH);
//		}

		if (!canSendOtpRegis(request.getLoginId(), invitationLink.getMsVendor().getVendorCode(),invitationLink, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_REGISTERED, null, audit), ReasonUser.ALREADY_REGISTERED);
		}

		invitationLink.setDtmUpd(new Date());
		invitationLink.setUsrUpd(audit.getCallerId());
		invitationLink.setOtpCode(null);
		daoFactory.getInvitationLinkDao().updateInvitationLink(invitationLink);
		
		auditTrail.setResultStatus("1");
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

		return new OtpVerificationResponse();
	}

	@Override
	public void insertUserofTenant(AmMsuser user, MsTenant tenant, AuditContext audit) {
		MsUseroftenant uot = new MsUseroftenant();
		uot.setAmMsuser(user);
		uot.setMsTenant(tenant);
		uot.setDtmCrt(new Date());
		uot.setUsrCrt(audit.getCallerId());
		daoFactory.getUserDao().insertUseroftenant(uot);
	}

	@Override
	public void saveUserPhoto(String loginId, String idPhoto, String selfPhoto, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(loginId);
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		if (null == personalDataBean || null == personalDataBean.getUserPersonalData()) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_PERSONAL_DATA_NOT_FOUND,
					null, this.retrieveLocaleAudit(audit)), ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}

		if (StringUtils.isNotBlank(selfPhoto)) {
			byte[] selfPhotoByteArray = MssTool.imageStringToByteArray(selfPhoto);
			personalDataBean.setSelfPhotoRaw(selfPhotoByteArray);
		} else {
			LOG.warn("SaveUserPhoto: Self photo of user {} is empty.", loginId);
		}

		if (StringUtils.isNotBlank(idPhoto)) {
			byte[] idPhotoByteArray = MssTool.imageStringToByteArray(idPhoto);
			personalDataBean.setPhotoIdRaw(idPhotoByteArray);// to be encrypted
		} else {
			LOG.warn("SaveUserPhoto: Id photo of user {} is empty.", loginId);
		}

		personalDataBean.getUserPersonalData().setUsrCrt(audit.getCallerId());
		personalDataBean.getUserPersonalData().setDtmUpd(new Date());
		daoFactory.getUserDao().updateUserPersonalData(personalDataBean);
	}

	@Override
	public ResetCodeVerificationResponse verifyResetCode(ResetCodeVerificationRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}
		AmMsuser user;
		String validationMessage = "";
		if (StringUtils.isNumeric(request.getLoginId())) {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
		} else {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		}

		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		if (StringUtils.isBlank(request.getResetCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "Reset code" }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}
		MsTenant tenant = null;
		// List<MsVendorRegisteredUser> vendorUser = null;
		TrDocumentD docDCheck = daoFactory.getDocumentDao().getLatestDocumentDetailBySigner(user);

		MsVendorRegisteredUser vendorUser = getVendorUserForValidateOtpForgotPassword(request, audit);
		
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit);
		commonValidatorLogic.validateNotNull(vendorUser, validationMessage, StatusCode.USER_NOT_FOUND);

		
		
		if (null != docDCheck) {
			tenant = docDCheck.getMsTenant();
		} else {
			String receiverDetail = getReceiverDetailForgotPassword(user, vendorUser);
			TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getLatestInvitationLinkByReceiverDetail(receiverDetail);
			if (null != invLink) {
				tenant = invLink.getMsTenant();
			} else {
				MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getLatestUserTenant(user.getLoginId());
				tenant = useroftenant.getMsTenant();
			}
		}
						
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_RESET_PASSW0RD);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP);
				
		if (tenant.getOtpActiveDuration() != null &&tenant.getOtpActiveDuration() > 0 && user.getResetCode() != null && user.getResetCodeRequestDate() != null) {
			
			Date nowDate = new Date();
			Date requestDate = user.getResetCodeRequestDate();
			long milliSecondsDiff = nowDate.getTime() - requestDate.getTime();
			double minutesDifff =  (double)  milliSecondsDiff / DateUtils.MILLIS_PER_MINUTE;
			
			LOG.info("Tenant OTP active duration: {} | Verify reset password OTP activation duration: {}", tenant.getOtpActiveDuration(), minutesDifff);
			if (minutesDifff > (double) tenant.getOtpActiveDuration()) {

					
				Status stts = new Status();
				stts.setCode(StatusCode.OTP_EXPIRED);
				stts.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_EXPIRED_OTP_CODE, null, audit));
				
				ResetCodeVerificationResponse response = new ResetCodeVerificationResponse();
				response.setStatus(stts);
				return response;
				
			}
				
		}
		
		if (!request.getResetCode().equals(user.getResetCode())) {
			
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

			auditTrail.setPhoneNoBytea(vendorUser.getPhoneBytea());
			auditTrail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
			auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
			auditTrail.setAmMsUser(user);
			auditTrail.setMsTenant(tenant);
			auditTrail.setMsVendor(vendorUser.getMsVendor());
			auditTrail.setLovSendingPoint(sendingPointLov);
			auditTrail.setLovProcessType(signingProcessTypeLov);
			auditTrail.setOtpCode(request.getResetCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
		}
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

		auditTrail.setPhoneNoBytea(vendorUser.getPhoneBytea());
		auditTrail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
		auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
		auditTrail.setAmMsUser(user);
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendorUser.getMsVendor());
		auditTrail.setLovSendingPoint(sendingPointLov);
		auditTrail.setLovProcessType(signingProcessTypeLov);
		auditTrail.setOtpCode(request.getResetCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		
		return new ResetCodeVerificationResponse();
	}

	@Override
	public ActivationStatusResponse getUserActivationStatus(ActivationStatusRequest request, AuditContext audit) {
		ActivationStatusResponse response = new ActivationStatusResponse();
		AmMsuser user;
		if (StringUtils.isNumeric(request.getLoginId())) {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
		} else {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		}
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		List<MsVendorRegisteredUser> listVru = null;
		if (!StringUtils.isNumeric(request.getLoginId())) {
			listVru = daoFactory.getVendorRegisteredUserDao()
					.getListVendorRegisteredUserBySignerRegisteredEmail(request.getLoginId());
		} else if (StringUtils.isNumeric(request.getLoginId())) {
			listVru = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByPhone(request.getLoginId());
		}

		if (null == listVru || listVru.isEmpty()) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		for (MsVendorRegisteredUser vru : listVru) {
			String activeStatus = ("1".equals(vru.getIsActive()) ? "1" : "0");
			String registerStatus = ("1".equals(vru.getIsRegistered()) ? "1" : "0");
			if (null == response.getActiveStatus() && null == response.getRegistrationStatus()) {
				response.setActiveStatus(activeStatus);
				response.setRegistrationStatus(registerStatus);
				if (response.getActiveStatus().equals("1") && response.getRegistrationStatus().equals("1")) {
					break;
				}
			} else if (response.getActiveStatus().equals("0") && response.getRegistrationStatus().equals("0")) {
				response.setActiveStatus(activeStatus);
				response.setRegistrationStatus(registerStatus);
				if (response.getActiveStatus().equals("1") && response.getRegistrationStatus().equals("1")) {
					break;
				}
			} else if (response.getActiveStatus().equals("0") && response.getRegistrationStatus().equals("1")) {
				response.setActiveStatus(activeStatus);
				response.setRegistrationStatus(registerStatus);
				if (response.getActiveStatus().equals("1") && response.getRegistrationStatus().equals("1")) {
					break;
				}
			}
		}

		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);

		return response;
	}

	@Override
	public EmailServiceStatusResponse checkUserEmailService(EmailServiceStatusRequest request, AuditContext audit) {
		MsVendorRegisteredUser vRUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(request.getLoginId(), request.getPsreCode());

		if (null == vRUser) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND, null,
							this.retrieveLocaleAudit(audit)),
					ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}

		EmailServiceStatusResponse response = new EmailServiceStatusResponse();
		response.setEmailServiceStatus((null == vRUser.getEmailService()) ? "0" : vRUser.getEmailService());
		return response;
	}

	@Override
	public EmailServiceStatusResponse checkUserEmailServiceEmbed(EmailServiceStatusEmbedRequest request,
			AuditContext audit) {
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}

		MsVendorRegisteredUser vRUser;
		if (StringUtils.isNumeric(request.getLoginId())) {
			vRUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(request.getLoginId(),
					request.getPsreCode());
			if (null != vRUser) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_ALREADY_EXISTED,
								new String[] { request.getLoginId() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.PHONE_NO_ALREADY_EXIST);
			}
		} else {
			vRUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(request.getLoginId(),
					request.getPsreCode());
			if (null != vRUser) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_EMAIL_ALREADY_EXISTED,
								new String[] { request.getLoginId() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.ALREADY_REGISTERED);
			}
		}

		return new EmailServiceStatusResponse();
	}

	@Override
	public PreRegisterResponse preRegisterEsignUser(PreRegisterRequest request, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(request.getEmail());

		// Untuk handle case user gagal register DIGISIGN dan sudah melakukan
		// preregister
		if (null != user) {
			MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), request.getVendorCode());

			if ("1".equals(vendorUser.getIsActive())) {
				throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_REGISTERED,
						null, this.retrieveLocaleAudit(audit)), ReasonUser.ALREADY_REGISTERED);
			}
			return new PreRegisterResponse();
		}

		AmUserPersonalData personalData = daoFactory.getUserDao().getUserDataByPhone(request.getPhone());
		if (null != personalData) {
			throw new UserException(this.messageSource.getMessage("businesslogic.usermanagement.phonealreadyexist",
					null, this.retrieveLocaleAudit(audit)), ReasonUser.PHONE_NO_ALREADY_EXIST);
		}

		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new String[] { CONST_TENANT }, this.retrieveLocaleAudit(audit)), ReasonUser.TENANT_NOT_FOUND);
		}

		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(msgBean.getOfficeCode(),
				tenant.getTenantCode());
		if (null == office) {
			office = new MsOffice();
			office.setOfficeCode(StringUtils.upperCase(msgBean.getOfficeCode()));
			office.setIsActive("1");
			office.setOfficeName(msgBean.getOfficeName());
			office.setMsTenant(tenant);
			office.setUsrCrt(GlobalVal.CONST_CONFINS);
			office.setDtmCrt(new Date());
			office.setMsRegion(null);
			daoFactory.getOfficeDao().insertOffice(office);
		}

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		if (null == vendor) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new String[] { CONST_VENDOR }, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_NOT_FOUND);
		}

		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(GlobalVal.ROLE_BM_MF,
				msgBean.getTenantCode());
		if (null == role) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new String[] { "Role" }, this.retrieveLocaleAudit(audit)), ReasonUser.ROLE_NOT_FOUND);
		}

		if (StringUtils.isNotBlank(request.getNik()) && request.getNik().length() != 16) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_NIK_LENGTH, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_NIK_LENGTH);
		}

		user = new AmMsuser();
		String password = this.generateRandomPassword();

		SignerBean signerBean = new SignerBean();
		signerBean.setLoginId(request.getEmail());
		signerBean.setUserName(request.getFullname());
		signerBean.setUserPhone(request.getPhone());
		signerBean.setIdNo(request.getNik());

		this.insertNewUser(user, signerBean, password, office, audit);
		this.insertNewUserPersonalData(user, signerBean, audit); // cuma insert sebagian data, beberapa di-insert API
																	// register
		this.insertPasswordHist(user, audit);
		this.insertRoleNewUser(user, role, audit);
		this.insertUserofTenant(user, tenant, audit);

		MsVendorRegisteredUser registeredUser = new MsVendorRegisteredUser();
		registeredUser.setSignerRegisteredEmail(user.getLoginId());
		registeredUser.setIsActive("0");
		registeredUser.setIsRegistered("0");
		registeredUser.setUsrCrt(GlobalVal.CONST_CONFINS);
		registeredUser.setDtmCrt(new Date());
		registeredUser.setAmMsuser(user);
		registeredUser.setMsVendor(vendor);
		vendorLogic.insertVendorRegisteredUser(registeredUser);

		// send email info user
		this.sendAccountInfoEmail(user.getFullName(), user.getLoginId(), password);

		return new PreRegisterResponse();
	}

	@Override
	public void updateUserPersonalData(UserBean userBean, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(userBean.getEmail());
		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);

		String gender = "";
		if (userBean.getUserGender().equals(GlobalVal.CODE_DIGISIGN_MALE)) {
			gender = GlobalVal.CODE_LOV_MALE;
		} else if (userBean.getUserAddress().equals(GlobalVal.CODE_DIGISIGN_FEMALE)) {
			gender = GlobalVal.CODE_LOV_FEMALE;
		}

		ZipcodeCityBean zipcodeBean = new ZipcodeCityBean();
		zipcodeBean.setProvinsi(StringUtils.upperCase(userBean.getProvinsi()));
		zipcodeBean.setKelurahan(StringUtils.upperCase(userBean.getKelurahan()));
		zipcodeBean.setKecamatan(StringUtils.upperCase(userBean.getKecamatan()));
		zipcodeBean.setKota(StringUtils.upperCase(userBean.getKota()));
		zipcodeBean.setZipcode(StringUtils.upperCase(userBean.getZipcode()));
		personalDataBean.getUserPersonalData().setZipcodeBean(zipcodeBean);
		personalDataBean.getUserPersonalData().setGender(StringUtils.upperCase(gender));
		personalDataBean.getUserPersonalData().setPlaceOfBirth(StringUtils.upperCase(userBean.getUserPob()));
		personalDataBean.getUserPersonalData()
				.setDateOfBirth(MssTool.formatStringToDate(userBean.getUserDob(), GlobalVal.DATE_FORMAT));
		personalDataBean.getUserPersonalData().setEmail(StringUtils.upperCase(userBean.getEmail()));

		personalDataBean.setIdNoRaw(StringUtils.upperCase(userBean.getIdNo()));
		personalDataBean.setPhoneRaw(userBean.getUserPhone());
		personalDataBean.setAddressRaw(StringUtils.upperCase(userBean.getUserAddress()));

		if (StringUtils.isNotBlank(userBean.getIdPhoto())) {
			byte[] idPhoto = MssTool.imageStringToByteArray(userBean.getIdPhoto());
			personalDataBean.setPhotoIdRaw(idPhoto);
		}

		if (StringUtils.isNotBlank(userBean.getSelfPhoto())) {
			byte[] selfPhoto = MssTool.imageStringToByteArray(userBean.getSelfPhoto());
			personalDataBean.setSelfPhotoRaw(selfPhoto);
		}

		personalDataBean.getUserPersonalData().setDtmUpd(new Date());
		personalDataBean.getUserPersonalData().setUsrUpd(audit.getCallerId());
		daoFactory.getUserDao().updateUserPersonalData(personalDataBean);
	}

	@Override
	public AmMsuser getUserByPhone(String phone, AuditContext audit) {
		return daoFactory.getUserDao().getActiveUserByPhone(phone);
	}

	@Override
	public AmMsuser getUserByOtpCode(String otpCode, AuditContext audit) {
		return daoFactory.getUserDao().getUserByOtpCode(otpCode);
	}

	@Override
	public LinkResetPasswordResponse getResetPasswordLink(LinkResetPasswordRequest request, AuditContext audit) {
		LinkResetPasswordResponse response = new LinkResetPasswordResponse();
		Status status = new Status();
		if (StringUtils.isBlank(request.getResetPasswordCode())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "ResetPasswordCode" }, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}

		AmMsuser user = this.getUserByOtpCode(request.getResetPasswordCode(), audit);
		if (null == user) {
			status.setCode(StatusCode.RESET_PASSWORD_CODE_INVALID);
			status.setMessage(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_INCORRECT_RESET_PASSWORD_LINK_CODE,
							new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
		} else if (user.getOtpCode().equalsIgnoreCase(request.getResetPasswordCode())) {
			response.setResetPasswordLink(user.getVendorResetPassLink());
			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
			response.setStatus(status);

			user.setOtpCode(null);
			user.setVendorResetPassLink(null);
			daoFactory.getUserDao().updateUser(user);
		}

		return response;
	}

	@Override
	public RegistrationResponse registerUser(RegistrationRequest regisRequest, AuditContext audit) throws IOException {
		return regisUser(regisRequest, null, null, false, audit);
	}

	@Override
	public RegistrationResponse registerUserEmbed(RegistrationEmbedRequest regisRequest, AuditContext audit)
			throws IOException {

		if (StringUtils.isBlank(regisRequest.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(regisRequest.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(msgBean.getOfficeCode(),
				msgBean.getTenantCode());
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == office) {
			office = new MsOffice();
			office.setOfficeCode(StringUtils.upperCase(msgBean.getOfficeCode()));
			office.setIsActive("1");
			office.setOfficeName(msgBean.getOfficeName());
			office.setMsTenant(tenant);
			office.setUsrCrt(audit.getCallerId());
			office.setDtmCrt(new Date());
			daoFactory.getOfficeDao().insertOffice(office);
		}

		RegistrationRequest request = new RegistrationRequest();
		request.setUserData(regisRequest.getUserData());
		request.setVendorCode(regisRequest.getVendorCode());
		request.setTenantCode(msgBean.getTenantCode());

		return regisUser(request, GlobalVal.INV_BY_EMAIL, GlobalVal.ROLE_BM_MF, false, audit);
	}

	private void validateIdNo(String idNo,String vendorCode, String loginId, String phone, AuditContext audit) {
		AmMsuser userLogin = daoFactory.getUserDao().getUserByLoginId(loginId);
		AmMsuser userPhone = daoFactory.getUserDao().getActiveUserByPhone(phone);
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(idNo);
		List<Map<String,Object>> idVendor = daoFactory.getVendorDao().getVendorByIdNo(idNo);
		Iterator<Map<String,Object>> itr = idVendor.iterator();
		
		String idLogin = StringUtils.upperCase(loginId);
		
		
		if (StringUtils.isBlank(idNo)) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] { "NIK" }, this.retrieveLocaleAudit(audit)), ReasonUser.NIK_EMPTY);
		}
		
		if (StringUtils.isBlank(idLogin)) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] { CONST_EMAIL }, this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_EMPTY);
		}
		
		if (StringUtils.isBlank(phone)) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] { "No HP" }, this.retrieveLocaleAudit(audit)), ReasonUser.PHONE_NO_EMPTY);
		}
		
		if(null != userLogin ) {
			// login id sama Nik beda	
			if (userLogin.getLoginId().equals(idLogin) && !MssTool.getHashedString(idNo).equals(userLogin.getHashedIdNo())) {
				throw new UserException(this.messageSource.getMessage("businesslogic.usermanagement.idloginexist",
						null, this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_UNIQUE);
			}
			
		}
			
		if(null != userPhone) {
			// no hp sama NIK beda 		
			if (MssTool.getHashedString(phone).equals(userPhone.getHashedPhone()) && !MssTool.getHashedString(idNo).equals(userPhone.getHashedIdNo())) {
				throw new UserException(this.messageSource.getMessage("businesslogic.usermanagement.phonealreadyexist",
						null, this.retrieveLocaleAudit(audit)), ReasonUser.PHONE_NO_ALREADY_EXIST);
			}
		}
		
		if(null != user) {
			// email sama  NIK beda		
			if(user.getLoginId().equals(idLogin) && !MssTool.getHashedString(idNo).equals(userLogin.getHashedIdNo())) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NIK_REGISTERED,
								null, this.retrieveLocaleAudit(audit)),
						ReasonUser.NIK_ALREADY_EXIST);
			}
		}
		
		
		while(itr.hasNext()) {
			
			Map<String,Object> map = itr.next();
			
			if(null != user) {
				if (MssTool.getHashedString(idNo).equals(user.getHashedIdNo()) && vendorCode.equals(map.get("d0"))) {
					throw new UserException(
							this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NIK_REGISTERED,
									null, this.retrieveLocaleAudit(audit)),
							ReasonUser.NIK_ALREADY_EXIST);
				}
			}
			
			if (null != userPhone) {
				if (MssTool.getHashedString(phone).equals(userPhone.getHashedPhone()) && vendorCode.equals(map.get("d0"))) {
					throw new UserException(
							this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NIK_REGISTERED,
									null, this.retrieveLocaleAudit(audit)),
							ReasonUser.NIK_ALREADY_EXIST);
				}
			}
			
			if (null != user) {
				if (user.getLoginId().equals(idLogin) && vendorCode.equals(map.get("d0"))) {
					throw new UserException(
							this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NIK_REGISTERED,
									null, this.retrieveLocaleAudit(audit)),
							ReasonUser.NIK_ALREADY_EXIST);
				}	
			}
		}
	}

	private RegistrationResponse regisUser(RegistrationRequest regisRequest, String invitationBy, String roleCode,
			boolean isRegisByInv, AuditContext audit) throws IOException {
		Date dob = MssTool.formatStringToDate(regisRequest.getUserData().getUserDob(), GlobalVal.DATE_FORMAT);
		prepareDataRegister(regisRequest, audit);

		RegisterDigisignResponseBean resultBean = digisignLogic.sendDataRegister(regisRequest, audit);
		handleDigisignRegisterResponse(resultBean, regisRequest.getUserData().getUserPhone(),
				regisRequest.getUserData().getEmail(), invitationBy, regisRequest.getVendorCode(), audit);

		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultBean.getResult())
				|| GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(resultBean.getNotif())
				|| GenericDigisignLogic.DIGI_REGISTERED_MSG.equalsIgnoreCase(resultBean.getNotif())) {
			// Format dob untuk Digisign diubah ke dd-MM-yyyy, perlu ubah kembali ke
			// yyyy-MM-dd untuk eSign
			regisRequest.getUserData().setUserDob(MssTool.formatDateToStringIn(dob, GlobalVal.DATE_FORMAT));

			if (GlobalVal.INV_BY_EMAIL.equals(invitationBy) || GlobalVal.INV_BY_SMS.equals(invitationBy)) {
				boolean isVendorUserActive = false;
				if (GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(resultBean.getNotif())) {
					LOG.info("Change Email from {} to {} (registered on Digisign)",
							regisRequest.getUserData().getEmail(), resultBean.getEmailRegistered());
					regisRequest.getUserData().setEmail(resultBean.getEmailRegistered() + CONST_OFF);
					isVendorUserActive = true;

					// Panggil ulang API register Digisign dengan email yang sudah terdaftar
					String newEmail = resultBean.getEmailRegistered();
					RegistrationRequest newRequest = new RegistrationRequest();
					newRequest.setTenantCode(regisRequest.getTenantCode());
					newRequest.setVendorCode(regisRequest.getVendorCode());
					newRequest.setUserData(UserBean.newInstance(regisRequest.getUserData()));
					newRequest.getUserData().setEmail(newEmail);

					Date userDob = MssTool.formatStringToDate(newRequest.getUserData().getUserDob(),
							GlobalVal.DATE_FORMAT);
					String userDobDigisign = MssTool.formatDateToStringIn(userDob, GlobalVal.DATE_FORMAT_DASH_IN);
					newRequest.getUserData().setUserDob(userDobDigisign);

					digisignLogic.sendDataRegister(newRequest, audit);
				}
				if (GenericDigisignLogic.DIGI_REGISTERED_MSG.equals(resultBean.getNotif())) {
					isVendorUserActive = true;

					// Add to queue for updating tr_error_history.rerun_process later
					MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(regisRequest.getVendorCode());
					UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(
							regisRequest.getUserData().getIdNo(), vendor.getIdMsVendor());
					QueuePublisher.queueUpdErrHistRerunProcess(bean);
				}

				insertUserAfterRegister(regisRequest, invitationBy, isRegisByInv, roleCode, isVendorUserActive, audit);
			}

			if (!GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(resultBean.getNotif())
					&& !GenericDigisignLogic.DIGI_REGISTERED_MSG.equals(resultBean.getNotif())) {
				this.updateUserPersonalData(regisRequest.getUserData(), audit);
				this.updateUserRegisterStatus(regisRequest.getUserData().getEmail(), regisRequest.getVendorCode(), "1");
				// save image to user personal data
				saveUserPhoto(regisRequest.getUserData().getEmail(), regisRequest.getUserData().getIdPhoto(),
						regisRequest.getUserData().getSelfPhoto(), audit);

				String isVendorEkyc = vendorLogic.getVendorEKYCStatus(regisRequest.getTenantCode(),
						regisRequest.getVendorCode(), audit);
				if ("1".equals(isVendorEkyc)) {
					insertBalanceMutationVerif(regisRequest.getVendorCode(), regisRequest.getTenantCode(),
							regisRequest.getUserData().getEmail(), audit);
				}
			}
		} else {
			this.cutFailedVerifBalance(resultBean, regisRequest, audit);
		}

		int statusCode = 0;
		Status statusDigisign = new Status();
		try {
			statusCode = Integer.parseInt(resultBean.getResult());
		} catch (NumberFormatException ne) {
			statusCode = StatusCode.DIGISIGN_FAILED;
			LOG.error("Error API DIGISIGN with code : {}, with message : {}", resultBean.getResult(),
					resultBean.getNotif());
		}

		statusDigisign.setCode(statusCode);

//		if (statusCode == 0) {
//			resultBean.setNotif("Harap untuk tetap membuka halaman ini dan melanjutkan aktivasi akun.");
//		}

		String messageFormat = StringUtils.isEmpty(resultBean.getInfo()) ? "%1$s" : "%1$s (%2$s)";
		String message = String.format(messageFormat, resultBean.getNotif(), resultBean.getInfo());

		if (14 == statusCode && GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(resultBean.getNotif())) {
			message = this.messageSource.getMessage("businesslogic.user.nikalreadyregistered",
					new String[] { regisRequest.getUserData().getIdNo(), resultBean.getEmailRegistered() },
					this.retrieveLocaleAudit(audit));
		}

		statusDigisign.setMessage(message);
		RegistrationResponse registrationResponse = new RegistrationResponse();
		registrationResponse.setStatus(statusDigisign);
		return registrationResponse;
	}

	private void handleDigisignRegisterResponse(RegisterDigisignResponseBean response, String receiverDetail,
			String email, String invitationBy, String vendorCode, AuditContext audit) {
		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(response.getResult())
				&& "Email sudah terdaftar, namun belum melakukan aktivasi. Silahkan untuk melakukan aktivasi sebelum data dihapus dari daftar aktivasi."
						.equals(response.getNotif())) {
			// ESG-933: Handling response sudah terdaftar, tapi belum aktivasi
			throw new DigisignException(this.messageSource.getMessage(MSG_DIGI_REGISTERED_NOTACTIVATED,
					null, this.retrieveLocaleAudit(audit)));
		}

		if (null != response.getData() && "12".equals(response.getResult())) {
			RegisterDigisignResponseDataBean verifResult = response.getData();
			// Kalau NIK, Nama, dan birthdate true akan return string kosong dan tidak ganti
			// notif
			String notif = digisignLogic.buildRegisterVerifMessage(verifResult);
			String userDetail = null;
			if (invitationBy.equals("SMS")) {
				userDetail = invLinkLogic.getDataUserInvitation(receiverDetail, vendorCode, audit);
			} else if (invitationBy.equals(CONST_EMAIL)) {
				userDetail = invLinkLogic.getDataUserInvitation(email, vendorCode, audit);
			}

			if (null != response.getInfo() && response.getInfo().equals(GlobalVal.CONST_VERIFIKASI_WAJAH_GAGAL)) {
				notif = "Verifikasi user gagal. (Verifikasi wajah gagal)";
			}
			if (null != response.getInfo() && (response.getInfo().equals("No face found on KTP")
					|| response.getInfo().equals("No face found onKTP"))) {
				notif = "Verifikasi user gagal. Tidak ditemukan foto wajah pada KTP";
			}
			if (null != response.getInfo() && (response.getInfo().equals("No face found on Selfie")
					|| response.getInfo().equals("No face found onSelfie"))) {
				notif = GlobalVal.CONST_VERIFIKASI_USER_GAGAL_TIDAK_DITEMUKAN_FOTO_WAJAH;
			}
			if (null != response.getInfo() && (response.getInfo()
					.equals("Verifikasi gagal Pastikan cahaya cukup dan foto ktp sesuai dengan foto wajah")
					|| response.getInfo()
							.equals("Verifikasi gagal Pastikan cahayacukup dan foto ktp sesuai dengan foto wajah"))) {
				notif = GlobalVal.CONST_VERIFIKASI_USER_GAGAL_TIDAK_DITEMUKAN_FOTO_WAJAH;
			}

			notif = notif.replace("user", "user " + userDetail);
			if (StringUtils.isNotBlank(notif)) {
				response.setNotif(notif);
			}
		}
	}

	private void handleDigisignRegisterResponseV2(RegisterDigisignResponseBean response, String receiverDetail,
			String email, String invitationBy, String vendorCode, AuditContext audit) {
		if (null != response.getData() && "12".equals(response.getResult())) {
			RegisterDigisignResponseDataBean verifResult = response.getData();
			// Kalau NIK, Nama, dan birthdate true akan return string kosong dan tidak ganti
			// notif
			String notif = digisignLogic.buildRegisterVerifMessage(verifResult);
			String userDetail = null;
			if (invitationBy.equals("SMS")) {
				userDetail = invLinkLogic.getDataUserInvitation(receiverDetail, vendorCode, audit);
			} else if (invitationBy.equals(CONST_EMAIL)) {
				userDetail = invLinkLogic.getDataUserInvitation(email, vendorCode, audit);
			}

			if (null != response.getInfo() && response.getInfo().equals(GlobalVal.CONST_VERIFIKASI_WAJAH_GAGAL)) {
				notif = "Verifikasi user gagal. (Verifikasi wajah gagal)";
			}
			if (null != response.getInfo() && (response.getInfo().equals("No face found on KTP")
					|| response.getInfo().equals("No face found onKTP"))) {
				notif = "Verifikasi user gagal. Tidak ditemukan foto wajah pada KTP";
			}
			if (null != response.getInfo() && (response.getInfo().equals("No face found on Selfie")
					|| response.getInfo().equals("No face found onSelfie"))) {
				notif = GlobalVal.CONST_VERIFIKASI_USER_GAGAL_TIDAK_DITEMUKAN_FOTO_WAJAH;
			}
			if (null != response.getInfo() && (response.getInfo()
					.equals("Verifikasi gagal Pastikan cahaya cukup dan foto ktp sesuai dengan foto wajah")
					|| response.getInfo()
							.equals("Verifikasi gagal Pastikan cahayacukup dan foto ktp sesuai dengan foto wajah"))) {
				notif = GlobalVal.CONST_VERIFIKASI_USER_GAGAL_TIDAK_DITEMUKAN_FOTO_WAJAH;
			}

			notif = notif.replace("user", "user " + userDetail);
			if (StringUtils.isNotBlank(notif)) {
				response.setNotif(notif);
			}
		}
	}

	private void cutFailedVerifBalance(RegisterDigisignResponseBean response, RegistrationRequest request,
			AuditContext audit) {
		String verifGagalNotif = "Verifikasi user gagal";

		if (!"12".equals(response.getResult()) || !StringUtils.containsIgnoreCase(response.getNotif(), verifGagalNotif)
				|| "verifikasi text gagal.".equalsIgnoreCase(response.getInfo())) {
			LOG.info(CONST_INSERT_VERIFICATION_BALMUT_SKIP);
			return;
		}

		/*
		 * Response verifikasi user gagal yang di-handle merujuk ke link:
		 * https://drive.google.com/file/d/10AgBwXbO483lAAjywmhZfvZWxqp_-5Or/view?usp=
		 * sharing Di response register no 10 ada case info kosong / null
		 */

		/*
		 * ESG-921 Response register no 10 seharusnya tidak potong saldo result: 12
		 * notif: verifikasi user gagal. NIK, Nama, dan Tanggal Lahir tidak sesuai data:
		 * NIK false, Nama false, Tanggal lahir false info: empty
		 */
		if (null != response.getData() && Boolean.FALSE.equals(response.getData().getNik())
				&& Boolean.FALSE.equals(response.getData().getName())
				&& Boolean.FALSE.equals(response.getData().getBirthdate()) && StringUtils.isBlank(response.getInfo())) {
			LOG.info(CONST_INSERT_VERIFICATION_BALMUT_SKIP);
			return;
		}

		if ("System found more than 1 face on selfie photo".equalsIgnoreCase(response.getInfo())
				|| "Error, can't find face on selfie/KTP image. Make sure clearly foto and image is not inverted"
						.equalsIgnoreCase(response.getInfo())
				|| "Can't identify KTP".equalsIgnoreCase(response.getInfo())
				|| "No face found on KTP image".equalsIgnoreCase(response.getInfo())
				|| GlobalVal.CONST_VERIFIKASI_WAJAH_GAGAL.equalsIgnoreCase(response.getInfo())) {

			insertFailedVerifBalanceMutation(request.getTenantCode(), request.getVendorCode(),
					request.getUserData().getEmail(), request.getUserData().getUserName(),
					request.getUserData().getUserPhone(), audit);
			return;
		}
		LOG.warn("Unhandled failed verification info: {}", response.getInfo());
	}

	private void cutFailedVerifBalanceV2(RegisterDigisignResponseBean response, DigisignRegisterRequest request,
			AuditContext audit) {
		String verifGagalNotif = "Verifikasi user gagal";

		if (!"12".equals(response.getResult()) || !StringUtils.containsIgnoreCase(response.getNotif(), verifGagalNotif)
				|| "verifikasi text gagal.".equalsIgnoreCase(response.getInfo())) {
			LOG.info(CONST_INSERT_VERIFICATION_BALMUT_SKIP);
			return;
		}

		/*
		 * Response verifikasi user gagal yang di-handle merujuk ke link:
		 * https://drive.google.com/file/d/10AgBwXbO483lAAjywmhZfvZWxqp_-5Or/view?usp=
		 * sharing Di response register no 10 ada case info kosong / null
		 */

		/*
		 * ESG-921 Response register no 10 seharusnya tidak potong saldo result: 12
		 * notif: verifikasi user gagal. NIK, Nama, dan Tanggal Lahir tidak sesuai data:
		 * NIK false, Nama false, Tanggal lahir false info: empty
		 */
		if (null != response.getData() && Boolean.FALSE.equals(response.getData().getNik())
				&& Boolean.FALSE.equals(response.getData().getName())
				&& Boolean.FALSE.equals(response.getData().getBirthdate()) && StringUtils.isBlank(response.getInfo())) {
			LOG.info(CONST_INSERT_VERIFICATION_BALMUT_SKIP);
			return;
		}

		if ("System found more than 1 face on selfie photo".equalsIgnoreCase(response.getInfo())
				|| "Error, can't find face on selfie/KTP image. Make sure clearly foto and image is not inverted"
						.equalsIgnoreCase(response.getInfo())
				|| "Can't identify KTP".equalsIgnoreCase(response.getInfo())
				|| "No face found on KTP image".equalsIgnoreCase(response.getInfo())
				|| GlobalVal.CONST_VERIFIKASI_WAJAH_GAGAL.equalsIgnoreCase(response.getInfo())) {

			insertFailedVerifBalanceMutation(request.getTenantCode(), request.getVendorCode(),
					request.getUserData().getEmail(), request.getUserData().getUserName(),
					request.getUserData().getUserPhone(), audit);
			return;
		}
		LOG.warn("Unhandled failed verification info: {}", response.getInfo());
	}

	private void insertFailedVerifBalanceMutation(String tenantCode, String vendorCode, String loginId, String name,
			String phone, AuditContext audit) {
		MsLov balanceTypeVrf = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxTypeVrf = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(tenantCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(loginId);

		Date trxDate = new Date();
		String refNo = StringUtils.EMPTY;
		int qty = -1;
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();

		String notes;
		if (null == user) {
			notes = "Verifikasi Biometrik Gagal Untuk " + name + " " + phone;
		} else {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
			notes = "Verifikasi Biometrik Gagal Untuk " + user.getFullName() + " " + personalData.getPhoneRaw();
		}

		saldoLogic.insertBalanceMutation(null, null, null, balanceTypeVrf, trxTypeVrf, tenant, vendor, trxDate, refNo,
				qty, String.valueOf(nextTrxNo), user, notes, null, audit);

	}

	private void insertUserAfterRegister(RegistrationRequest regisRequest, String invitationBy, boolean isRegisByInv,
			String roleCode, boolean isVendorUserActive, AuditContext audit) throws IOException {
		// Userbean to SignerBean untuk parameter insert user
		String email = regisRequest.getUserData().getEmail();
		regisRequest.getUserData().setEmail(!email.contains(CONST_OFF) ? email : email.substring(0, email.length() - 5));
		String userBeanJson = gson.toJson(regisRequest.getUserData());
		SignerBean signerBean = gson.fromJson(userBeanJson, SignerBean.class);

		signerBean.setLoginId(regisRequest.getUserData().getEmail());
		if (GlobalVal.INV_BY_SMS.equals(invitationBy)) {
			signerBean.setEmailService("1");
			if (email.contains(CONST_OFF)) {
				signerBean.setEmailService("0");
			}
		} else if (GlobalVal.INV_BY_EMAIL.equals(invitationBy)) {
			signerBean.setEmailService("0");
		}
		// Convert gender format
		if (GlobalVal.CODE_DIGISIGN_MALE.equals(regisRequest.getUserData().getUserGender())) {
			signerBean.setUserGender(GlobalVal.CODE_LOV_MALE);
		} else if (GlobalVal.CODE_DIGISIGN_FEMALE.equals(regisRequest.getUserData().getUserGender())) {
			signerBean.setUserGender(GlobalVal.CODE_LOV_FEMALE);
		}

		String randomPass = this.generateRandomPassword();
		AmMsuser newUser = new AmMsuser();
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(regisRequest.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(regisRequest.getVendorCode());
		MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(roleCode, regisRequest.getTenantCode());

		if (isRegisByInv) {
			this.insertNewUserInv(newUser, signerBean, office, audit);
		} else {
			this.insertNewUser(newUser, signerBean, randomPass, office, audit);
		}
		this.insertNewUserPersonalData(newUser, signerBean, audit);
		if (!isRegisByInv) {
			this.insertPasswordHist(newUser, audit);
		}
		this.insertRoleNewUser(newUser, role, audit);
		this.insertUserofTenant(newUser, tenant, audit);

		MsVendorRegisteredUser vendorUser = new MsVendorRegisteredUser();
		vendorUser.setSignerRegisteredEmail(newUser.getLoginId());

		if (isVendorUserActive || email.contains(CONST_OFF)) {
			vendorUser.setIsActive("1");
		} else {
			vendorUser.setIsActive("0");
		}

		vendorUser.setUsrCrt(audit.getCallerId());
		vendorUser.setDtmCrt(new Date());
		vendorUser.setAmMsuser(newUser);
		vendorUser.setMsVendor(vendor);
		vendorUser.setIsRegistered("1");
		vendorLogic.insertVendorRegisteredUser(vendorUser);

		// Send Account Info by SMS or Email
		if (GlobalVal.INV_BY_SMS.equals(invitationBy) && !isRegisByInv) {
			sendAccountInfoSms(regisRequest.getUserData().getUserPhone(), newUser.getInitialName(), randomPass, tenant,
					vendor, newUser, audit);
		} else if (GlobalVal.INV_BY_EMAIL.equals(invitationBy) && !isRegisByInv) {
			sendAccountInfoEmail(newUser.getFullName(), newUser.getLoginId(), randomPass);
		}
	}

	private void sendAccountInfoEmail(String fullname, String email, String password) {

		Map<String, Object> userMap = new HashMap<>();
		userMap.put("email", email);
		userMap.put(AmMsuser.PASSWORD_HBM, password);
		userMap.put(FULLNAME, fullname);
		userMap.put("link", loginUrl);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userMap);

		MsMsgTemplate msg = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INFO_AKUN, templateParameters);

		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setBodyMessage(msg.getBody());
		emailInfo.setSubject(msg.getSubject());
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(new String[] { email });

		try {
			emailSenderLogic.sendEmail(emailInfo, null);
		} catch (Exception e) {
			LOG.error("Send account info email error", e);
		}
	}

	private void sendAccountInfoSms(String phone, String initialName, String password, MsTenant tenant, MsVendor vendor,
			AmMsuser newUser, AuditContext audit) {

		Map<String, Object> userParam = new HashMap<>();
		userParam.put("initial", initialName);
		userParam.put(AmMsuser.PASSWORD_HBM, password);
		userParam.put("link", loginUrl);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userParam);

		MsMsgTemplate template = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INFO_AKUN_SMS,
				templateParameters);
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phone, template.getBody(), tenant);
		SendSmsResponse response = smsLogic.sendSms(sendSmsValueFirstRequestBean);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		String notes = phone + " : Send Account Info SMS";

		if (response.getErrorCode() == null || 
				(!response.getErrorCode().equals(VFIRST_ERR28682)
				&& !response.getErrorCode().equals(VFIRST_ERR28681)
				&& !response.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					-1, String.valueOf(response.getTrxNo()), newUser, notes, response.getGuid(), audit);
		} else {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					0, String.valueOf(response.getTrxNo()), newUser, notes + GlobalVal.BALMUT_ERROR + response.getErrorCode(),
					response.getGuid(), audit);
		}
	}

	@Override
	public ActivationLinkResponse getActivationLink(ActivationLinkRequest request, AuditContext audit) {
		return this.getActLink(request, audit);
	}

	@Override
	public ActivationLinkResponse getActivationLinkEmbed(ActivationLinkEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		ActivationLinkRequest actLinkRequest = new ActivationLinkRequest();
		if (StringUtils.isNotBlank(request.getEmail())) {
			actLinkRequest.setEmail(request.getEmail());
		} else {
			actLinkRequest.setEmail(msgBean.getEmail());
		}
		actLinkRequest.setVendorCode(request.getVendorCode());
		actLinkRequest.setTenantCode(msgBean.getTenantCode());

		return this.getActLink(actLinkRequest, audit);
	}

	@Override
	public ResendActLinkResponse resendActivationLinkForInvi(ResendActLinkForInviRequest request, AuditContext audit) {
		MsVendor vendor = vendorLogic.getVendorByCode(request.getVendorCode(), audit);
		MsTenant tenant = tenantLogic.getTenantByCode(request.getTenantCode(), audit);
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao()
				.getInvitationLinkByRecieverDetailAndIdMsVendor(request.getReceiverDetail(), vendor.getIdMsVendor());
		if (invLink == null) {
			throw new ServicesUserException(this.messageSource.getMessage(
					GlobalKey.MESSAGE_ERROR_INV_LINK_NOT_EXIST_WITH_DATA, null, this.retrieveLocaleAudit(audit)));
		}
		
		MsVendorRegisteredUser vuser;
		if (GlobalVal.INV_BY_SMS.equals(invLink.getInvitationBy())) {
			vuser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByPhoneAndVendorCode(request.getReceiverDetail(),
							request.getVendorCode());
		} else {
			vuser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getReceiverDetail(),
							request.getVendorCode());
		}
		
		if (vuser == null) {
			throw new ServicesUserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)));
		}
		return this.resendActLink(vuser, vendor, tenant, audit);

	}

	@Override
	public ResendActLinkResponse resendActivationLinkForUser(ResendActLinkForUserRequest request, AuditContext audit) {
		MsVendor vendor = vendorLogic.getVendorByCode(request.getVendorCode(), audit);
		MsTenant tenant = tenantLogic.getTenantByCode(request.getTenantCode(), audit);
		MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getSignerEmail(),
						request.getVendorCode());
		if (vuser == null) {
			throw new ServicesUserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)));
		}
		return this.resendActLink(vuser, vendor, tenant, audit);
	}

	private ResendActLinkResponse resendActLink(MsVendorRegisteredUser vuser, MsVendor vendor, MsTenant tenant,
			AuditContext audit) {
		ResendActLinkResponse response = new ResendActLinkResponse();
		if ("0".equals(vuser.getIsRegistered())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED,
					new String[] { "User" }, this.retrieveLocaleAudit(audit)), ReasonUser.NOT_REGISTERED);
		}
		if ("1".equals(vuser.getIsActive())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_ACTIVATED,
					new String[] { "User" }, this.retrieveLocaleAudit(audit)), ReasonUser.ALREADY_REGISTERED);
		}
		if ("0".equals(vuser.getIsActive()) && "1".equals(vuser.getIsRegistered())) {

			if ("0".equals(vendor.getResendActivationLink())) {
				throw new VendorException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_CAN_NOT_RESEND_ACT_LINK,
								new String[] { vendor.getVendorCode() }, audit), ReasonVendor.VENDOR_CAN_NOT_RESEND_ACT_LINK);
			}
			
			// khusus teken aja
			if (vuser.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
				AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(vuser.getSignerRegisteredEmail(), true, audit);
				PersonalDataBean userbean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);

				TknajRegisterCekResponse responsetekenaja = tekenAjaLogic.registerCek(userbean.getIdNoRaw(),
						TekenAjaConstant.REGCEK_ACTION_RESEND_EMAIL, vendor, tenant, audit);

				if (responsetekenaja.getCode() != null) {
					if (responsetekenaja.getCode().equals(GlobalVal.TEKEN_REGCEK_USER_DO_NOT_EXISTS)) {
						throw new UserException(
								this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED,
										new String[] { "User" }, this.retrieveLocaleAudit(audit)),
								ReasonUser.NOT_REGISTERED);
					} else if (responsetekenaja.getCode().equals(GlobalVal.TEKEN_REGCEK_USER_EXIST_VERIFIED)) {
						throw new UserException(
								this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_ACTIVATED,
										new String[] { "User" }, this.retrieveLocaleAudit(audit)),
								ReasonUser.ALREADY_REGISTERED);
					}
				}
			} else {
				LOG.info("Get link aktivasi vendor lain");
			}
		}
		return response;
	}

	private ActivationLinkResponse getActLink(ActivationLinkRequest request, AuditContext audit) {
		ActivationLinkResponse response = new ActivationLinkResponse();
		Status status = new Status();

		UserBean resultBean = this.prepareDataActivationLink(request, audit);
		ActivationDigisignResponseBean resultActivationBean = digisignLogic.getActivationLink(resultBean,
				new AuditContext());

		int statusCode = 0;
		try {
			statusCode = Integer.parseInt(resultActivationBean.getResult());
		} catch (NumberFormatException ne) {
			statusCode = StatusCode.DIGISIGN_FAILED; // ERROR from DIGISN menggunakan trycatch karena bisa throw string,
														// padahal code
			LOG.error("Error API DIGISIGN with code : {}, with message : {}", resultActivationBean.getResult(),
					resultActivationBean.getNotif());
		}

		if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultActivationBean.getResult())) {
			String errorMessage;
			if ("14".equals(resultActivationBean.getResult())
					&& "Email sudah melakukan aktivasi".equals(resultActivationBean.getNotif())) {

				AmMsuser userInfo = daoFactory.getUserDao().getUserByLoginId(request.getEmail());

				PersonalDataBean userBean = daoFactory.getUserDao().getUserDataByIdMsUser(userInfo.getIdMsUser(), false);

				String detailUser = "(" + userBean.getIdNoRaw() + ", " + userInfo.getFullName() + ")";

				errorMessage = "Anda sudah selesai aktivasi, silahkan menunggu permintaan tanda tangan dikirimkan untuk lanjut ke proses berikutnya.";
				errorMessage = errorMessage.replace("Anda", "User " + detailUser);

				updateActivatedUser(request.getEmail(), request.getVendorCode(), audit);
			} else {
				errorMessage = this.messageSource.getMessage("businesslogic.user.erroractivation",
						new Object[] { resultActivationBean.getNotif() }, this.retrieveLocaleAudit(audit));
			}

			status.setCode(StatusCode.DIGISIGN_FAILED);
			status.setMessage(errorMessage);
			response.setStatus(status);
			return response;
		}

		status.setCode(statusCode);
		status.setMessage(resultActivationBean.getNotif());
		response.setUrl(resultActivationBean.getLink());
		response.setStatus(status);
		return response;
	}

	private void updateActivatedUser(String loginId, String vendorCode, AuditContext audit) {
		
		String validationMessage = "";
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(loginId, true, audit);
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendorCode);
		if (null == vendorUser) {
			LOG.warn("User with email {} is not found in ms_vendor_registered_user with vendor {}", user.getLoginId(),
					vendorCode);
			return;
		}
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_PRIVY_CERTIFICATE_EXPIRE_TIME);
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new Object[] {"Duration Certificate " + vendorCode}, audit);
		commonValidatorLogic.validateNotNull(gs, validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
		int certificateDurationVendor ;
		try {
			certificateDurationVendor = Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			throw new CommonException("Invalid value Certificate Duration", ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		Date activatedDate = new Date();
		Date expiredDate = DateUtils.addDays(activatedDate, certificateDurationVendor);
		expiredDate = DateUtils.addMinutes(expiredDate, -10);

		vendorUser.setIsRegistered("1");
		vendorUser.setIsActive("1");
		vendorUser.setActivatedDate(activatedDate);
		vendorUser.setCertExpiredDate(expiredDate);
		vendorUser.setUsrUpd(loginId);
		vendorUser.setDtmUpd(new Date());
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vendorUser);

		UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(personalData.getIdNoRaw(),
				vendorUser.getMsVendor().getIdMsVendor());
		QueuePublisher.queueUpdErrHistRerunProcess(bean);
	}

	private void updateUserRegisterStatus(String loginId, String vendorCode, String registerStatus) {
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(loginId, vendorCode);
		vendorUser.setIsRegistered(registerStatus);
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vendorUser);
	}

	private Status checkPhoneEmailNik(GenerateInvLinkRequest request, MsTenant tenant, boolean isV2,
			AuditContext audit) {
		Status status = new Status();
		Map<Integer, String> errors = new HashMap<>();

		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue()
				: regexPhone;
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue()
				: regexEmail;
		List<String> emails = new ArrayList<>();
		List<String> phones = new ArrayList<>();
		List<String> niks = new ArrayList<>();

		int j = 0;
		for (GenerateInvLinkUserBean bean : request.getUsers()) {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(bean.getVendorCode());
			try {
				if (StringUtils.isEmpty(bean.getVendorCode()) && isV2) {
					throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY, null,
							this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_EMPTY);
				}

				if (null == vendor && isV2) {
					throw new VendorException(messageSource.getMessage(MSG_VENDORCODE_INVALID,
							new Object[] { bean.getVendorCode() }, this.retrieveLocaleAudit(audit)),
							ReasonVendor.VENDOR_CODE_INVALID);
				}
				if ("0".equals(tenant.getEmailService()) && StringUtils.isBlank(bean.getEmail())) {
					throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_EMAIL, null,
							this.retrieveLocaleAudit(audit)), ReasonUser.EMAIL_EMPTY);
				}
				this.checkDuplicateNikInvLink(bean.getIdNo(), niks, audit);
				String resultEmail = this.checkDuplicateEmailInvLink(bean.getEmail(), emails, emailRegex, audit);
				if (StringUtils.isNotBlank(resultEmail)) {
					errors.put(j, resultEmail);
				}

				String resultPhone = this.checkDuplicatePhoneInvLink(bean.getUserPhone(), phones, phoneNoRegex,
						bean.getEmail(), audit);
				if (StringUtils.isNotBlank(resultPhone)) {
					errors.put(j, resultPhone);
				}

				if (isV2) {
					this.checkExistingNikEmailPhoneV2(bean.getUserPhone(), bean.getEmail(), bean.getIdNo(),
							bean.getVendorCode(), audit);
				} else {
					this.checkExistingNikEmailPhone(bean.getUserPhone(), bean.getEmail(), bean.getIdNo(), audit);
				}

			} catch (UserException | InvitationLinkException e) {
				
				String userName = null;
				if (StringUtils.isNotBlank(bean.getUserName())) {
					userName = bean.getUserName();
				} else {
					userName = StringUtils.isBlank(bean.getEmail()) ? bean.getUserPhone() :bean.getEmail();
				}
				
				audit.setCallerId(userName);
				insertGenerateInvErrorHistory(tenant, userName, GlobalVal.ERROR_TYPE_REJECT, e.getLocalizedMessage(),
						vendor, audit);
				status.setCode(e.getErrorCode());
				status.setMessage(e.getLocalizedMessage());
				return status;
			}

			j++;
		}

		List<UserBean> users = new LinkedList<>(Arrays.asList(request.getUsers()));
		for (int i = users.size() - 1; i >= 0; i--) {
			if (errors.containsKey(i)) {
				users.remove(i);
			}
		}

		if (users.isEmpty()) {
			status.setCode(9999);
			StringBuilder msg = new StringBuilder();
			for (int i = 0; i < errors.size(); i++) {
				if (StringUtils.isNotBlank(msg.toString())) {
					msg.append(" | " + errors.get(i));
				} else {
					msg.append(errors.get(i));
				}
			}
			status.setMessage(msg.toString());
			return status;
		}

		request.setUsers(users.toArray(new GenerateInvLinkUserBean[0]));

		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		return status;
	}

	private void checkExistingNikEmailPhone(String phone, String email, String nik, AuditContext audit) {

		AmMsuser nikUser = daoFactory.getUserDao().getUserByIdNo(nik);
		AmMsuser phoneUser = daoFactory.getUserDao().getActiveUserByPhone(phone);
		AmMsuser emailUser = daoFactory.getUserDao().getUserByLoginId(email);

		if (null != nikUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(nikUser.getIdMsUser(), false);

			if (null != phoneUser && null != emailUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& phoneUser.getLoginId().equals(emailUser.getLoginId())) {

				/*
				 * ESH-1236 NIK, HP, dan Email sudah digunakna untuk pengguna yang sama Throw
				 * error di bawah di-comment supaya bisa melanjutkan proses generate invitation
				 * link dan tidak memberhentikan proses NAP CONFINS
				 */

//				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.userdataused",
//				new String[] {email}, retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);

				return;
			}

			if (null != phoneUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& "1".equals(nikUser.getEmailService()) && StringUtils.isBlank(email)) {

				/*
				 * ESH-1236 Case serupa dengan yang di atas: NIK dan HP sudah digunakan untuk
				 * pengguna yang sama, pengguna tidak memiliki email pribadi Tidak throw error
				 * supaya bisa lanjut proses generate invitation link lainnya dan tidak
				 * memberhentikan proses NAP CONFINS
				 */
				return;
			}

			// Phone user matches NIK user
			if (null != phoneUser && nikUser.getLoginId().equals(phoneUser.getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednikphone",
						new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}

			// Email user matches NIK user
			if (null != emailUser && nikUser.getLoginId().equals(emailUser.getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						"businesslogic.invitationlink.emailnikusedinphone",
						new String[] { email, nik, MssTool.maskingString(personalData.getPhoneRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);
			}

			// REJECT
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednik",
					new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
					ReasonInvitationLink.DOUBLE_NIK);
		}

		if (null != emailUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(emailUser.getIdMsUser(), false);

			// Phone user matches Email user
			if (null != phoneUser && emailUser.getLoginId().equals(phoneUser.getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK,
						new String[] { email, phone, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
			}

			// REJECT
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK,
					new String[] { email, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
		}

		if (null != phoneUser) {
			// REJECT
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.phoneusedinemail",
					new String[] { phone, MssTool.maskEmailAddress(phoneUser.getLoginId(), 2) },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_PHONE);
		}
	}

	private void checkExistingNikEmailPhoneV2(String phone, String email, String nik, String vendorCode, AuditContext audit) {

		List<String> vendors = new ArrayList<>();
		if (StringUtils.isNotBlank(nik)) {
			vendors = daoFactory.getVendorRegisteredUserDao().getUserRegisteredVendorsByNik(nik);
		}
		
		AmMsuser nikUser = daoFactory.getUserDao().getUserByIdNo(nik);
		AmMsuser phoneUser = userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		AmMsuser emailUser = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		
		MsVendorRegisteredUser vruPhone = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		MsVendorRegisteredUser vruEmail = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendorCode);
		MsVendorRegisteredUser vruNik = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		
		TrInvitationLink invLinkByIdNo = StringUtils.isBlank(nik) ? null : daoFactory.getInvitationLinkDao().getInvitationByIdNoAndVendorCode(nik, vendorCode);
		if (null != invLinkByIdNo && "0".equals(invLinkByIdNo.getIsActive()) && null != vruNik) {
			throw new InvitationLinkException(getMessage("businesslogic.invitationlink.existingidno", new Object[] { nik }, audit), ReasonInvitationLink.UNKOWN);
		}

		List<TrInvitationLink> invLinkByPhone = StringUtils.isBlank(phone) ? null : daoFactory.getInvitationLinkDao().getListInvitationByPhone(phone);
		if (null != invLinkByPhone && !invLinkByPhone.isEmpty()
				&& (null != nik && !nik.isEmpty() && !nik.equals(invLinkByPhone.get(0).getIdNo())) && vruPhone != vruNik) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.existingphone",
					new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_PHONE_NO);
		}

		if (null != nikUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(nikUser.getIdMsUser(), false);

			if (null != phoneUser && null != emailUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& phoneUser.getLoginId().equals(emailUser.getLoginId())) {

				/*
				 * ESH-1236 NIK, HP, dan Email sudah digunakna untuk pengguna yang sama Throw
				 * error di bawah di-comment supaya bisa melanjutkan proses generate invitation
				 * link dan tidak memberhentikan proses NAP CONFINS
				 */

//				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.userdataused",
//				new String[] {email}, retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);

				return;
			}

			if (null != phoneUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& "1".equals(nikUser.getEmailService()) && StringUtils.isBlank(email)) {

				/*
				 * ESH-1236 Case serupa dengan yang di atas: NIK dan HP sudah digunakan untuk
				 * pengguna yang sama, pengguna tidak memiliki email pribadi Tidak throw error
				 * supaya bisa lanjut proses generate invitation link lainnya dan tidak
				 * memberhentikan proses NAP CONFINS
				 */
				return;
			}

			// Phone user matches NIK user
			if (null != vruPhone && nikUser.getLoginId().equals(vruPhone.getAmMsuser().getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednikphone",
						new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}

			// Email user matches NIK user
			if (null != vruEmail && nikUser.getLoginId().equals(vruEmail.getAmMsuser().getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						"businesslogic.invitationlink.emailnikusedinphone",
						new String[] { email, nik, MssTool.maskingString(personalData.getPhoneRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);
			}

			// REJECT
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
			if (vendors.contains(vendorCode) && vendor.getReregistAvailable().equals("0")) {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednik",
						new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}
		} else if (null != phoneUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(phoneUser.getIdMsUser(), false);
			String masked = generateMaskedInfo(personalData.getIdNoRaw());
			
			//Phone user matches email User
			if (null != emailUser && emailUser.getIdMsUser() == phoneUser.getIdMsUser()) {
				throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK, new Object[] {email, phone, masked}, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
			}
			
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.document.invalidphonenik", new Object[] {masked, nik}, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
		} else if (null != emailUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(emailUser.getIdMsUser(), false);
			String masked = generateMaskedInfo(personalData.getIdNoRaw());
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK, new Object[] {email, masked}, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
		}

		// Jika no telp sudah digunakan di invitation lain

		if (null != vruEmail) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(vruEmail.getAmMsuser().getIdMsUser(), false);

			// Phone user matches Email user
			if (null != vruPhone && vruEmail.getAmMsuser().getLoginId().equals(vruPhone.getAmMsuser().getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK,
						new String[] { email, phone, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
			}

			// REJECT
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK,
					new String[] { email, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
		}

		if (null != vruPhone) {
			// REJECT
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.phoneusedinemail",
					new String[] { phone, MssTool.maskEmailAddress(vruPhone.getAmMsuser().getLoginId(), 2) },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_PHONE);
		}
	}

	
	private void checkDuplicateNikInvLink(String nik, List<String> niks, AuditContext audit) {
		if (StringUtils.isBlank(nik)) {
			return;
		}

		if (niks.stream().noneMatch(nik::equals)) {
			niks.add(nik);
		} else {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.doublenik",
					new String[] { nik }, retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);
		}
	}

	private String checkDuplicateEmailInvLink(String email, List<String> emails, String emailRegex, AuditContext audit) {
		if (StringUtils.isNotBlank(email)) {
			if (emails.stream().noneMatch(email::equalsIgnoreCase)) {
				emails.add(email);
			} else {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.doubleemail",
						new Object[] { email }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
			}

			if (!email.matches(emailRegex)) {
				if (GlobalVal.CONST_CONFINS.equalsIgnoreCase(audit.getCallerId())) {
					return messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
							new Object[] { email }, this.retrieveLocaleAudit(audit));
				} else {
					throw new InvitationLinkException(
							messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
									new Object[] { email }, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.INVALID_EMAIL);
				}
			}
		}

		return "";
	}

	private String checkDuplicatePhoneInvLink(String phone, List<String> phones, String phoneNoRegex, String email,
			AuditContext audit) {
		if (StringUtils.isNotBlank(phone)) {
			if (phones.stream().noneMatch(phone::equalsIgnoreCase)) {
				phones.add(phone);
			} else {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.doublephone",
						new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_PHONE);
			}

			if (!phone.matches(phoneNoRegex)) {
				if (GlobalVal.CONST_CONFINS.equalsIgnoreCase(audit.getCallerId())) {
					return messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
							new Object[] { phone }, this.retrieveLocaleAudit(audit));
				} else {
					throw new InvitationLinkException(
							messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
									new Object[] { phone }, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.INVALID_PHONE_NO);
				}
			}
		}

		return "";
	}
	
	private String getPrivyLivenessUrl(TrInvitationLink invLink, AuditContext audit) {
		try {
			MsTenant tenant = invLink.getMsTenant();
			PrivyLivenessUrlResponse response = privyLogic.getPrivyLivenessV3Url(tenant, audit);
			return response.getData().getUserLandingUrl();
		} catch (Exception e) {
			throw new PrivyException(StringUtils.isBlank(e.getMessage()) ? getMessage("businesslogic.privy.failedtogetlivenessurl", null, audit) : e.getMessage(), e);
		}	
	}

	@Override
	public InvitationRegisterDataResponse getInvitationRegisterData(InvitationRegisterDataRequest request, AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		boolean isAutoFill = invLinkValidatorLogic.validateAutofillInvitationRegisteredUser(invLink);
		AmMsuser msUser = daoFactory.getUserDao().getUserByIdNo(invLink.getIdNo());
		
		UserBean userData = new UserBean();
		
		if (isAutoFill && msUser != null) {
			AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(msUser);
			userData.setUserGender(userPersonalData.getGender());
			userData.setKelurahan(userPersonalData.getZipcodeBean().getKelurahan());
			userData.setKecamatan(userPersonalData.getZipcodeBean().getKecamatan());
			userData.setKota(userPersonalData.getZipcodeBean().getKota());
			userData.setZipcode(userPersonalData.getZipcodeBean().getZipcode());
			userData.setUserPob(userPersonalData.getPlaceOfBirth());
			userData.setProvinsi(userPersonalData.getZipcodeBean().getProvinsi());
			userData.setUserAddress(personalDataEncLogic.decryptToString(userPersonalData.getAddressBytea()));
			userData.setUserDob(MssTool.formatDateToStringIn(userPersonalData.getDateOfBirth(), GlobalVal.DATE_FORMAT));
		} else {
			userData.setUserGender(invLink.getGender());
			userData.setKelurahan(invLink.getKelurahan());
			userData.setKecamatan(invLink.getKecamatan());
			userData.setKota(invLink.getKota());
			userData.setZipcode(invLink.getZipCode());
			userData.setUserPob(invLink.getPlaceOfBirth());
			userData.setProvinsi(invLink.getProvinsi());
			userData.setUserAddress((null == invLink.getAddress()) ? null : invLink.getAddress());
			userData.setUserDob((null == invLink.getDateOfBirth()) ? null
					: MssTool.formatDateToStringIn(invLink.getDateOfBirth(), GlobalVal.DATE_FORMAT));
		}
		
		userData.setUserName(invLink.getFullName());

		// Set Email
		String email = invLink.getEmail();
		String invitationBy = invLink.getInvitationBy();

		if (StringUtils.isNotBlank(email)) {
			userData.setEmail(email);
		} else if (GlobalVal.INV_BY_EMAIL.equals(invitationBy)) {
			userData.setEmail(invLink.getReceiverDetail());
		}
		
		userData.setIdNo(invLink.getIdNo());
		userData.setUserPhone(invLink.getPhone());
		

		if (null != invLink.getPhotoId()) {
			String key = new String(invLink.getPhotoId());
			byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
			userData.setIdPhoto((null == bytePhoto) ? null : Base64.getEncoder().encodeToString(bytePhoto));
		} else {
			userData.setIdPhoto(null);
		}
		
		MsVendor vendor = invLink.getMsVendor();

		String vendorCode = vendor.getVendorCode();
		String vendorName = vendor.getVendorName();
		String verifPhone = vendor.getVerifPhone();

		InvitationRegisterDataResponse response = new InvitationRegisterDataResponse();
		response.setTenantCode(invLink.getMsTenant().getTenantCode());
		response.setVendorCode(vendorCode);
		response.setVendorName(vendorName);
		response.setVerifPhone(verifPhone);
		response.setUserData(userData);

		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			response.setLivenessUrl(getPrivyLivenessUrl(invLink, audit));
			response.setUseSyncPrivyVerif(invLink.getUseSyncPrivyVerif());
		}
		
		return response;
	}

	private String getPrivyAsyncVerifProgress(TrJobCheckRegisterStatus trJobCheckRegisterStatus) {
		if  (null == trJobCheckRegisterStatus) {
			return "0";
		}

		Short requestStatus = trJobCheckRegisterStatus.getRequestStatus();
		if (requestStatus == 2) {
			return "2";
		}

		return "1";
	}

	private String getPrivyAsyncVerifResult(TrJobCheckRegisterStatus trJobCheckRegisterStatus) {
		if (null == trJobCheckRegisterStatus) {
			return "";
		}

		return trJobCheckRegisterStatus.getTrBalanceMutation().getNotes();
	}

	@Override
	public InvitationRegisterStatusResponse checkInvitationRegisterStatus(InvitationRegisterStatusRequest request, AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		audit.setCallerId(invLink.getReceiverDetail());
		
		String verificationInProgress = "0";
		String verificationResult = "";
		String certificateActiveStatus;
		
		if (invLink.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID) && !"1".equals(invLink.getUseSyncPrivyVerif())) {
			MsVendor vendor = invLink.getMsVendor();
			String idNo = invLink.getIdNo();
			TrJobCheckRegisterStatus trJobCheckRegisterStatus = daoFactory.getJobCheckRegisterStatusDao().getLatestJobCheckRegStatusByIdNo(idNo, vendor);
			verificationInProgress = getPrivyAsyncVerifProgress(trJobCheckRegisterStatus);
			verificationResult = getPrivyAsyncVerifResult(trJobCheckRegisterStatus);
		}
		
		InvitationRegisterStatusResponse response = new InvitationRegisterStatusResponse();
		response.setVerificationInProgress(verificationInProgress);
		response.setVerificationResult(verificationResult);
		
		AmMsuser user = null;
		if (StringUtils.isNumeric(invLink.getReceiverDetail())) {
			user = userValidatorLogic.validateGetUserByPhone(invLink.getReceiverDetail(), false, audit);
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(invLink.getReceiverDetail(), false, audit);
		}

		if (null == user) {
			invLinkValidatorLogic.validateInvitationExpiredDate(invLink, audit);
			
			response.setRegistrationStatus("0");
			response.setActiveStatus("0");
			return response;
		}

		MsVendorRegisteredUser vendorUser = null;
		if (StringUtils.isNumeric(invLink.getReceiverDetail())) {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(
					invLink.getReceiverDetail(), invLink.getMsVendor().getVendorCode());
		} else {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(
					invLink.getReceiverDetail(), invLink.getMsVendor().getVendorCode());
		}

		if (null == vendorUser) {
			invLinkValidatorLogic.validateInvitationExpiredDate(invLink, audit);
			response.setRegistrationStatus("0");
			response.setActiveStatus("0");
			return response;
		}

		if (StringUtils.isBlank(vendorUser.getVendorRegistrationId())) {
			invLinkValidatorLogic.validateInvitationExpiredDate(invLink, audit);
			response.setRegistrationStatus("0");
			response.setActiveStatus("0");
			return response;
		}

		if (vendorUser.getCertExpiredDate() != null) {
			if (!GlobalVal.VENDOR_CODE_VIDA.equals(vendorUser.getMsVendor().getVendorCode()) || !userValidatorLogic.isCertifExpiredForInquiry(vendorUser) ) {
					certificateActiveStatus = "1";
			} else {
				certificateActiveStatus = "0";
			}
			
			response.setCertificateActiveStatus(certificateActiveStatus);
		}

		String activeStatus = ("1".equals(vendorUser.getIsActive()) ? "1" : "0");
		String registerStatus = ("1".equals(vendorUser.getIsRegistered()) ? "1" : "0");
		String externalActivation = "1".equals(vendorUser.getIsExternalActivation()) ? "1" : "0";
		
		if ("0".equals(registerStatus)) {
			invLinkValidatorLogic.validateInvitationExpiredDate(invLink, audit);
		}
		
		response.setActiveStatus(activeStatus);
		response.setRegistrationStatus(registerStatus);
		response.setActivationByExternalFlow(externalActivation);
		
		return response;
	}

	@Override
	public GetUserPhotoResponse getPhoto(GetUserPhotoRequest request, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(request.getLoginId());
		if (null == user) {
			throw new ServicesUserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)));
		}
		GetUserPhotoResponse response = new GetUserPhotoResponse();
		Status status = new Status();
		String message = StringUtils.EMPTY;

		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);

		if (null == personalDataBean.getSelfPhotoRaw()) {
			message += "SELF PHOTO EMPTY; ";
		}
		if (null == personalDataBean.getPhotoIdRaw()) {
			message += "ID PHOTO EMPTY; ";
		}

		if (!request.getReturnAsFile()) {
			response.setSelfPhoto((null == personalDataBean.getSelfPhotoRaw()) ? null
					: Base64.getEncoder().encodeToString(personalDataBean.getSelfPhotoRaw()));

			response.setIdPhoto((null == personalDataBean.getPhotoIdRaw()) ? null
					: Base64.getEncoder().encodeToString(personalDataBean.getPhotoIdRaw()));
		} else {
			try {
				if (null != personalDataBean.getSelfPhotoRaw()) {
					ByteArrayInputStream bisSelf = new ByteArrayInputStream(personalDataBean.getSelfPhotoRaw());
					BufferedImage bImageSelf = ImageIO.read(bisSelf);
					ImageIO.write(bImageSelf, "jpg",
							new File(resultPhotoPath + File.separatorChar + "SELF_" + request.getLoginId() + ".jpg"));

					message += "SELF PHOTO GENERATED; ";
				}
				if (null != personalDataBean.getPhotoIdRaw()) {
					ByteArrayInputStream bisId = new ByteArrayInputStream(personalDataBean.getPhotoIdRaw());
					BufferedImage bImageId = ImageIO.read(bisId);
					ImageIO.write(bImageId, "jpg",
							new File(resultPhotoPath + File.separatorChar + "ID_" + request.getLoginId() + ".jpg"));

					message += "ID PHOTO GENERATED; ";
				}
			} catch (IOException e) {
				LOG.error("Get photo error ", e);
			}
		}

		status.setMessage(message);
		response.setStatus(status);

		return response;
	}

	@Override
	public ActivationLinkResponse getActivationLinkRegInv(ActivationLinkEmbedRequest request, AuditContext audit) {

		// Hardcode digisign karena URL API nya hardcode digisign juga
		// URL API: /actlinkRegInvDigi
		if (StringUtils.isBlank(request.getVendorCode()) || "null".equals(request.getVendorCode())) {
			request.setVendorCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		}

		String code = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);

		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCode(code);
		if (StringUtils.isBlank(invLink.getPhone()) && StringUtils.isBlank(invLink.getEmail())
				&& "0".equals(invLink.getIsActive())) {
			Status status = new Status();
			status.setCode(0);
			status.setMessage(messageSource.getMessage("businesslogic.user.alreadyactivated", null,
					this.retrieveLocaleAudit(audit)) + " (" + StatusCode.ALREADY_ACTIVATED + ")");
			ActivationLinkResponse response = new ActivationLinkResponse();
			response.setStatus(status);
			response.setFullName(invLink.getFullName());
			response.setIdNo(invLink.getIdNo());
			return response;
		}

		ActivationLinkRequest actLinkRequest = new ActivationLinkRequest();
		if (StringUtils.isNotBlank(request.getEmail())) {
			actLinkRequest.setEmail(request.getEmail());
		} else {
			if (invLink.getInvitationBy().equalsIgnoreCase(GlobalVal.INV_BY_EMAIL)) {
				actLinkRequest.setEmail(StringUtils.lowerCase(invLink.getReceiverDetail()));
			} else {
				AmMsuser user = daoFactory.getUserDao().getActiveUserByPhone(invLink.getReceiverDetail());
				actLinkRequest.setEmail(user.getLoginId());
			}
		}
		actLinkRequest.setVendorCode(request.getVendorCode());
		actLinkRequest.setTenantCode(invLink.getMsTenant().getTenantCode());

		ActivationLinkResponse response = getActLink(actLinkRequest, audit);
		if (StatusCode.DIGISIGN_FAILED == response.getStatus().getCode() && response.getStatus().getMessage().contains(
				"sudah selesai aktivasi, silahkan menunggu permintaan tanda tangan dikirimkan untuk lanjut ke proses berikutnya.")) {
			invLinkLogic.deactivateInvitationLink(invLink, audit);
		}
		response.setFullName(invLink.getFullName());
		response.setIdNo(invLink.getIdNo());
		return response;
	}

	private void sendOtpEmail(String fullname, String email, String otpCode, AuditContext audit) {
		Map<String, Object> userMap = new HashMap<>();
		userMap.put(FULLNAME, fullname);
		userMap.put("otp", otpCode);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userMap);
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP,
				templateParameters);

		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setSubject(template.getSubject());
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(new String[] { email });
		try {
			emailSenderLogic.sendEmail(emailInfo, null);
		} catch (MessagingException e) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMAIL_SENDER, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.OTP_REGISTER_EMAIL_ERROR);
		}
	}

	@Override
	public RegistrationResponse registerBM(RegistrationRequest request, AuditContext audit) throws IOException {
		this.validateIdNo(request.getUserData().getIdNo(),request.getVendorCode(), request.getUserData().getEmail(),request.getUserData().getUserPhone() ,audit);
		
		String validationMessage = "";

		String randomPassword = this.generateRandomPassword();
		AmMsuser newUser = new AmMsuser();
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(GlobalVal.OFFICE_CODE_HO,
				tenant.getTenantCode());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(GlobalVal.ROLE_BM_MF,
				tenant.getTenantCode());

		UserBean userBean = request.getUserData();
		String userJson = gson.toJson(userBean);
		SignerBean signerBean = gson.fromJson(userJson, SignerBean.class);

		signerBean.setLoginId(userBean.getEmail());
		signerBean.setEmailService("0");
		
		AmMsuser userNIK = daoFactory.getUserDao().getUserByIdNo(request.getUserData().getIdNo());
		
		this.insertNewUser(newUser, signerBean, randomPassword, office, audit);
		if (null == userNIK) {
//			 bandingin NIK 
			
			
//			bandingin NIK juga sama kayak atas dri ammsuser
			this.insertNewUserPersonalData(newUser, signerBean, audit);
			
			this.insertPasswordHist(newUser, audit);
		}
		
		MsUseroftenant userTenant = daoFactory.getTenantDao().getUseroftenantByLoginIdTenantCode(request.getUserData().getEmail(), request.getTenantCode());
		
		if (null == userTenant) {
//			bandingin user nya udh ada di tenant itu apa belum
			this.insertUserofTenant(newUser, tenant, audit);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofrole(request.getUserData().getEmail(), role);

		if (null == userRole) {
//			bandingin juga udh ada apa belom di role itu
			this.insertRoleNewUser(newUser, role, audit);
		}

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_VIDA_CERTIFICATE_EXPIRE_TIME);
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new Object[] {"Duration Certificate " + vendor.getVendorCode()}, audit);
		commonValidatorLogic.validateNotNull(gs, validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
		int certificateDurationVendor ;
		try {
			certificateDurationVendor = Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			throw new CommonException("Invalid value Certificate Duration", ReasonCommon.INVALID_DATE_FORMAT);
		}

		Date activatedDate = new Date();
		Date expiredDate = DateUtils.addDays(activatedDate, certificateDurationVendor);

		MsVendorRegisteredUser registeredUser = new MsVendorRegisteredUser();
		registeredUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getUserData().getEmail()));
		registeredUser.setIsActive("1");
		registeredUser.setUsrCrt(audit.getCallerId());
		registeredUser.setDtmCrt(new Date());
		registeredUser.setAmMsuser(newUser);
		registeredUser.setMsVendor(vendor);
		registeredUser.setVendorUserAutosignKey(request.getUserData().getkUser());
		registeredUser.setIsRegistered("1");
		registeredUser.setEmailService("0");
		if (StringUtils.isNotBlank(request.getUserData().getUserPhone())) {
			String phone = MssTool.getHashedString(request.getUserData().getUserPhone());
			registeredUser.setHashedSignerRegisteredPhone(phone);
			byte[] phoneBytea = personalDataEncLogic.encryptFromString(request.getUserData().getUserPhone());
			registeredUser.setPhoneBytea(phoneBytea);
		}
		registeredUser.setActivatedDate(activatedDate);
		registeredUser.setCertExpiredDate(expiredDate);
		registeredUser.setVendorUserAutosignCvv(request.getUserData().getcvv());
		registeredUser.setPoaId(request.getUserData().getPoaId());
		vendorLogic.insertVendorRegisteredUser(registeredUser);

		this.sendAccountInfoEmail(signerBean.getUserName(), signerBean.getEmail(), randomPassword);

		return new RegistrationResponse();
	}

	@Override
	public ChangeProfileDataResponse changeUserProfileData(ChangeProfileDataRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getEmailOrPhone())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new String[] { "emailOrPhone" }, this.retrieveLocaleAudit(audit)),
					ReasonUser.EMAIL_OR_PHONE_EMPTY);
		}

		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);

		AmMsuser userExist;
		if (StringUtils.isNumeric(request.getEmailOrPhone())) {
			if (!GlobalVal.NOTIF_TYPE_SMS.equals(user.getDataChangeRequest())) {
				throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VERIFY_NOT_MATCH,
						null, this.retrieveLocaleAudit(audit)), ReasonUser.VERIFY_NOT_MATCH);
			}

			userExist = daoFactory.getUserDao().getActiveUserByPhone(request.getEmailOrPhone());
			if (null != userExist) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_ALREADY_EXISTED,
								new String[] { request.getEmailOrPhone() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.PHONE_NO_ALREADY_EXIST);
			}

			String hashedPhone = MssTool.getHashedString(request.getEmailOrPhone());
			user.setHashedPhone(hashedPhone);
			personalDataBean.setPhoneRaw(request.getEmailOrPhone());
		} else {
			if (!GlobalVal.NOTIF_TYPE_EMAIL.equals(user.getDataChangeRequest())) {
				throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VERIFY_NOT_MATCH,
						null, this.retrieveLocaleAudit(audit)), ReasonUser.VERIFY_NOT_MATCH);
			}

			userExist = daoFactory.getUserDao().getUserByLoginId(request.getEmailOrPhone());
			if (null != userExist) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_EMAIL_ALREADY_EXISTED,
								new String[] { request.getEmailOrPhone() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.ALREADY_REGISTERED);
			}

			MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByLoginId(audit.getCallerId());
			vendorRegisteredUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmailOrPhone()));
			vendorRegisteredUser.setDtmUpd(new Date());
			vendorRegisteredUser.setUsrUpd(audit.getCallerId());
			user.setLoginId(StringUtils.upperCase(request.getEmailOrPhone()));
			user.setEmailService("0");
			personalDataBean.getUserPersonalData().setEmail(StringUtils.upperCase(request.getEmailOrPhone()));
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vendorRegisteredUser);
		}

		user.setDataChangeRequest(null);
		user.setDtmUpd(new Date());
		user.setUsrUpd(audit.getCallerId());
		personalDataBean.getUserPersonalData().setDtmUpd(new Date());
		personalDataBean.getUserPersonalData().setUsrUpd(audit.getCallerId());
		daoFactory.getUserDao().updateUser(user);
		daoFactory.getUserDao().updateUserPersonalData(personalDataBean);

		return new ChangeProfileDataResponse();
	}

	@Override
	public SendChangeProfileOTPResponse sendChangeProfileOtp(SendChangeProfileOTPRequest request, AuditContext audit)
			throws IOException {
		SendChangeProfileOTPResponse response = new SendChangeProfileOTPResponse();
		Status status = new Status();
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		LOG.info(otpCode);
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		PersonalDataBean bean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		user.setOtpCode(otpCode);
		String masked;
		if (!request.getIsEmail().equals("1")) {
			this.sendOtpSms(bean.getPhoneRaw(), otpCode, null,tenant, user,null,null,null,null,null, audit);
			user.setDataChangeRequest(GlobalVal.NOTIF_TYPE_SMS);
			masked = this.generateMaskedInfo(bean.getPhoneRaw());
		} else {
			if (user.getEmailService().equals("1")) {
				status.setCode(9999);
				status.setMessage(messageSource.getMessage("businesslogic.user.cannotsentemail",
						new Object[] { user.getFullName() }, this.retrieveLocaleAudit(audit)));
				response.setStatus(status);
				return response;
			}

			this.sendOtpEmail(user.getFullName(), user.getLoginId(), otpCode, audit);
			user.setDataChangeRequest(GlobalVal.NOTIF_TYPE_EMAIL);
			String[] splitEmail = user.getLoginId().split("@");
			masked = this.generateMaskedInfo(splitEmail[0]) + "@" + splitEmail[1];
		}
		daoFactory.getUserDao().updateUser(user);
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		response.setEmailOrPhone(masked);
		return response;
	}

	private String generateMaskedInfo(String toMask) {
		StringBuilder masked = new StringBuilder();
		int length = toMask.length();
		if (StringUtils.isNumeric(toMask)) {
			masked.append(toMask.subSequence(0, 2));
			for (int i = 0; i < length - 4; i++) {
				masked.append("*");
			}
			masked.append(toMask.substring(length - 3));
		} else {
			masked.append(toMask.substring(0, 2));
			for (int i = 0; i < length - 2; i++) {
				masked.append("*");
			}
		}

		return masked.toString();
	}

	private void sendOtpSms(String phoneNo, String otpCode, String notes, MsTenant tenant, AmMsuser user,TrDocumentH docH,MsOffice office,MsBusinessLine businessLine,String refNo, TrSigningProcessAuditTrail auditTrail, AuditContext audit) {
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		
		MsMsgTemplate template = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		} else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}
		
		SendSmsResponse responseSms = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, template.getBody(), tenant);
		
		SigningProcessAuditTrailBean auditTrailBean = null;
		if (null != auditTrail) {
			auditTrailBean = new SigningProcessAuditTrailBean();
			auditTrailBean.setEmail(auditTrail.getEmail());
			auditTrailBean.setInvLink(auditTrail.getTrInvitationLink());
			auditTrailBean.setLovProcessType(auditTrail.getLovProcessType());
			auditTrailBean.setLovSendingPoint(auditTrail.getLovSendingPoint());
			auditTrailBean.setNotes(auditTrail.getNotes());
			auditTrailBean.setOtpCode(auditTrail.getOtpCode());
			auditTrailBean.setPhone(phoneNo);
			auditTrailBean.setTenant(tenant);
			auditTrailBean.setUser(user);
			auditTrailBean.setVendorPsre(auditTrail.getMsVendor());
		}
		
		if (gs.getGsValue().equals("1")) {
			if (null != auditTrailBean) {
				responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean, auditTrailBean);
			} else {
				responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean);
			}
		} else {
			LOG.info("Send SMS OTP Success");
			responseSms.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		String balanceMutationNotes = null;
		if (StringUtils.isBlank(notes)) {
			balanceMutationNotes = phoneNo + GlobalVal.SEND_OTP_SMS;
		} else {
			balanceMutationNotes = notes;
		}

		if (responseSms.getErrorCode() == null || 
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, docH, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					-1, String.valueOf(responseSms.getTrxNo()), user, balanceMutationNotes, responseSms.getGuid(),office,businessLine, audit);
		} else {
			saldoLogic.insertBalanceMutation(null, docH, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					0, String.valueOf(responseSms.getTrxNo()), user, balanceMutationNotes + GlobalVal.BALMUT_ERROR + responseSms.getErrorCode(),
					responseSms.getGuid(),office,businessLine, audit);
		}
	}
	
	private void sendOtpSms(String phoneNo, String otpCode, String notes, MsTenant tenant, AmMsuser user,TrDocumentH docH,MsOffice office,MsBusinessLine businessLine,String refNo, AuditContext audit, SigningProcessAuditTrailBean auditTrailBean) {
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		
		MsMsgTemplate template = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		} else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}
		
		SendSmsResponse responseSms = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, template.getBody(), tenant);
		if (gs.getGsValue().equals("1")) {
			responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean, auditTrailBean);
		} else {
			LOG.info("Send SMS OTP Success");
			responseSms.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		String balanceMutationNotes = null;
		if (StringUtils.isBlank(notes)) {
			balanceMutationNotes = phoneNo + GlobalVal.SEND_OTP_SMS;
		} else {
			balanceMutationNotes = notes;
		}

		if (responseSms.getErrorCode() == null || 
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, docH, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					-1, String.valueOf(responseSms.getTrxNo()), user, balanceMutationNotes, responseSms.getGuid(),office,businessLine, audit);
		} else {
			saldoLogic.insertBalanceMutation(null, docH, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					0, String.valueOf(responseSms.getTrxNo()), user, balanceMutationNotes + GlobalVal.BALMUT_ERROR + responseSms.getErrorCode(),
					responseSms.getGuid(),office,businessLine, audit);
		}
	}

	@Override
	public OtpVerifChangeProfileResponse otpVerifChangeProfile(OtpVerifChangeProfileRequest request,
			AuditContext audit) {
		if (StringUtils.isBlank(request.getLoginId())) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new Object[] { AmMsuser.LOGIN_ID_HBM }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_EMPTY);
		}
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(request.getLoginId());
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		if ("0".equals(user.getIsActive())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_INACTIVE_OR_NOT_EXIST,
					null, this.retrieveLocaleAudit(audit)), ReasonUser.INACTIVE_USER);
		}
		if (StringUtils.isBlank(request.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { CONST_CODE_OTP }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}
		if (!request.getOtpCode().equals(user.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
		}

		user.setOtpCode(null);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);
		return new OtpVerifChangeProfileResponse();
	}

	@Override
	public UpdateUserDataResponse updateUserData(UpdateUserDataRequest request, String apiKey, AuditContext audit) {

		String updateCode = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_USER_UPDATE_CODE,
				audit);
		if (!updateCode.equals(request.getUpdateCode())) {
			throw new UserException(this.messageSource.getMessage("businesslogic.user.invalidupdatecode", null,
					this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_UPDATE_CODE);
		}

		AmMsuser user;
		boolean checkUserExistence = false;

		user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);

		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);
		MsUseroftenant userTenant = daoFactory.getUseroftenantDao().getUserTenantByLoginIdAndTenantCode(user.getLoginId(),
				tenant.getTenantCode());
		if (null == userTenant) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_TENANT_NOT_FOUND,
					new String[] { user.getLoginId(), tenant.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonUser.USER_TENANT_NOT_FOUND);
		}

		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		AmUserPersonalData userPersonalData = personalData.getUserPersonalData();
		ZipcodeCityBean zipcodeBean = personalData.getUserPersonalData().getZipcodeBean();

		if (StringUtils.isNotBlank(request.getFullName())) {
			String fullName = StringUtils.upperCase(request.getFullName());
			LOG.info("User {}, updating fullName from {} to {}", user.getLoginId(), user.getFullName(), fullName);
			user.setFullName(fullName);
		}

		if (StringUtils.isNotBlank(request.getGender())) {
			if (!GlobalVal.CODE_LOV_MALE.equalsIgnoreCase(request.getGender())
					&& !GlobalVal.CODE_LOV_FEMALE.equalsIgnoreCase(request.getGender())) {
				throw new UserException(this.messageSource.getMessage("businesslogic.user.invalidgender", null,
						this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_GENDER_CODE);
			}
			String gender = StringUtils.upperCase(request.getGender());
			LOG.info("User {}, updating gender from {} to {}", user.getLoginId(), userPersonalData.getGender(), gender);
			userPersonalData.setGender(gender);
		}

		if (StringUtils.isNotBlank(request.getKelurahan())) {
			String kelurahan = StringUtils.upperCase(request.getKelurahan());
			LOG.info("User {}, updating kelurahan from {} to {}", user.getLoginId(), zipcodeBean.getKelurahan(),
					kelurahan);
			zipcodeBean.setKelurahan(StringUtils.upperCase(request.getKelurahan()));
		}
		if (StringUtils.isNotBlank(request.getKecamatan())) {
			String kecamatan = StringUtils.upperCase(request.getKecamatan());
			LOG.info("User {}, updating kecamatan from {} to {}", user.getLoginId(), zipcodeBean.getKecamatan(),
					kecamatan);
			zipcodeBean.setKecamatan(StringUtils.upperCase(request.getKecamatan()));
		}
		if (StringUtils.isNotBlank(request.getKota())) {
			String kota = StringUtils.upperCase(request.getKota());
			LOG.info("User {}, updating kota from {} to {}", user.getLoginId(), zipcodeBean.getKota(), kota);
			zipcodeBean.setKota(StringUtils.upperCase(request.getKota()));
		}
		if (StringUtils.isNotBlank(request.getProvinsi())) {
			String provinsi = StringUtils.upperCase(request.getProvinsi());
			LOG.info("User {}, updating provinsi from {} to {}", user.getLoginId(), zipcodeBean.getProvinsi(),
					provinsi);
			zipcodeBean.setProvinsi(StringUtils.upperCase(request.getProvinsi()));
		}
		if (StringUtils.isNotBlank(request.getZipCode())) {
			LOG.info("User {}, updating zipcode from {} to {}", user.getLoginId(), zipcodeBean.getZipcode(),
					request.getZipCode());
			zipcodeBean.setZipcode(request.getZipCode());
		}

		if (StringUtils.isNotBlank(request.getDateOfBirth())) {
			String currentDob = MssTool.formatDateToStringIn(userPersonalData.getDateOfBirth(), GlobalVal.DATE_FORMAT);
			LOG.info("User {}, updating dob from {} to {}", user.getLoginId(), currentDob, request.getDateOfBirth());
			userPersonalData
					.setDateOfBirth(MssTool.formatStringToDate(request.getDateOfBirth(), GlobalVal.DATE_FORMAT));
		}

		if (StringUtils.isNotBlank(request.getPhotoSelf())) {
			LOG.info("User {}, self photo updated", user.getLoginId());
			byte[] photoSelf = MssTool.imageStringToByteArray(request.getPhotoSelf());
			personalData.setSelfPhotoRaw(photoSelf);
		}

		if (StringUtils.isNotBlank(request.getPlaceOfBirth())) {
			String pob = StringUtils.upperCase(request.getPlaceOfBirth());
			LOG.info("User {}, updating place of birth from {} to {}", user.getLoginId(),
					userPersonalData.getPlaceOfBirth(), pob);
			userPersonalData.setPlaceOfBirth(pob);
		}

		if (StringUtils.isNotBlank(request.getIdNo())) {
			AmMsuser idNoUser = daoFactory.getUserDao().getUserByIdNo(request.getIdNo());
			if (null != idNoUser && !user.getLoginId().equals(idNoUser.getLoginId())) {
				throw new UserException(this.messageSource.getMessage("businesslogic.user.nikalreadyregistered",
						new String[] { request.getIdNo(), idNoUser.getLoginId() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.NIK_ALREADY_EXIST);
			}
			LOG.info("User {}, updating idNo from {} to {}", user.getLoginId(), personalData.getIdNoRaw(),
					request.getIdNo());
			user.setHashedIdNo(MssTool.getHashedString(request.getIdNo()));
			personalData.setIdNoRaw(request.getIdNo());
		}

		if (StringUtils.isNotBlank(request.getAddress())) {
			String address = StringUtils.upperCase(request.getAddress());
			LOG.info("User {}, address updated from {} to {}", user.getLoginId(), personalData.getAddressRaw(),
					address);
			personalData.setAddressRaw(StringUtils.upperCase(request.getAddress()));
		}

		if (StringUtils.isNotBlank(request.getPhotoId())) {
			LOG.info("User {}, id photo updated", user.getLoginId());
			byte[] photoId = MssTool.imageStringToByteArray(request.getPhotoId());
			personalData.setPhotoIdRaw(photoId);
		}

		// untuk update email service sesuai dengan email yang di-input (kalau domai ada
		// di email hosting,
		// berarti email service 1
//		List<MsEmailHosting> lEh = daoFactory.getEmailDao().getListActiveEmailHosting();
//		List<String> domains = new ArrayList<>();
//		for(MsEmailHosting eh : lEh) {
//			domains.add(eh.getEmailHostingDomain());
//		}

		if (null == request.getVendorCode()) {
			throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_EMPTY);
		}

		MsVendorRegisteredUser mvru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(request.getLoginId(), request.getVendorCode());
		if (null != mvru) {
			if (StringUtils.isNotBlank(request.getIsRegistered())) {
				if ("1".equals(request.getIsRegistered()) || "0".equals(request.getIsRegistered())) {
					LOG.info("User {}, Register status updated to : {}", user.getLoginId(), request.getIsRegistered());
					mvru.setIsRegistered(request.getIsRegistered());
				} else {
					throw new UserException("isRegister harus diisi 0 atau 1", ReasonUser.UNKNOWN);
				}
			}

			if (StringUtils.isNotBlank(request.getIsActive())) {
				if ("1".equals(request.getIsActive()) || "0".equals(request.getIsActive())) {
					LOG.info("User {}, Active status updated to : {}", user.getLoginId(), request.getIsActive());
					mvru.setIsActive(request.getIsActive());
				} else {
					throw new UserException("isActive harus diisi 0 atau 1", ReasonUser.UNKNOWN);
				}
			}

			if (StringUtils.isNotBlank(request.getPhone())) {
				AmMsuser phoneUser = daoFactory.getUserDao().getActiveUserByPhone(request.getPhone());
				if (null != phoneUser && !(user.getLoginId().equals(phoneUser.getLoginId()))) {
					throw new UserException(this.messageSource.getMessage("businesslogic.user.phonealreadyregistered1",
							new String[] { request.getPhone(), phoneUser.getLoginId() },
							this.retrieveLocaleAudit(audit)), ReasonUser.PHONE_NO_ALREADY_EXIST);
				}
				LOG.info("User {}, updating phone from {} to {}", user.getLoginId(), personalData.getPhoneRaw(),
						request.getPhone());
				user.setHashedPhone(MssTool.getHashedString(request.getPhone()));
				personalData.setPhoneRaw(request.getPhone());
				mvru.setPhoneBytea(personalDataEncLogic.encryptFromString(request.getPhone()));
				mvru.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getPhone()));
			}

			mvru.setDtmUpd(new Date());
			mvru.setUsrUpd(audit.getCallerId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(mvru);
		} else {
			throw new UserException(
					this.messageSource.getMessage("businesslogic.vendor.mvrunull",
							new String[] { request.getVendorCode() }, this.retrieveLocaleAudit(audit)),
					ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		if (StringUtils.isNotBlank(request.getNewEmail())) {
			this.checkAmMsuserPhoneEmailNik(null, request.getNewEmail(), null, audit);
			AmMsuser newEmailUser = userValidatorLogic.validateGetUserByEmailv2(request.getNewEmail(),
					checkUserExistence, audit);

			if (null != newEmailUser) {
				throw new UserException(
						this.messageSource.getMessage("businesslogic.user.emailalreadyregistered",
								new String[] { request.getNewEmail() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.PHONE_NO_ALREADY_EXIST);
			}
//			String[] email = request.getNewEmail().split("@");
			LOG.info("User {}, email updated from {} to {}", user.getLoginId(), user.getLoginId(),
					request.getNewEmail());

			mvru.setDtmUpd(new Date());
			mvru.setUsrUpd(audit.getCallerId());
			mvru.setSignerRegisteredEmail(StringUtils.upperCase(request.getNewEmail()));
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(mvru);

			user.setLoginId(StringUtils.upperCase(request.getNewEmail()));
			// user.setEmailService(domains.contains(StringUtils.lowerCase(email[1])) ? "1"
			// : "0");
			user.setEmailService("0");
			userPersonalData.setEmail(StringUtils.upperCase(request.getNewEmail()));
		}

		userPersonalData.setDtmUpd(new Date());
		userPersonalData.setUsrUpd(audit.getCallerId());
		userPersonalData.setZipcodeBean(zipcodeBean);
		personalData.setUserPersonalData(userPersonalData);

		user.setDtmUpd(new Date());
		user.setUsrUpd(audit.getCallerId());
		daoFactory.getUserDao().updateUser(user);
		daoFactory.getUserDao().updateUserPersonalData(personalData);

		return new UpdateUserDataResponse();
	}

	@Override
	public ResendRegisterInvitationResponse resendRegisterInvitation(ResendRegisterInvitationRequest request,
			AuditContext audit) throws UnsupportedEncodingException {
		Status status = new Status();
		ResendRegisterInvitationResponse response = new ResendRegisterInvitationResponse();

		TrInvitationLink invLink = this.getTrInvitationLinkByReceiverDetailAndVendorCode(request.getReceiverDetail(),
				request.getTenantCode(), request.getVendorCode(), audit);
		String emailService = "0";
		if (GlobalVal.INV_BY_EMAIL.equals(invLink.getInvitationBy())) {
			MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(invLink.getReceiverDetail(),
							request.getVendorCode());
			
			if (null != registeredUser) {
				boolean checkUserExistence = false;
				AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(invLink.getReceiverDetail(),
						checkUserExistence, audit);

				boolean isECertExpired = false;
				
				if (request.getVendorCode().equalsIgnoreCase(GlobalVal.VENDOR_CODE_VIDA)) {
					isECertExpired = userValidatorLogic.isCertifExpiredForInquiry(registeredUser);
				}
				
				if (!isECertExpired && StringUtils.isNotBlank(registeredUser.getVendorRegistrationId())) {
					TrDocumentD earliestDocument = daoFactory.getDocumentDao()
							.getLatestDocumentDetailBySignerLoginId(user.getLoginId());
					if (null != earliestDocument) {
						throw new InvitationLinkException(
								this.messageSource.getMessage("businesslogic.user.useremailhasdocument",
										new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)),
								ReasonInvitationLink.USER_REGISTERED);
					}
					
					throw new InvitationLinkException(this.messageSource.getMessage("businesslogic.user.alreadyregistered",
							null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
				}
			}
			
		} else if (GlobalVal.INV_BY_SMS.equals(invLink.getInvitationBy())) {
			MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByPhoneAndVendorCode(invLink.getReceiverDetail(), request.getVendorCode());

			if (null != registeredUser) {
				boolean checkUserExistence = true;

				AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getReceiverDetail(),
						checkUserExistence, audit);
				
				boolean isECertExpired = false;
				
				if (request.getVendorCode().equalsIgnoreCase(GlobalVal.VENDOR_CODE_VIDA)) {
					isECertExpired = registeredUser.getCertExpiredDate().compareTo(new Date()) <= 0;
				}
				
				if (!isECertExpired && StringUtils.isNotBlank(registeredUser.getVendorRegistrationId())) {
					TrDocumentD earliestDocument = daoFactory.getDocumentDao()
							.getLatestDocumentDetailBySignerLoginId(user.getLoginId());
					if (null != earliestDocument) {
						throw new InvitationLinkException(
								this.messageSource.getMessage("businesslogic.user.userphonehasdocument",
										new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)),
								ReasonInvitationLink.USER_REGISTERED);
					}
					
					throw new InvitationLinkException(this.messageSource.getMessage("businesslogic.user.alreadyregistered",
							null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
				}
			}
			
			emailService = "1";
		}
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		MsLov process = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_RESEND_INV);
		auditTrail.setLovProcessType(process);
		auditTrail.setMsTenant(invLink.getMsTenant());
		auditTrail.setMsVendor(invLink.getMsVendor());
		auditTrail.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		auditTrail.setDtmCrt(new Date());
		auditTrail.setEmail(StringUtils.isBlank(invLink.getEmail()) ? null : invLink.getEmail());
		if (StringUtils.isNotBlank(invLink.getPhone())) {
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(invLink.getPhone()));
			auditTrail.setHashedPhoneNo( MssTool.getHashedString(invLink.getPhone()));
		}
		
		NotificationType type = tenantLogic.getNotificationType(invLink.getMsTenant(), NotificationSendingPoint.RESEND_INV, emailService);
		NotificationSendingPoint sendPoint = NotificationSendingPoint.RESEND_INV;
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, sendPoint.getLovCode());
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setNotes(invLink.getInvitationCode());
		auditTrail.setResultStatus("1");
		auditTrail.setTrInvitationLink(invLink);
		
		if (type.compareTo(NotificationType.EMAIL) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
			try {
				status = this.resendInvitationEmail(invLink, audit);
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
			} catch (Exception e) {
				LOG.error("Resend invitation email error", e);
				auditTrail.setResultStatus("0");
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				status.setCode(StatusCode.UNKNOWN);
				status.setMessage(e.getLocalizedMessage());
			}
		} else if (type.compareTo(NotificationType.WHATSAPP) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
			auditTrail.setNotificationVendor(gateway.getDescription());
			status = resendInvitationWa(invLink, auditTrail, audit);
		} else if (type.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
			auditTrail.setNotificationVendor(gateway.getDescription());
			status = resendInvitationWaHalosis(invLink, auditTrail, audit);
		} else if (type.compareTo(NotificationType.SMS_VFIRST) == 0) { 
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
			auditTrail.setNotificationVendor(gateway.getDescription());
			try {
				status = this.resendInvitationSmsVfirst(invLink, auditTrail, audit);
			} catch (Exception e) {
				LOG.error("Resend invitation SMS error", e);
				auditTrail.setResultStatus("0");
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				status.setCode(StatusCode.UNKNOWN);
				status.setMessage(e.getLocalizedMessage());
			}
		} else if (type.compareTo(NotificationType.SMS_JATIS) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);
			auditTrail.setNotificationVendor(gateway.getDescription());
			try {
				status = this.resendInvitationSmsJatis(invLink, auditTrail, audit);
			} catch (Exception e) {
				LOG.error("Resend invitation SMS error", e);
				auditTrail.setResultStatus("0");
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				status.setCode(StatusCode.UNKNOWN);
				status.setMessage(e.getLocalizedMessage());
				if (StringUtils.isBlank(status.getMessage())) {
					status.setMessage(getMessage("businesslogic.invitationlink.failedtoresend", null, audit));
				}
			}
		} else {
			throw new InvitationLinkException(
					this.messageSource.getMessage("businesslogic.invitationlink.invalidinvitationby", null,
							this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.INVALID_INV_BY);
		}

		response.setStatus(status);
		return response;
	}

	private String createInvitationLink(String invCode, AuditContext audit) throws UnsupportedEncodingException {
		String aesEncryptedLink = commonLogic.encryptMessageToString(invCode, audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		return regInvLink + encrypted;
	}

	private Status resendInvitationEmail(TrInvitationLink invLink, AuditContext audit)
			throws UnsupportedEncodingException {

		Map<String, Object> link = new HashMap<>();
		link.put("link", this.createInvitationLink(invLink.getInvitationCode(), audit));
		link.put(TENANT_NAME, invLink.getMsTenant().getTenantName());
		Map<String, Object> template = new HashMap<>();
		template.put(CONST_INVITE, link);

		MsMsgTemplate msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG, template);
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setBodyMessage(msg.getBody());
		emailInfo.setSubject(msg.getSubject());
		String[] to = new String[] { invLink.getReceiverDetail() };
		emailInfo.setTo(to);
		emailInfo.setFrom(fromEmailAddr);
		Status status = new Status();
		try {
			emailSenderLogic.sendEmail(emailInfo, null);
			status.setCode(0);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
					new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
		} catch (MessagingException e) {
			LOG.error("Send invitation email error.", e);
			status.setCode(StatusCode.UNKNOWN);
			status.setMessage(e.getLocalizedMessage());
		}
		return status;
	}

	private Status resendInvitationSmsVfirst(TrInvitationLink invLink, TrSigningProcessAuditTrail auditTrail, AuditContext audit)
			throws UnsupportedEncodingException {

		String phone = StringUtils.isNotBlank(invLink.getPhone()) ? invLink.getPhone() : invLink.getReceiverDetail();

		Map<String, Object> link = new HashMap<>();
		link.put("link", this.createInvitationLink(invLink.getInvitationCode(), audit));
		link.put(TENANT_NAME, invLink.getMsTenant().getTenantName());
		Map<String, Object> template = new HashMap<>();
		template.put(CONST_INVITE, link);

		MsMsgTemplate msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG_SMS, template);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = invLink.getReceiverDetail() + " : Resend SMS Account Info";

		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phone, msg.getBody(), invLink.getMsTenant());
		SendSmsResponse response = smsLogic.sendSms(sendSmsValueFirstRequestBean);
		if (null == response.getErrorCode() || 
				(!VFIRST_ERR28682.equals(response.getErrorCode())
				&& !VFIRST_ERR28681.equals(response.getErrorCode())
				&& !VFIRST_ERR408.equals(response.getErrorCode()))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, invLink.getMsTenant(), vendor,
					new Date(), invLink.getRefNumber(), -1, String.valueOf(response.getTrxNo()), null, notes, response.getGuid(), invLink.getMsOffice(), invLink.getMsBusinessLine(), audit);
			auditTrail.setResultStatus("1");
		} else {
			notes += GlobalVal.BALMUT_ERROR + response.getErrorCode();
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, invLink.getMsTenant(), vendor,
					new Date(), invLink.getRefNumber(), 0, String.valueOf(response.getTrxNo()), null, notes, response.getGuid(), invLink.getMsOffice(), invLink.getMsBusinessLine(), audit);
			auditTrail.setResultStatus("0");
		}

		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		
		Status status = new Status();
		if (null == response.getErrorCode()) {
			status.setCode(0);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
					new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
		} else {
			Integer code = 0;
			try {
				code = Integer.valueOf(response.getErrorCode());
			} catch (Exception e) {
				code = StatusCode.UNKNOWN;
			}
			status.setCode(code);
			status.setMessage(response.getErrorMsg());
		}
		return status;
	}
	
	private Status resendInvitationSmsJatis(TrInvitationLink invLink, TrSigningProcessAuditTrail auditTrail, AuditContext audit)
			throws UnsupportedEncodingException {

		String phone = StringUtils.isNotBlank(invLink.getPhone()) ? invLink.getPhone() : invLink.getReceiverDetail();

		Map<String, Object> link = new HashMap<>();
		link.put("link", this.createInvitationLink(invLink.getInvitationCode(), audit));
		link.put(TENANT_NAME, invLink.getMsTenant().getTenantName());
		Map<String, Object> template = new HashMap<>();
		template.put(CONST_INVITE, link);

		MsMsgTemplate msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG_SMS, template);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = invLink.getReceiverDetail() + " : Resend SMS Account Info";

		String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		JatisSmsRequestBean request = new JatisSmsRequestBean(invLink.getMsTenant(), invLink.getMsOffice(), invLink.getMsBusinessLine(), phone, msg.getBody(), reservedTrxNo, invLink.getRefNumber(), false);
		JatisSmsResponse response = jatisSmsLogic.sendSmsOnly(request);
		
		if ("1".equals(response.getStatus())) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, invLink.getMsTenant(), vendor,
					new Date(), invLink.getRefNumber(), -1, reservedTrxNo, null, notes, response.getMessageId(), invLink.getMsOffice(), invLink.getMsBusinessLine(), audit);
			auditTrail.setResultStatus("1");
		} else {
			notes += GlobalVal.BALMUT_ERROR + response.getStatus();
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, invLink.getMsTenant(), vendor,
					new Date(), invLink.getRefNumber(), 0, reservedTrxNo, null, notes, response.getMessageId(), invLink.getMsOffice(), invLink.getMsBusinessLine(), audit);
			auditTrail.setResultStatus("0");
		}

		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		
		Status status = new Status();
		if ("1".equals(response.getStatus())) {
			status.setCode(0);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
					new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
		} else {
			Integer code = 0;
			try {
				code = Integer.valueOf(response.getStatus());
			} catch (Exception e) {
				code = StatusCode.UNKNOWN;
			}
			status.setCode(code);
			status.setMessage(response.getMessageId());
			if (StringUtils.isBlank(status.getMessage())) {
				status.setMessage(getMessage("businesslogic.invitationlink.failedtoresend", null, audit));
			}
		}
		return status;
	}
	
	private Status resendInvitationWa(TrInvitationLink invLink, TrSigningProcessAuditTrail auditTrail, AuditContext audit) throws UnsupportedEncodingException {
		MsMsgTemplate msg;
		if (invLink.getMsTenant().getInvitationLinkActiveDuration() == null || invLink.getMsTenant().getInvitationLinkActiveDuration() == 0) {
			msg = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.CONST_INVITATION_LINK_BUTTON);
		} else {
			msg = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.CONST_INVITATION_LINK_ACTIVE_DURATION);
		}
		
		MsTenant tenant = invLink.getMsTenant();
		
		String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		
		String buttonText = encrypted;
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(invLink.getMsTenant().getTenantName());
		bodyTexts.add(encrypted);
		if (msg.getTemplateCode().equals(GlobalVal.CONST_INVITATION_LINK_ACTIVE_DURATION)) {
			bodyTexts.add(String.valueOf(tenant.getInvitationLinkActiveDuration()));
		}
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(msg);
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(invLink.getMsTenant());
		request.setPhoneNumber(invLink.getReceiverDetail());
		request.setMsOffice(invLink.getMsOffice());
		request.setMsBusinessLine(invLink.getMsBusinessLine());
		request.setRefNo(invLink.getRefNumber());
		request.setNotes(request.getPhoneNumber() + " : Resend WhatsApp Account Info");
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setEmail(auditTrail.getEmail());
		auditTrailBean.setInvLink(invLink);
		auditTrailBean.setLovProcessType(auditTrail.getLovProcessType());
		auditTrailBean.setLovSendingPoint(auditTrail.getLovSendingPoint());
		auditTrailBean.setNotes(auditTrail.getNotes());
		auditTrailBean.setPhone(request.getPhoneNumber());
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(auditTrail.getAmMsUser());
		auditTrailBean.setVendorPsre(auditTrail.getMsVendor());
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_WA_GENINV);
		
		Status status = new Status();
		if ("1".equals(gs.getGsValue())) {
			balanceValidatorLogic.validateWhatsAppNotifBalanceAvailability(tenant, request.getPhoneNumber(), audit);
			boolean success = whatsAppLogic.sendMessageNotAsync(request, auditTrailBean, audit);
			if (success) {
				status.setCode(0);
				status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
						new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
			} else {
				throw new UserException(messageSource.getMessage("businesslogic.user.invitationnotsentviawa", null, this.retrieveLocaleAudit(audit)), ReasonUser.INVITATION_NOT_RESENT);
			}
		} else {
			LOG.info("Invitation Link sent via WhatsApp with template : {}.", msg.getTemplateCode());
			status.setCode(0);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
					new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
		}
		return status;
	}
	
	private Status resendInvitationWaHalosis(TrInvitationLink invLink, TrSigningProcessAuditTrail auditTrail, AuditContext audit) throws UnsupportedEncodingException {
		MsMsgTemplate msg;
		if (invLink.getMsTenant().getInvitationLinkActiveDuration() == null || invLink.getMsTenant().getInvitationLinkActiveDuration() == 0) {
			msg = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.CONST_INVITATION_LINK_BUTTON);
		} else {
			msg = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.CONST_INVITATION_LINK_ACTIVE_DURATION);
		}
		
		MsTenant tenant = invLink.getMsTenant();
		
		String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		
		String buttonText = encrypted;
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(invLink.getMsTenant().getTenantName());
		bodyTexts.add(encrypted);
		
		List<String> headerTexts = new ArrayList<>();
		headerTexts.add(msg.getSubject());
		
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(msg);
		request.setHeaderTexts(headerTexts);
		request.setBodyTexts(bodyTexts);
		request.setMsTenant(invLink.getMsTenant());
		request.setPhoneNumber(invLink.getReceiverDetail());
		request.setMsOffice(invLink.getMsOffice());
		request.setMsBusinessLine(invLink.getMsBusinessLine());
		request.setRefNo(invLink.getRefNumber());
		request.setMsTenant(tenant);
		request.setNotes(request.getPhoneNumber() + " : Resend WhatsApp Account Info");
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setEmail(auditTrail.getEmail());
		auditTrailBean.setInvLink(invLink);
		auditTrailBean.setLovProcessType(auditTrail.getLovProcessType());
		auditTrailBean.setLovSendingPoint(auditTrail.getLovSendingPoint());
		auditTrailBean.setNotes(auditTrail.getNotes());
		auditTrailBean.setPhone(request.getPhoneNumber());
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(auditTrail.getAmMsUser());
		auditTrailBean.setVendorPsre(auditTrail.getMsVendor());
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_WA_GENINV);
		
		Status status = new Status();
		if ("1".equals(gs.getGsValue())) {
			balanceValidatorLogic.validateWhatsAppNotifBalanceAvailability(tenant, request.getPhoneNumber(), audit);
			boolean success = whatsAppHalosisLogic.sendSynchronousMessage(request, auditTrailBean, audit);
			if (success) {
				status.setCode(0);
				status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
						new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
			} else {
				throw new UserException(messageSource.getMessage("businesslogic.user.invitationnotsentviawa", null, this.retrieveLocaleAudit(audit)), ReasonUser.INVITATION_NOT_RESENT);
			}
		} else {
			LOG.info("Invitation Link sent via WhatsApp with template : {}.", msg.getTemplateCode());
			status.setCode(0);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVITATION_SENT,
					new String[] { invLink.getReceiverDetail() }, this.retrieveLocaleAudit(audit)));
		}
		return status;
	}

	@Override
	public DataInvRegisResponse getDataInvRegis(DataInvRegisRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getParam())) {
			DataInvRegisResponse response = new DataInvRegisResponse();
			response.setPage(1);
			response.setTotalPage(1);
			response.setPage(0);
			response.setTotalPage(0);
			response.setTotalResult(0);

			Status status = new Status();
			status.setCode(0);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);

			response.setStatus(status);
			return response;
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		List<TrInvitationLink> invLink = this.getInvLink(StringUtils.trim(request.getParam()), tenant);
		List<InvLinkBean> list = new ArrayList<>();

		if (CollectionUtils.isEmpty(invLink)) {
			DataInvRegisResponse response = new DataInvRegisResponse();
			response.setPage(1);
			response.setTotalPage(1);
			response.setPage(0);
			response.setTotalPage(0);
			response.setTotalResult(0);

			Status status = new Status();
			status.setCode(0);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);

			response.setStatus(status);
			return response;
		}

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MMM-dd");

		for (int i = 0; i < invLink.size(); i++) {
			InvLinkBean bean = new InvLinkBean();
			bean.setUserName(invLink.get(i).getFullName());
			bean.setUserGender(invLink.get(i).getGender());
			bean.setKelurahan(invLink.get(i).getKelurahan());
			bean.setKecamatan(invLink.get(i).getKecamatan());
			bean.setKota(invLink.get(i).getKota());
			bean.setZipcode(invLink.get(i).getZipCode());
			bean.setUserDob(null == invLink.get(i).getDateOfBirth() ? null : sdf.format(invLink.get(i).getDateOfBirth()));
			byte[] bytePhotoSelf = invLink.get(i).getPhotoSelf();
			bean.setSelfPhoto((null == bytePhotoSelf) ? null : Base64.getEncoder().encodeToString(bytePhotoSelf));
			bean.setUserPob(invLink.get(i).getPlaceOfBirth());
			bean.setProvinsi(invLink.get(i).getProvinsi());
			bean.setEmail(invLink.get(i).getEmail());
			bean.setIdNo(invLink.get(i).getIdNo());
			bean.setUserPhone(invLink.get(i).getPhone());
			bean.setUserAddress(invLink.get(i).getAddress());
			bean.setIsActive(invLink.get(i).getIsActive());
			
			Date invitationDate = null != invLink.get(i).getDtmUpd() ? invLink.get(i).getDtmUpd() : invLink.get(i).getDtmCrt();
			bean.setInvCrt(MssTool.formatDateToStringIn(invitationDate, "dd-MMM-yyyy HH:mm:ss"));
			
			bean.setInvBy(invLink.get(i).getInvitationBy());
			bean.setRecieverDetail(invLink.get(i).getReceiverDetail());
			bean.setIsRegistered(this.checkInvRegisStatusV2(invLink.get(i), "reg", audit));
			bean.setResendActivationLinkStatus("1".equals(invLink.get(i).getIsActive()) ? invLink.get(i).getMsVendor().getResendActivationLink() : "0");
			bean.setVendorName(invLink.get(i).getMsVendor().getVendorName());
			bean.setVendorCode(invLink.get(i).getMsVendor().getVendorCode());
			
			boolean isEditable = invLinkLogic.isInvitationLinkEditable(invLink.get(i), audit);
			bean.setIsEditable(isEditable ? "1" : "0");
			
			boolean isRegenerable = invLinkLogic.isInvitationLinkRegenerable(invLink.get(i), audit);
			bean.setIsRegenerable(isRegenerable ? "1" : "0");
			list.add(bean);
		}

		DataInvRegisResponse response = new DataInvRegisResponse();
		response.setPage(1);
		response.setTotalPage(1);
		response.setListUser(list);
		response.setTotalResult(list.size());

		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);

		return response;
	}

	private List<TrInvitationLink> getInvLink(String param, MsTenant tenant) {
		List<TrInvitationLink> invLinks = daoFactory.getInvitationLinkDao().getListInvitationLinkByRecieverDetail(param, tenant.getTenantCode());
		if (CollectionUtils.isNotEmpty(invLinks)) {
			return invLinks;
		}
		
		if (StringUtils.isNumeric(param)) {
			invLinks = daoFactory.getInvitationLinkDao().getListInvitationByIdNo(param, tenant.getTenantCode());
			if (CollectionUtils.isNotEmpty(invLinks)) {
				return invLinks;
			}
			
			return daoFactory.getInvitationLinkDao().getListInvitationByPhone(param, tenant.getTenantCode());
		}
		
		return daoFactory.getInvitationLinkDao().getListInvitationByEmail(param, tenant.getTenantCode());
		
	}

	private void checkAmMsuserPhoneEmailNik(String phone, String email, String nik, AuditContext audit) {
		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue()
				: regexPhone;
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue()
				: regexEmail;

		if (StringUtils.isNotBlank(phone) && !phone.matches(phoneNoRegex)) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.invalidphoneno",
					new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_PHONE_NO);
		}

		if (StringUtils.isNotBlank(email) && !email.matches(emailRegex)) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.invalidemail",
					new Object[] { email }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_EMAIL);
		}

		AmMsuser userByIdNo = StringUtils.isBlank(nik) ? null : daoFactory.getUserDao().getUserByIdNo(nik);
		if (null != userByIdNo) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.nikalreadyregistered1",
					new Object[] { nik }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.UNKOWN);
		}

	}

	@Override
	public ListInvitationResponse getListInvitation(ListInvitationRequest request, AuditContext audit)
			throws ParseException {
		ListInvitationResponse response = new ListInvitationResponse();

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		Date dateStart = null;
		Date dateEnd = null;

		if (!isDateRangeValid(request.getTanggalPengirimanDari(), request.getTanggalPengirimanSampai(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { "listInvitation" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		if (StringUtils.isNotBlank(request.getTanggalPengirimanDari())) {
			dateStart = MssTool.formatStringToDate(request.getTanggalPengirimanDari() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);

		}

		if (StringUtils.isNotBlank(request.getTanggalPengirimanSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalPengirimanSampai() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}

		Integer totalData = daoFactory.getInvitationLinkDao().countListInvitation(request.getTenantCode(),
				request.getNama(), request.getPengirimanMelalui(), request.getPenerimaUndangan(),
				request.getStatusUndangan(), request.getStatusRegistrasi(), dateStart, dateEnd);
		double totalPage = Math.ceil((double) totalData / maxRow);

		List<InvitationBean> listInvitation = daoFactory.getInvitationLinkDao().getListInvitation(min, max, dateStart,
				dateEnd, request.getTenantCode(), request.getNama(), request.getPenerimaUndangan(),
				request.getPengirimanMelalui(), request.getStatusUndangan(), request.getStatusRegistrasi());

		response.setListInvitation(listInvitation);
		response.setPage(request.getPage());
		response.setTotalResult(totalData);
		response.setTotalPage((int) totalPage);

		return response;
	}

	@Override
	public InvitationLinkResponse getInvitationLink(InvitationLinkRequest request, AuditContext audit) {
		TrInvitationLink invLink = this.getTrInvitationLinkByReceiverDetailAndVendorCode(request.getReceiverDetail(),
				request.getTenantCode(), request.getVendorCode(), audit);

		InvitationLinkResponse response = new InvitationLinkResponse();
		String link = StringUtils.EMPTY;
		try {
			link = this.createInvitationLink(invLink.getInvitationCode(), audit);
		} catch (Exception e) {
			throw new InvitationLinkException(e.getLocalizedMessage(), ReasonInvitationLink.UNKOWN);
		}

		response.setInvitationLink(link);
		return response;
	}

	@Override
	public ListInvitationExcelReportResponse exportListInvitationReport(ListInvitationExcelReportRequest request,
			AuditContext audit) throws IOException {
		Date dateStart = null;
		Date dateEnd = null;

		if (StringUtils.isNotBlank(request.getTanggalPengirimanDari())) {
			dateStart = MssTool.formatStringToDate(request.getTanggalPengirimanDari() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if (StringUtils.isNotBlank(request.getTanggalPengirimanSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalPengirimanSampai() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}

		List<InvitationReportBean> result = daoFactory.getInvitationLinkDao().getListInvitationReport(dateStart,
				dateEnd, request.getTenantCode(), request.getNama(), request.getPengirimanMelalui(),
				request.getPenerimaUndangan(), request.getStatusUndangan(), request.getStatusRegistrasi());

		ListInvitationExcelReportResponse response = new ListInvitationExcelReportResponse();
		response.setBase64ExcelReport(this.generateInvitationListExcelFile(result, audit));
		response.setFilename(this.generateInvitationListExcelFilename(request));

		return response;
	}

	private String generateInvitationListExcelFilename(ListInvitationExcelReportRequest request) {
		StringBuilder filename = new StringBuilder();
		filename.append("IL_REPORT");
		if (StringUtils.isNotBlank(request.getNama())) {
			filename.append("_").append(request.getNama());
		}
		if (StringUtils.isNotBlank(request.getPenerimaUndangan())) {
			filename.append("_").append(request.getPenerimaUndangan());
		}
		if (StringUtils.isNotBlank(request.getPengirimanMelalui())) {
			filename.append("_").append(request.getPengirimanMelalui());
		}
		if (StringUtils.isNotBlank(request.getStatusRegistrasi())) {
			filename.append("_").append(request.getStatusRegistrasi());
		}
		if (StringUtils.isNotBlank(request.getStatusUndangan())) {
			filename.append("_").append(request.getStatusUndangan());
		}
		if (StringUtils.isNotBlank(request.getTanggalPengirimanDari())) {
			filename.append("_").append(request.getTanggalPengirimanDari());
		}
		if (StringUtils.isNotBlank(request.getTanggalPengirimanSampai())) {
			filename.append("_").append(request.getTanggalPengirimanSampai());
		}

		filename.append(" ").append(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT_SDT_REPORT));
		filename.append(".xlsx");
		return filename.toString();
	}

	private String generateInvitationListExcelFile(List<InvitationReportBean> listInvitation, AuditContext audit) {
		byte[] excelByteArray = null;
		try {
			excelByteArray = excelLogic.generate((workbook, styleBoldText) -> this
					.createInvitationListExcelSheet(workbook, styleBoldText, listInvitation));
		} catch (IOException e) {
			throw new UserException(messageSource.getMessage("businesslogic.invitationlink.invalidexcel", null,
					retrieveLocaleAudit(audit)), ReasonUser.GENERATE_IL_REPORT_ERROR);
		}

		return Base64.getEncoder().encodeToString(excelByteArray);
	}

	private Object createInvitationListExcelSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText,
			List<InvitationReportBean> listInvitation) {
		XSSFSheet mainSheet = workbook.createSheet("Laporan Invitation List");

		String[] column = new String[] { "Nama", "Pengiriman Melalui", "Penerima Undangan", "Tanggal Pengiriman",
				"Tanggal Registrasi", "Status Undangan", "Status Registrasi" };

		for (int i = 0; i < column.length; i++) {
			switch (i) {
			case 0: // Nama
				mainSheet.setColumnWidth(i, 7000);
				break;
			case 1: // PengirimanMelalui
				mainSheet.setColumnWidth(i, 5000);
				break;
			case 2: // PenerimaUndangan
				mainSheet.setColumnWidth(i, 4000);
				break;
			case 3: // TanggalPengiriman
				mainSheet.setColumnWidth(i, 4000);
				break;
			case 4: // getTanggalRegistrasi
				mainSheet.setColumnWidth(i, 5000);
				break;
			case 5: // Status Undangan
				mainSheet.setColumnWidth(i, 11000);
				break;
			case 6: // Status Registrasi
				mainSheet.setColumnWidth(i, 5000);
				break;
			default:
				mainSheet.setColumnWidth(i, 5000);
				break;
			}
		}

		XSSFRow rowOne = mainSheet.createRow(0);
		for (int i = 0; i < column.length; i++) {
			XSSFCell cell = rowOne.createCell(i);
			cell.setCellValue(column[i]);
			cell.setCellStyle(styleBoldText);
		}

		for (int i = 0; i < listInvitation.size(); i++) {
			XSSFRow row = mainSheet.createRow(i + 1);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);

			InvitationReportBean bean = listInvitation.get(i);
			cellOne.setCellValue(bean.getNama());
			cellTwo.setCellValue(bean.getPengirimanMelalui());
			cellThree.setCellValue(bean.getPenerimaUndangan());
			cellFour.setCellValue(bean.getTanggalPengiriman());
			cellFive.setCellValue(bean.getTanggalRegistrasi());
			cellSix.setCellValue(bean.getStatusUndangan());
			cellSeven.setCellValue(bean.getStatusRegistrasi());
		}

		return null;
	}

	private String updateEmailPhoneMVendorRegisteredUser(UpdateUserRequest request, MsVendorRegisteredUser vru,
			Status status, AuditContext audit) {

		String oldPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());
		String oldEmail = vru.getSignerRegisteredEmail();
		String newPhone = "";
		String newEmail = "";
		String message = "";

		if (StringUtils.isNotBlank(request.getEmail()) && !oldEmail.equals(StringUtils.upperCase(request.getEmail()))) {
			newEmail = request.getEmail();
		}

		if (StringUtils.isNotBlank(request.getPhone()) && !oldPhone.equals(request.getPhone())) {
			newPhone = request.getPhone();
		}

		if (StringUtils.isNotBlank(request.getIsActive())) {
			vru.setIsActive(request.getIsActive());
		}

		this.checkAmMsuserPhoneEmailNik(newPhone, newEmail, null, audit);

		if ((StringUtils.isBlank(request.getPhone()) || oldPhone.equals(request.getPhone()))
				&& (StringUtils.isBlank(request.getEmail()) || oldEmail.equals(request.getEmail()))) {
			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(this.messageSource.getMessage(MSG_NO_UPDATE, null, this.retrieveLocaleAudit(audit)));
		} else {
			if (!oldPhone.equals(request.getPhone())) {
				vru.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getPhone()));
				vru.setPhoneBytea(personalDataEncLogic.encryptFromString(request.getPhone()));
			}

			if (!vru.getSignerRegisteredEmail().equals(StringUtils.upperCase(request.getEmail()))) {
				vru.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
				vru.setEmailService("0");
			}
		}
		return message;
	}
	
	private MsVendorRegisteredUser validateUpdateUserRequest(UpdateUserRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {CONST_EMAIL}, audit), ReasonUser.LOGIN_ID_EMPTY);
		}
		
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getLoginId(), request.getVendorCode());
		if (vru == null) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND,
					new String[] { "User" }, audit), ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}
		
		// Reject jika email service = 1 dan email berubah
		if ("1".equals(vru.getEmailService()) && !request.getLoginId().equalsIgnoreCase(request.getEmail())) {
			throw new UserException(getMessage("businesslogic.global.cannotbechanged", new String[] {CONST_EMAIL}, audit), ReasonUser.INVALID_FORMAT);
		}
		
		return vru;
	}

	@Override
	public UpdateUserResponse updateUser(UpdateUserRequest request, AuditContext audit) throws ParseException, IOException {
		
		UpdateUserResponse response = new UpdateUserResponse();
		Status status = new Status();
		String message = "";
		
		MsVendorRegisteredUser vru = validateUpdateUserRequest(request, audit);

		AmMsuser user = daoFactory.getUserDao().getUserByIdMsUser(vru.getAmMsuser().getIdMsUser());
		PersonalDataBean bean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);

		// TODO: Refactor supaya lebih jelas, yang sekarang sulit dimengerti
		if (!user.getLoginId().equals(request.getLoginId()) 
				&& vru.getIsActive().equals("0")
				&& vru.getMsVendor().getEditAfterSend().equals("1")) {
			
			message = updateEmailPhoneMVendorRegisteredUser(request, vru, status, audit);

			vru.setUsrUpd(audit.getCallerId());
			vru.setDtmUpd(new Date());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
			
		} else if (isNeedUpdate(request, user, bean)) {
			if (vru.getIsRegistered().equals("0") && vru.getIsActive().equals("0")
					&& vru.getMsVendor().getEditAfterSend().equals("1")) {
				message = updateUnregisteredUser(user, request, bean, vru, audit);
			} else if (vru.getIsActive().equals("0") && vru.getMsVendor().getEditAfterSend().equals("1")) {
				message = updateRegisteredUser(user, request, bean, vru, status, audit);
			} else {
				if (vru.getIsActive().equals("1")) {
					status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
					status.setMessage(getMessage("businesslogic.user.cannotupdate", null, audit));
				} else if (vru.getMsVendor().getEditAfterSend().equals("0")) {
					status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
					status.setMessage(getMessage("businesslogic.vendor.cannotupdatevendor", null, audit));
				}
			}

			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());

			bean.getUserPersonalData().setUsrUpd(audit.getCallerId());
			bean.getUserPersonalData().setDtmUpd(new Date());

			vru.setUsrUpd(audit.getCallerId());
			vru.setDtmUpd(new Date());

			daoFactory.getUserDao().updateUser(user);
			daoFactory.getUserDao().updateUserPersonalData(bean);
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
		} else {
			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(this.messageSource.getMessage(MSG_NO_UPDATE, null, this.retrieveLocaleAudit(audit)));
		}

		if (StringUtils.isBlank(status.getMessage())) {
			status.setMessage(StringUtils.isBlank(message) ? GlobalVal.SERVICES_RESULT_SUCCESS : message);
		}
		response.setStatus(status);
		return response;
	}

	private String updateUnregisteredUser(AmMsuser user, UpdateUserRequest request, PersonalDataBean bean,
			MsVendorRegisteredUser vru, AuditContext audit) throws ParseException {
		String newPhone = "";
		String newEmail = "";
		String newIdNo = "";
		String message = "";
		String oldPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());

		if (StringUtils.isNotBlank(request.getEmail()) && !user.getLoginId().equals(request.getEmail())) {
			newEmail = request.getEmail();
		}

		if (StringUtils.isNotBlank(oldPhone) && StringUtils.isNotBlank(request.getPhone())
				&& !oldPhone.equals(request.getPhone())) {
			newPhone = request.getPhone();
		}

		if (StringUtils.isNotBlank(request.getIdNo()) && !bean.getIdNoRaw().equals(request.getIdNo())) {
			newIdNo = request.getIdNo();
		}

		this.checkAmMsuserPhoneEmailNik(newPhone, newEmail, newIdNo, audit);

		if (StringUtils.isNotBlank(request.getEmail())) {
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			bean.getUserPersonalData().setEmail(StringUtils.upperCase(request.getEmail()));
			vru.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vru.setEmailService("0");
			user.setEmailService("0");
		}

		if (StringUtils.isNotBlank(request.getPhone()) && !oldPhone.equals(request.getPhone())) {
			user.setHashedPhone(MssTool.getHashedString(request.getPhone()));
			vru.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getPhone()));
			vru.setPhoneBytea(personalDataEncLogic.encryptFromString(request.getPhone()));
			bean.setPhoneRaw(request.getPhone());
		}

		if (StringUtils.isNotBlank(request.getIdNo()) && !bean.getIdNoRaw().equals(request.getIdNo())) {
			bean.setIdNoRaw(request.getIdNo());
			user.setHashedIdNo(MssTool.getHashedString(request.getIdNo()));
		}

		if (StringUtils.isNotBlank(request.getName())
				&& !user.getFullName().equals(StringUtils.upperCase(request.getName()))) {
			user.setFullName(StringUtils.upperCase(request.getName()));
		}

		if (StringUtils.isNotBlank(request.getPob())
				&& !bean.getUserPersonalData().getPlaceOfBirth().equals(StringUtils.upperCase(request.getPob()))) {
			bean.getUserPersonalData().setPlaceOfBirth(StringUtils.upperCase(request.getPob()));
		}

		SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
		if (StringUtils.isNotBlank(request.getDob())
				&& !bean.getUserPersonalData().getDateOfBirth().equals(sdf.parse(request.getDob()))) {
			bean.getUserPersonalData().setDateOfBirth(sdf.parse(request.getDob()));
		}

		if (StringUtils.isNotBlank(request.getGender())
				&& !bean.getUserPersonalData().getGender().equals(StringUtils.upperCase(request.getGender()))) {
			bean.getUserPersonalData().setGender(StringUtils.upperCase(request.getGender()));
		}

		if (StringUtils.isNotBlank(request.getAddress()) && !bean.getAddressRaw().equals(request.getAddress())) {
			bean.setAddressRaw(request.getAddress());
		}

		if (StringUtils.isNotBlank(request.getProvinsi()) && !bean.getUserPersonalData().getZipcodeBean().getProvinsi()
				.equals(StringUtils.upperCase(request.getProvinsi()))) {
			bean.getUserPersonalData().getZipcodeBean().setProvinsi(StringUtils.upperCase(request.getProvinsi()));
		}

		if (StringUtils.isNotBlank(request.getKota()) && !bean.getUserPersonalData().getZipcodeBean().getKota()
				.equals(StringUtils.upperCase(request.getKota()))) {
			bean.getUserPersonalData().getZipcodeBean().setKota(StringUtils.upperCase(request.getKota()));
		}

		if (StringUtils.isNotBlank(request.getKecamatan()) && !bean.getUserPersonalData().getZipcodeBean()
				.getKecamatan().equals(StringUtils.upperCase(request.getKecamatan()))) {
			bean.getUserPersonalData().getZipcodeBean().setKecamatan(StringUtils.upperCase(request.getKecamatan()));
		}

		if (StringUtils.isNotBlank(request.getKelurahan()) && !bean.getUserPersonalData().getZipcodeBean()
				.getKelurahan().equals(StringUtils.upperCase(request.getKelurahan()))) {
			bean.getUserPersonalData().getZipcodeBean().setKelurahan(StringUtils.upperCase(request.getKelurahan()));
		}

		if (StringUtils.isNotBlank(request.getZipCode())
				&& !bean.getUserPersonalData().getZipcodeBean().getZipcode().equals(request.getZipCode())) {
			bean.getUserPersonalData().getZipcodeBean().setZipcode(request.getZipCode());
		}

		return message;
	}

	private String updateRegisteredUser(AmMsuser user, UpdateUserRequest request, PersonalDataBean bean,
			MsVendorRegisteredUser vru, Status status, AuditContext audit) throws IOException {
		String oldPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());
		String oldEmail = user.getLoginId();
		String newPhone = "";
		String newEmail = "";
		String message = "";

		if (StringUtils.isNotBlank(request.getEmail())
				&& !user.getLoginId().equals(StringUtils.upperCase(request.getEmail()))) {
			newEmail = request.getEmail();
		}

		if (StringUtils.isNotBlank(request.getPhone()) && !oldPhone.equals(request.getPhone())) {
			newPhone = request.getPhone();
		}

		if (StringUtils.isNotBlank(request.getIsActive())) {
			vru.setIsActive(request.getIsActive());
		}

		this.checkAmMsuserPhoneEmailNik(newPhone, newEmail, null, audit);
		if ((StringUtils.isBlank(request.getPhone()) || oldPhone.equals(request.getPhone()))
				&& (StringUtils.isBlank(request.getEmail()) || user.getLoginId().equals(request.getEmail()))) {
			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(getMessage(MSG_NO_UPDATE, null, audit));
		} else {
			if (!oldPhone.equals(request.getPhone()) || StringUtils.isEmpty(vru.getHashedSignerRegisteredPhone())) {
				user.setHashedPhone(MssTool.getHashedString(request.getPhone()));
				vru.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getPhone()));
				vru.setPhoneBytea(personalDataEncLogic.encryptFromString(request.getPhone()));
				bean.setPhoneRaw(request.getPhone());
				newPhone = request.getPhone();
			}

			if (!user.getLoginId().equals(StringUtils.upperCase(request.getEmail()))) {
				user.setLoginId(StringUtils.upperCase(request.getEmail()));
				bean.getUserPersonalData().setEmail(StringUtils.upperCase(request.getEmail()));
				vru.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
				newEmail = request.getEmail();
				vru.setEmailService("0");
				user.setEmailService("0");
			}
			
			updateInvitationLinkWithNewEmailPhone(vru, newEmail, oldEmail, newPhone, oldPhone, audit);
		}

		if (request.getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			ChangeEmailPhoneRequestBean reqBean = new ChangeEmailPhoneRequestBean();
			reqBean.setNewEmail(StringUtils.isBlank(newEmail) ? user.getLoginId() : newEmail);
			reqBean.setNewPhone(StringUtils.isBlank(newPhone) ? bean.getPhoneRaw() : newPhone);
			reqBean.setOldEmail(oldEmail);
			reqBean.setOldPhone(oldPhone);
			MsUseroftenant uot = daoFactory.getUseroftenantDao().getLatestUserTenant(oldEmail);
			this.updateDataUserDigi(reqBean, uot.getMsTenant(), audit);
		}
		return message;
	}
	
	private void updateInvitationLinkWithNewEmailPhone(MsVendorRegisteredUser vendorUser, String newEmail, String oldEmail, String newPhone, String oldPhone, AuditContext audit) {
		MsVendor vendor = vendorUser.getMsVendor();
		
		// Edit invitation SMS
		if ("1".equals(vendorUser.getEmailService())) {
			
			TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetailAndIdMsVendor(oldPhone, vendor.getIdMsVendor());
			if (StringUtils.isNotBlank(newPhone)) {
				invLink.setReceiverDetail(newPhone);
				invLink.setPhone(newPhone);
			}
			invLink.setDtmUpd(new Date());
			invLink.setUsrUpd(audit.getCallerId());
			daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
			return;
		}
		
		// Edit invitation Email
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetailAndIdMsVendor(oldEmail, vendor.getIdMsVendor());
		if (StringUtils.isNotBlank(newEmail)) {
			invLink.setReceiverDetail(StringUtils.upperCase(newEmail));
			invLink.setEmail(StringUtils.upperCase(newEmail));
		}
		if (StringUtils.isNotBlank(newPhone)) {
			invLink.setPhone(newPhone);
		}
		
		invLink.setDtmUpd(new Date());
		invLink.setUsrUpd(audit.getCallerId());
		daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		
	}

	private void updateDataUserDigi(ChangeEmailPhoneRequestBean reqBean, MsTenant tenant, AuditContext audit)
			throws IOException {
		ChangeEmailPhoneDigiRequest request = new ChangeEmailPhoneDigiRequest();
		request.setBean(reqBean);
		ChangeEmailPhoneDigiResponse response = digisignLogic.changeEmailPhoneDigi(request, tenant, audit);

		if (!response.getBean().getResult().equals(GlobalVal.STATUS_DIGISIGN_SUCCESS)) {
			throw new DigisignException(response.getBean().getNotif());
		}
	}

	private boolean isNeedUpdate(UpdateUserRequest request, AmMsuser user, PersonalDataBean bean)
			throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("dd/mm/yyyy");

		return !user.getLoginId().equals(request.getEmail()) || !bean.getIdNoRaw().equals(request.getIdNo())
				|| !user.getFullName().equals(request.getName())
				|| !bean.getUserPersonalData().getPlaceOfBirth().equals(request.getPob())
				|| !bean.getUserPersonalData().getDateOfBirth().equals(sdf.parse(request.getDob()))
				|| !bean.getUserPersonalData().getGender().equals(request.getGender())
				|| !bean.getPhoneRaw().equals(request.getPhone()) || !bean.getAddressRaw().equals(request.getAddress())
				|| !bean.getUserPersonalData().getZipcodeBean().getProvinsi().equals(request.getProvinsi())
				|| !bean.getUserPersonalData().getZipcodeBean().getKota().equals(request.getKota())
				|| !bean.getUserPersonalData().getZipcodeBean().getKecamatan().equals(request.getKecamatan())
				|| !bean.getUserPersonalData().getZipcodeBean().getKelurahan().equals(request.getKelurahan())
				|| !bean.getUserPersonalData().getZipcodeBean().getZipcode().equals(request.getZipCode())
				|| user.getLoginId().equals(request.getLoginId());
	}

	@Override
	public MyProfileResponse getMyProfile(MyProfileRequest request, AuditContext audit) {
		MyProfileResponse response = new MyProfileResponse();

		String phoneNumber;
		boolean checkUserExistence = true;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), checkUserExistence, audit);

		List<MsVendorRegisteredUser> listVendorUser = daoFactory.getVendorRegisteredUserDao()
				.getListVendorRegisteredUserByLoginId(user.getLoginId());
		List<MyProfileBean> listBean = new ArrayList<>();

		response.setNama(user.getFullName());
		response.setTenantCode(request.getTenantCode());
		for (int i = 0; i < listVendorUser.size(); i++) {
			MyProfileBean bean = new MyProfileBean();
			bean.setEmail(listVendorUser.get(i).getSignerRegisteredEmail());
			if (listVendorUser.get(i).getPhoneBytea() != null) {
				phoneNumber = personalDataEncLogic.decryptToString(listVendorUser.get(i).getPhoneBytea());
			} else {
				phoneNumber = "-";
			}
			bean.setPhoneNumber(phoneNumber);
			bean.setVendor(listVendorUser.get(i).getMsVendor().getVendorCode());
			listBean.add(bean);
		}
		response.setBean(listBean);
		return response;
	}

	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}

		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}

	@Override
	public InquiryEditUserResponse getInquiryEditUser(InquiryEditUserRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] { CONST_TENANT }, retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new String[] { CONST_TENANT }, this.retrieveLocaleAudit(audit)), ReasonUser.TENANT_NOT_FOUND);
		}

		InquiryEditUserResponse response = new InquiryEditUserResponse();
		List<InquiryEditUserBean> userList = new ArrayList<>();
		List<Map<String, Object>> idUserList = daoFactory.getUserDao()
				.getListInquiryEditUserId(request.getUserIdentifier(), request.getTenantCode());
		Iterator<Map<String, Object>> itr = idUserList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BigInteger idMsvendorRegisteredUser = (BigInteger) map.get("d0");

			MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByIdMsVendorRegisteredUser(idMsvendorRegisteredUser.longValue());
			if (vuser == null) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND,
								new String[] { "User" }, this.retrieveLocaleAudit(audit)),
						ReasonUser.PERSONAL_DATA_NOT_FOUND);
			}
			AmMsuser user = daoFactory.getUserDao().getUserByIdMsUser(vuser.getAmMsuser().getIdMsUser());
			PersonalDataBean personalData = daoFactory.getUserDao()
					.getUserDataByIdMsUser(vuser.getAmMsuser().getIdMsUser(), false);

			AmUserPersonalData userPersonalData = personalData.getUserPersonalData();
			if(userPersonalData == null) {
				return response;
			}
			ZipcodeCityBean zipcodeBean = userPersonalData.getZipcodeBean();

			String vuserphone;
			if (vuser.getPhoneBytea() == null) {
				vuserphone = "";
			} else {
				vuserphone = personalDataEncLogic.decryptToString(vuser.getPhoneBytea());
			}

			// pengecekan status edit
			String statusEdit = "";
			if ((vuser.getMsVendor().getEditAfterSend().equals("0") || vuser.getIsActive().equals("1"))
					|| (vuser.getIsRegistered().equals("1")
							&& vuser.getMsVendor().getEditAfterRegister().equals("0"))) {
				statusEdit = "0";
			} else {
				statusEdit = "1";
			}

			// pengecekan resend link aktivasi
			String resendlinkaktivasi = "";
			if (vuser.getIsRegistered().equals("0") || vuser.getIsActive().equals("1")) {
				resendlinkaktivasi = "0";
			} else {
				resendlinkaktivasi = vuser.getMsVendor().getResendActivationLink();
			}
			
			// pengecekan certificate status
			
			String certificateStatus = ACTIVE;

			if (null == vuser.getCertExpiredDate()) {
				certificateStatus = NOT_ACTIVE;
			}
			
			if (null != vuser.getCertExpiredDate() && GlobalVal.VENDOR_CODE_VIDA.equals(vuser.getMsVendor().getVendorCode()) && userValidatorLogic.isCertifExpiredForInquiry(vuser)) {
				certificateStatus = GlobalVal.CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS;
			}

			TrDocumentH documentH = daoFactory.getDocumentDao().getLatestUnsignedAgreement(vuser.getAmMsuser(), tenant, vuser.getMsVendor());
			String refNumber = (null == documentH) ? null : documentH.getRefNumber();

			InquiryEditUserBean bean = new InquiryEditUserBean();
			if (null == refNumber) {
				bean.setSignLink("0");
			} else {
				bean.setSignLink("1");
			}
			bean.setIdMsUser(vuser.getAmMsuser().getIdMsUser());
			bean.setEmail(vuser.getSignerRegisteredEmail());
			bean.setVendorCode(vuser.getMsVendor().getVendorCode());
			bean.setVendorName(vuser.getMsVendor().getVendorName());
			bean.setNik(personalData.getIdNoRaw());
			bean.setName(user.getFullName());
			bean.setPlaceOfBirth(userPersonalData.getPlaceOfBirth());
			bean.setDateOfBirth(MssTool.formatDateToStringIn(userPersonalData.getDateOfBirth(), GlobalVal.DATE_FORMAT));
			bean.setGender(userPersonalData.getGender());
			bean.setPhone(vuserphone);
			bean.setAddress(personalData.getAddressRaw());
			bean.setProvinsi(zipcodeBean.getProvinsi());
			bean.setKota(zipcodeBean.getKota());
			bean.setKecamatan(zipcodeBean.getKecamatan());
			bean.setKelurahan(zipcodeBean.getKelurahan());
			bean.setKodePos(zipcodeBean.getZipcode());
			bean.setRefNumber(refNumber);
			bean.setRegistrationStatus(labelRegistrationStatus(vuser));
			bean.setEmailService(vuser.getEmailService());
			bean.setResendActivationLink(resendlinkaktivasi);
			bean.setStatusEdit(statusEdit);
			bean.setIsRegistered(vuser.getIsRegistered());
			bean.setCertificateStatus(certificateStatus);
			boolean hideEditButton = "0".equals(vuser.getAmMsuser().getIsActive()) || "1".equals(vuser.getIsActive());
			bean.setHideEditButton(hideEditButton);
			userList.add(bean);
		}

		response.setPage(1);
		response.setTotalPage(1);
		response.setTotalResult(userList.size());
		response.setUserList(userList);
		return response;
	}

	private String labelRegistrationStatus(MsVendorRegisteredUser vendorRegisteredUser) {
		if ("1".equals(vendorRegisteredUser.getIsActive())) {
			return ACT_STATUS_HAS_ACT;
		} else if ("1".equals(vendorRegisteredUser.getIsRegistered())) {
			return ACT_STATUS_NOT_ACT;
		}
		return ACT_STATUS_NOT_REGIS;
	}

	private void insertGenerateInvErrorHistory(MsTenant tenant, String custName, String errorType, String errorMessage,
			MsVendor vendor, AuditContext audit) {

		MsLov lovModul = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ERR_HIST_MODULE,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_GEN_INV);

		TrErrorHistory errorHistory = new TrErrorHistory();
		errorHistory.setMsLov(lovModul);
		errorHistory.setCustName(StringUtils.upperCase(custName));
		errorHistory.setMsTenant(tenant);
		errorHistory.setErrorType(errorType);
		errorHistory.setErrorDate(new Date());
		errorHistory.setErrorMessage(errorMessage);
		errorHistory.setUsrCrt(audit.getCallerId());
		errorHistory.setDtmCrt(new Date());
		errorHistory.setMsVendor(vendor);
		daoFactory.getErrorHistoryDao().insertErrorHistory(errorHistory);

	}

	@Override
	public CreateEmailResponse createEmail(CreateEmailRequest request, AuditContext audit) throws IOException {
		List<String> emails = new ArrayList<>();
		String isActive = "1";
		
		for (Map<String, String> user : request.getNames()) {
			String address = user.get("name");
			String pass = daoFactory.getUserDao().getPassDefault(isActive);
			StringBuilder createEmailUri = new StringBuilder();
			createEmailUri.append("https://docsol.id:2083/execute/Email/add_pop?email=").append(address)
					.append("&password=").append(pass) ;

			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			String authorizationHeader = Base64.getEncoder()
					.encodeToString(("docsol" + ":" + "edkWnfEYpjXD").getBytes());
			mapHeader.add(HttpHeaders.AUTHORIZATION, "Basic " + authorizationHeader);
			WebClient client = WebClient.create(createEmailUri.toString()).headers(mapHeader);
			Response response = client.post(null);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String result = IOUtils.toString(isReader);
			LOG.info("Create Email Account result: {}", result);

			String fullAddress = address + "@" + "docsol.id";

			addEmailAccountForwarder(fullAddress, mapHeader);
			emails.add(fullAddress);
		}
		CreateEmailResponse response = new CreateEmailResponse();
		response.setEmails(emails);
		return response;
	}

	private void addEmailAccountForwarder(String address, MultivaluedMap<String, String> mapHeader) throws IOException {
		StringBuilder addForwarderUri = new StringBuilder();
		addForwarderUri.append("https://docsol.id:2083/execute/Email/add_forwarder?domain=docsol.id&email=")
				.append(address).append("&fwdopt=fwd&fwdemail=<EMAIL>");

		WebClient client = WebClient.create(addForwarderUri.toString()).headers(mapHeader);
		Response response = client.post(null);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		LOG.info("Add Forwarded result: {}", result);
	}

	@Override
	public ReturnlinkAktivasiTekenAjaResponse checkKodeEncryptValid(ReturnlinkAktivasiTekenAjaRequest request,
			AuditContext audit) {
		ReturnlinkAktivasiTekenAjaResponse response = new ReturnlinkAktivasiTekenAjaResponse();

		String decryptCode = commonLogic.decryptMessageToString(request.getMsg(), audit);
		AmMsuser user = daoFactory.getUserDao().getUserByOtpCode(decryptCode);

		if (user != null) {
			response.setUrl(user.getActivationLink());
			user.setActivationLink(null);
			user.setOtpCode(null);
			daoFactory.getUserDao().updateUser(user);
			LOG.info("aktivation link and otp code is deleted");
		} else {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_NOT_FOUND,
					new Object[] { "userFile" }, this.retrieveLocaleAudit(audit)), ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}
		return response;
	}

	@Override
	public boolean isNikUserActivated(String nik, String vendorCode, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(nik);
		if (null == user) {
			return false;
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendorCode);

		return null != vendorUser && "1".equals(vendorUser.getIsActive());
	}

	@Override
	public TekenAjaCallbackResponse callbackRegister(TekenAjaCallbackRequest request, AuditContext audit) {
		
		String email = request.getData().getEmail();
		MsVendorRegisteredUser vRUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, GlobalVal.VENDOR_CODE_TEKENAJA);

		if (vRUser == null) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND,
					new String[] { "User" }, audit), ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}
		
		if ("0".equals(vRUser.getIsRegistered())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED,
					new String[] { "User" }, audit), ReasonUser.NOT_REGISTERED);
		}
		
		if(StringUtils.isBlank(request.getData().getTekenId())) {
			throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_USER_HAS_NOT_REGISTERED, null, audit));			
		}
		
		Date activatedDate = new Date();
		
		if ("1".equals(vRUser.getIsExternalActivation())) {
			String receiverDetail = null;
			
			if ("1".equals(vRUser.getEmailService())) {
				receiverDetail = personalDataEncLogic.decryptToString(vRUser.getPhoneBytea());
			} else {
				receiverDetail = vRUser.getSignerRegisteredEmail();
			}
			
			TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvLinkByReceiverDetailAndVendorCode(receiverDetail, GlobalVal.VENDOR_CODE_TEKENAJA);
			if (invLink != null) {
				invLink.setIsActive("0");
				invLink.setGender(null);
				invLink.setKelurahan(null);
				invLink.setKecamatan(null);
				invLink.setKota(null);
				invLink.setZipCode(null);
				invLink.setPlaceOfBirth(null);
				invLink.setDateOfBirth(null);
				invLink.setProvinsi(null);
				invLink.setEmail(null);
				invLink.setPhotoSelf(null);
				invLink.setPhotoId(null);
				invLink.setIdNo(null);
				invLink.setUsrUpd(audit.getCallerId());
				invLink.setPhone(null);
				invLink.setAmMsuser(null);
				invLink.setAddress(null);
				invLink.setDtmUpd(new Date());
				daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
			}
			
			vRUser.setIsActive("1");
		}
		
		vRUser.setUsrUpd(audit.getCallerId());
		vRUser.setDtmUpd(new Date());
		vRUser.setActivatedDate(activatedDate);
		vRUser.setCertExpiredDate(DateUtils.addYears(activatedDate, 1));
		vRUser.setVendorRegistrationId(request.getData().getTekenId());
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vRUser);
		
		PersonalDataBean personalDataQueue = daoFactory.getUserDao().getUserDataByIdMsUser(vRUser.getAmMsuser().getIdMsUser(), false);
		String nikRaw = personalDataQueue.getIdNoRaw();
		UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(nikRaw, vRUser.getMsVendor().getIdMsVendor());
		QueuePublisher.queueUpdErrHistRerunProcess(bean);
		
		MsUseroftenant uot = daoFactory.getUseroftenantDao().getLatestUseroftenant(vRUser.getAmMsuser(), vRUser.getMsVendor());
		MsLov lovCallbackType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_CALLBACK_TYPE, GlobalVal.CODE_LOV_CALLBACK_TYPE_ACTIVATION_COMPLETE);
		callbackLogic.executeCallbackToClient(uot.getMsTenant(), lovCallbackType, vRUser, null, CALLBACK_ACTIVATION_COMPLETE_MESSAGE, audit);
		
		TekenAjaDownloadCertificateResponse responseCert = tekenAjaLogic.downloadCertificate(vRUser,uot.getMsTenant(), audit);
		String base64Cert = responseCert.getData();
		byte[] decodedCert = Base64.getDecoder().decode(base64Cert);
		cloudStorageLogic.storeCertificateTknAj(vRUser.getVendorRegistrationId(), decodedCert);
		
		return new TekenAjaCallbackResponse();
	}

	@Override
	public String getNikUserActivationStatus(String nik, String vendorCode, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(nik);
		if (null == user) {
			return ACT_STATUS_NOT_REGIS;
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendorCode);
		if (null == vendorUser || null == vendorUser.getIsRegistered() || "0".equals(vendorUser.getIsRegistered())) {
			return ACT_STATUS_NOT_REGIS;
		}

		if ("1".equals(vendorUser.getIsActive())) {
			return ACT_STATUS_HAS_ACT;
		} else {
			return ACT_STATUS_NOT_ACT;
		}
	}

	@Override
	public TekenAjaRegisterResponse registerNormalTekenAja(TekenAjaRegisterRequest request, AuditContext audit) {
		return doRegisterTekenAja(request, RegistrationType.NORMAL, audit);
	}

	private TekenAjaRegisterResponse doRegisterTekenAja(TekenAjaRegisterRequest request, RegistrationType registerType,
			AuditContext audit) {

		userValidatorLogic.validateTekenAjaRegisterRequest(request, registerType, audit);
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);

		if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			throw new VendorException(messageSource.getMessage(MSG_VENDORCODE_INVALID,
					new String[] { request.getVendorCode() }, retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}

		TekenAjaRegisterResponse response = new TekenAjaRegisterResponse();

		TknajRegisterCekResponse nikCheckResponse = tekenAjaLogic.registerCek(request.getUserData().getIdKtp(),
				TekenAjaConstant.REGCEK_ACTION_CHECK_NIK, vendor, tenant, audit);
		if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_VERIFIED.equals(nikCheckResponse.getCode())
				&& TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_VERIFIED.equals(nikCheckResponse.getMessage())) {
			insertTekenAjaUser(request.getUserData(), tenant, vendor, true, true, false, registerType, audit);
			UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(request.getUserData().getIdKtp(),
					vendor.getIdMsVendor());
			QueuePublisher.queueUpdErrHistRerunProcess(bean);
			Status status = new Status();
			status.setCode(StatusCode.TEKEN_AJA_ERROR);
			status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_WAIT_FOR_SIGN, null, audit));
			response.setStatus(status);
			return response;
		} else if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_UNVERIFIED.equals(nikCheckResponse.getCode())
				&& TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_UNVERIFIED.equals(nikCheckResponse.getMessage())) {
			tekenAjaLogic.registerCek(request.getUserData().getIdKtp(), TekenAjaConstant.REGCEK_ACTION_RESEND_EMAIL,
					vendor, tenant, audit);
			insertTekenAjaUser(request.getUserData(), tenant, vendor, true, false, false, registerType, audit);
			Status status = new Status();
			status.setCode(StatusCode.TEKEN_AJA_ERROR);
			status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_WAIT_FOR_SIGN, null, audit));
			response.setStatus(status);
			return response;

		} else if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_CERT_EXPIRED.equals(nikCheckResponse.getCode())
				&& TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_CERT_EXPIRED.equals(nikCheckResponse.getMessage())) {
			// Untuk sekarang, langsung return response. Nantinya kemungkinan akan tetap
			// melakukan registrasi
			LOG.info("NIK {} sudah terdaftar di TekenAja tapi sertifikat kadaluwarsa. User tidak dibuat.",
					request.getUserData().getIdKtp());
			Status status = new Status();
			status.setCode(StatusCode.TEKEN_AJA_ERROR);
			status.setMessage(tekenAjaLogic.buildRegisterCekErrorMessage(nikCheckResponse));
			response.setStatus(status);
			return response;
		}

		TekenAjaRegisterApiResponse registerResponse = tekenAjaLogic.registerUser(request, audit);
		cutTekenAjaVerifBalance(tenant, vendor, request.getUserData(), audit);
		if (!TekenAjaConstant.RESPONSE_STATUS_OK.equals(registerResponse.getStatus())) {
			String message = tekenAjaLogic.buildRegisterErrorMessage(registerResponse);
			Status status = new Status();
			status.setCode(StatusCode.TEKEN_AJA_ERROR);
			status.setMessage(message);
			response.setStatus(status);
			return response;
		}

		insertTekenAjaUser(request.getUserData(), tenant, vendor, true, false, false, registerType, audit);
		return new TekenAjaRegisterResponse();
	}

	private void cutTekenAjaVerifBalance(MsTenant tenant, MsVendor vendor, TekenAjaUserBean userData,
			AuditContext audit) {
		String notes;
		MsLov balanceTypeVrf = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxTypeVrf = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_UVRF);

		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(userData.getEmail());
		if (null != user) {
			PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
			notes = personalDataBean.getPhoneRaw();
		} else {
			notes = userData.getTlp();
		}

		Date trxDate = new Date();
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String refNo = StringUtils.EMPTY;
		int qty = -1;

		saldoLogic.insertBalanceMutation(null, null, null, balanceTypeVrf, trxTypeVrf, tenant, vendor, trxDate, refNo,
				qty, String.valueOf(nextTrxNo), user, notes, null, audit);
	}

	private void insertTekenAjaUser(TekenAjaUserBean userData, MsTenant tenant, MsVendor vendor, boolean isRegistered,
			boolean isActivated, boolean isReregister, RegistrationType registerType, AuditContext audit) {

		// Set password
		String hashedPassword = StringUtils.EMPTY;
		String password = StringUtils.EMPTY;
		if (RegistrationType.INVITATION_EMAIL == registerType || RegistrationType.INVITATION_SMS == registerType) {
			password = REGISTER_BY_INVITATION_PASSWORD;
			hashedPassword = password;
		} else if (RegistrationType.EMBED == registerType) {
			password = generateRandomPassword();
			hashedPassword = PasswordHash.createHash(password);
		}

		String roleCode = (RegistrationType.EMBED == registerType) ? GlobalVal.ROLE_BM_MF : GlobalVal.ROLE_CUSTOMER;
		String emailService = "1".equals(userData.getEmailService()) ? "1" : "0";
		String hashedPhone = MssTool.getHashedString(userData.getTlp());
		String hashedIdNo = MssTool.getHashedString(userData.getIdKtp());
		boolean isNewUser = false;

		MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(roleCode, tenant.getTenantCode());
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(userData.getIdEmailHosting());

		// Insert am_msuser
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(userData.getIdKtp());
		if (null == user) {
			isNewUser = true;

			// Memastikan No HP belum digunakan sebelum insert
			AmMsuser phoneUser = userValidatorLogic.validateGetUserByPhone(userData.getTlp(), false, audit);
			if (null != phoneUser) {
				throw new UserException(
						messageSource.getMessage(MSG_USER_PHONE_USEDBYOTHER,
								new String[] { userData.getTlp() }, retrieveLocaleAudit(audit)),
						ReasonUser.PHONE_NO_ALREADY_EXIST);
			}

			// Memastikan email belum digunakan sebelum insert
			AmMsuser emailUser = daoFactory.getUserDao().getUserByLoginId(userData.getEmail());
			if (null != emailUser) {
				throw new UserException(
						messageSource.getMessage("businesslogic.user.emailuserbyother",
								new String[] { userData.getEmail() }, retrieveLocaleAudit(audit)),
						ReasonUser.LOGIN_ID_NOT_UNIQUE);
			}

			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setFullName(StringUtils.upperCase(userData.getNama()));
			String[] separatedName = userData.getNama().split(" ");
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword(hashedPassword);
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("1");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUser(user);
		}

		// Insert am_userpwdhistory
		if (!REGISTER_BY_INVITATION_PASSWORD.equals(hashedPassword) && isNewUser) {
			MsLov pwdChangeType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE,
					GlobalVal.CODE_LOV_PWD_CHANGE_TYPE_NEW);
			AmUserpwdhistory passwordHistory = new AmUserpwdhistory();
			passwordHistory.setUsrCrt(audit.getCallerId());
			passwordHistory.setDtmCrt(new Date());
			passwordHistory.setAmMsuser(user);
			passwordHistory.setPassword(hashedPassword);
			passwordHistory.setMsLov(pwdChangeType);
			daoFactory.getUserDao().insertUserPwdhistory(passwordHistory);
		}

		// insert ms_useroftenant
		MsUseroftenant tenantUser = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(),
				tenant.getTenantCode());
		if (null == tenantUser) {
			tenantUser = new MsUseroftenant();
			tenantUser.setAmMsuser(user);
			tenantUser.setMsTenant(tenant);
			tenantUser.setDtmCrt(new Date());
			tenantUser.setUsrCrt(audit.getCallerId());
			daoFactory.getUserDao().insertUseroftenant(tenantUser);
		}

		// insert ms_vendor_registered_user
		String registerStatus = isRegistered ? "1" : "0";
		String activationStatus = isActivated ? "1" : "0";

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(userData.getEmail(),
						vendor.getVendorCode());
		if (null == vendorUser) {
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive(activationStatus);
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);

			if (isActivated) {
				Date activeDate = new Date();
				Date expiredDate = DateUtils.addYears(activeDate, 1);
				vendorUser.setActivatedDate(activeDate);
				vendorUser.setCertExpiredDate(expiredDate);
			}

			vendorUser.setIsRegistered(registerStatus);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			byte[] phoneBytea = personalDataEncLogic.encryptFromString(userData.getTlp());
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUser(vendorUser);
		} else {
			vendorUser.setIsActive(activationStatus);
			if (isActivated) {
				Date activeDate = new Date();
				Date expiredDate = DateUtils.addYears(activeDate, 1);
				vendorUser.setActivatedDate(activeDate);
				vendorUser.setCertExpiredDate(expiredDate);
			}
			vendorUser.setIsRegistered(registerStatus);
			vendorUser.setUsrUpd(audit.getCallerId());
			vendorUser.setDtmUpd(new Date());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vendorUser);
		}

		// insert am_user_personal_data
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		AmMsprovince province = provinceValidatorLogic.validateGetProvince(userData.getProvinceId(), true, audit);
		AmMsdistrict district = districtValidatorLogic.validateGetDistrict(userData.getDistrictId(), province, true,
				audit);
		AmMssubdistrict subdistrict = subdistrictValidatorLogic.validateGetSubdistrict(userData.getSubdistrictId(),
				district, true, audit);

		ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
		zipcodeCityBean.setProvinsi(province.getProvinceName());
		zipcodeCityBean.setKota(district.getDistrictName());
		zipcodeCityBean.setKecamatan(subdistrict.getSubdistrictName());
		zipcodeCityBean.setKelurahan(StringUtils.upperCase(userData.getKelurahan()));
		zipcodeCityBean.setZipcode(userData.getKodePos());

		Date dob = MssTool.formatStringToDate(userData.getTglLahir(), GlobalVal.DATE_FORMAT);

		if (null == personalData || null == personalData.getUserPersonalData()) {

			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getJenisKelamin()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getTmpLahir()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);

			if (StringUtils.isNotBlank(userData.getSelfPhoto())) {
				byte[] photo = MssTool.imageStringToByteArray(userData.getSelfPhoto());
				personalDataBean.setSelfPhotoRaw(photo);
			}
			if (StringUtils.isNotBlank(userData.getIdPhoto())) {
				byte[] photo = MssTool.imageStringToByteArray(userData.getIdPhoto());
				personalDataBean.setPhotoIdRaw(photo);
			}

			personalDataBean.setIdNoRaw(userData.getIdKtp());
			personalDataBean.setPhoneRaw(userData.getTlp());
			personalDataBean.setAddressRaw(StringUtils.upperCase(userData.getAlamat()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
		}

		// insert am_memberofrole
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofrole(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsuser(user);
			userRole.setAmMsrole(role);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRole(userRole);
		}

		// Update tr_reregistration_user if re-register
		if (isReregister) {
			String nik = userData.getIdKtp();
			String phone = userData.getTlp();
			String email = (RegistrationType.INVITATION_SMS == registerType) ? null : userData.getEmail();
			String vendorCode = vendor.getVendorCode();

			TrReregistrationUser reregistrationUser = daoFactory.getReregistrationUserDao().getRegistrationUser(nik,
					phone, email, vendorCode);
			if (null != reregistrationUser) {
				if (RegistrationType.INVITATION_SMS != registerType) {
					// Ga hapus kolom phone kalau register by invitation SMS
					reregistrationUser.setPhone(null);
				}
				reregistrationUser.setIdNo(null);
				reregistrationUser.setIsActive("0");
				reregistrationUser.setUsrUpd(audit.getCallerId());
				reregistrationUser.setDtmUpd(new Date());
				daoFactory.getReregistrationUserDao().updateReregistrationUser(reregistrationUser);
			}
		}

		if (RegistrationType.EMBED == registerType && isNewUser) {
			sendAccountInfoEmail(user.getFullName(), userData.getEmail(), password);
		}
	}

	@Override
	public RegisterResponse registerEmbedTekenAja(TekenAjaRegisterEmbedRequest request, AuditContext audit) {

		EmbedMsgBean msgBean = embedValidatorLogic.validateEmbedMessage(request.getMsg(), audit);
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(msgBean.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(msgBean.getOfficeCode(),
				msgBean.getTenantCode());

		if (null == office) {
			office = new MsOffice();
			office.setOfficeCode(StringUtils.upperCase(msgBean.getOfficeCode()));
			office.setIsActive("1");
			office.setOfficeName(msgBean.getOfficeName());
			office.setMsTenant(tenant);
			office.setUsrCrt(audit.getCallerId());
			office.setDtmCrt(new Date());
			daoFactory.getOfficeDao().insertOffice(office);
		}

		TekenAjaUserBean userData = request.getUserData();

		TekenAjaRegisterRequest regisRequest = new TekenAjaRegisterRequest();
		regisRequest.setUserData(userData);
		regisRequest.setTenantCode(tenant.getTenantCode());
		regisRequest.setVendorCode(request.getVendorCode());

		if (userCanReregister(userData.getIdKtp(), userData.getTlp(), userData.getEmail(), vendor.getVendorCode())) {
			return registerTekenAja(regisRequest, RegistrationType.EMBED, true, audit);
		}

		if (!emailPhoneRegisteredInVendor(userData.getEmail(), userData.getTlp(), vendor.getVendorCode())) {
			return registerTekenAja(regisRequest, RegistrationType.EMBED, false, audit);
		}

		if (!"1".equals(vendor.getReregistAvailable())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_ALREADY_REGISTERED,
					new String[] { vendor.getVendorName() }, audit), ReasonUser.ALREADY_REGISTERED);
		}

		Status status = new Status();
		status.setCode(StatusCode.USER_CAN_REREGISTER);
		status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_CAN_REREGISTER,
				new String[] { vendor.getVendorName() }, audit));
		RegisterResponse response = new RegisterResponse();
		response.setReregistrationAvailable("1");
		response.setStatus(status);
		return response;
	}

	@Override
	public ResetOtpCodeResponse resetOtpCode(ResetOtpCodeRequest request, AuditContext audit) {
		ResetOtpCodeResponse response = new ResetOtpCodeResponse();
		Status status = new Status();
		try {
			AmMsuser user;
			boolean checkUserExistence = true;
			if (StringUtils.isNumeric(request.getLoginId())) {
				user = userValidatorLogic.validateGetUserByPhone(request.getLoginId(), checkUserExistence, audit);
			} else {
				user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
			}
			user.setResetCodeRequestNum((short) 0);
			user.setDtmUpd(new Date());
			user.setResetCodeRequestDate(new Date());
			user.setUsrUpd(String.valueOf(user.getLoginId()));
			daoFactory.getUserDao().updateUser(user);
		} catch (Exception e) {
			response.setMessage("User tidak ditemukan");
			status.setCode(8103);
			response.setStatus(status);
		}

		return response;
	}

	private TrInvitationLink getTrInvitationLinkByReceiverDetailAndVendorCode(String receiverDetail, String tenantCode,
			String vendorCode, AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		if (null == vendor) {
			throw new VendorException(this.messageSource.getMessage("businesslogic.saldo.vendornotexist",
					new Object[] { vendorCode }, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_NOT_FOUND);
		}

		Long idMsVendor = vendor.getIdMsVendor();
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao()
				.getInvitationLinkByRecieverDetailAndIdMsVendor(receiverDetail, idMsVendor);
		if (null == invLink) {
			throw new InvitationLinkException(
					this.messageSource.getMessage("businesslogic.invitationlink.invitationdatainvalid", null,
							this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.INV_LINK_NOT_EXIST);
		}
		if ("0".equals(invLink.getIsActive())) {
			throw new InvitationLinkException(this.messageSource
					.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INACTIVE_LINK, null, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.INACTIVE_LINK);
		}
		if (!invLink.getMsTenant().getTenantCode().equalsIgnoreCase(tenantCode)) {
			throw new InvitationLinkException(this.messageSource.getMessage("businesslogic.user.invalidtenant", null,
					this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_TENANT);
		}
		return invLink;
	}

	@Override
	public UpdateReregisResponse updateReregister(UpdateReregisRequest request, AuditContext audit) {
		if (StringUtils.isBlank(audit.getCallerId())) {
			audit.setCallerId(request.getPhone());
		}

		GenerateInvLinkRequest invReq = new GenerateInvLinkRequest();
		invReq.setTenantCode(request.getTenantCode());
		GenerateInvLinkUserBean userBean = new GenerateInvLinkUserBean();
		userBean.setEmail(request.getEmail());
		userBean.setIdNo(request.getIdNo());
		userBean.setUserPhone(request.getPhone());
		userBean.setVendorCode(request.getVendorCode());
		GenerateInvLinkUserBean[] beans = new GenerateInvLinkUserBean[] { userBean };
		invReq.setUsers(beans);

		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		Status status = this.checkPhoneEmailNik(invReq, tenant, true, audit);

		if (status.getCode() != 0) {
			throw new UserException(status.getMessage(), ReasonUser.UNKNOWN);
		}

		String invCode = null;
		TrInvitationLink invLink = null;
		if (request.getMsg() != null) {
			invCode = commonLogic.decryptMessageToString(request.getMsg(), audit);
			invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCode(invCode);
		}

		if (null != invLink) {
			if (!request.getVendorCode().equalsIgnoreCase(invLink.getMsVendor().getVendorCode())) {
				throw new InvitationLinkException(
						messageSource.getMessage("businesslogic.vendor.vendorcodeinvlink",
								new Object[] { request.getVendorCode() }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.MISMATCH_VENDOR);
			}
			invLink.setIsActive("1");

			if (StringUtils.isBlank(request.getEmail())) {
				if (StringUtils.isBlank(request.getPhone())) {
					throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.fillatleastone",
							null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_PHONE_NO);
				}

				invLink.setInvitationBy("SMS");
				invLink.setReceiverDetail(request.getPhone());
				invLink.setPhone(request.getPhone());
				invLink.setEmail(null);
			} else {
				invLink.setInvitationBy(CONST_EMAIL);
				invLink.setReceiverDetail(StringUtils.upperCase(request.getEmail()));
				invLink.setEmail(StringUtils.upperCase(request.getEmail()));
				invLink.setPhone(StringUtils.isNotBlank(request.getPhone()) ? request.getPhone() : null);
			}
			
			invLink.setUsrUpd(audit.getCallerId());
			invLink.setDtmUpd(new Date());
			daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		}

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		TrReregistrationUser rereg = new TrReregistrationUser();
		rereg.setDtmCrt(new Date());
		rereg.setUsrCrt(audit.getCallerId());
		rereg.setEmail(StringUtils.upperCase(request.getEmail()));
		rereg.setPhone(request.getPhone());
		rereg.setIdNo(request.getIdNo());
		rereg.setIsActive("1");
		rereg.setMsVendor(vendor);
		daoFactory.getReregistrationUserDao().insertReregistrationUser(rereg);

		return new UpdateReregisResponse();
	}

	private String checkInvRegisStatusV2(TrInvitationLink invLink, String check, AuditContext audit) {
		AmMsuser user = null;
		if (StringUtils.isNumeric(invLink.getReceiverDetail())) {
			boolean checkUserExistence = false;
			user = userValidatorLogic.validateGetUserByPhone(invLink.getReceiverDetail(), checkUserExistence, audit);
			if (null == user) {
				user = daoFactory.getUserDao().getUserByIdNo(invLink.getIdNo());
			}
		} else {
			boolean checkUserExistence = false;
			user = userValidatorLogic.validateGetUserByEmailv2(invLink.getReceiverDetail(), checkUserExistence, audit);
		}

		if (null == user) {
			return "0";
		}

		MsVendorRegisteredUser vendorUserv2 = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(
				user.getIdMsUser(), invLink.getMsVendor().getVendorCode());
		if (null == vendorUserv2) {
			return "0";
		}

		if ("reg".equals(check)) {
			return ("1".equals(vendorUserv2.getIsRegistered()) ? "1" : "0");
		} else {
			return ("1".equals(vendorUserv2.getIsActive()) ? "1" : "0");
		}
	}
	
	private MsVendorRegisteredUser validateGetUserActivationStatusForgetPasswordDetailRequest(ActivationStatusResetPasswordRequest request, AuditContext audit) {
		List<MsVendorRegisteredUser> vendorUsers = null;
		
		MsVendorRegisteredUser vendorUser = null;
		
		if (StringUtils.isNumeric(request.getLoginId())) {
			vendorUsers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByPhone(request.getLoginId());
			
			if (vendorUsers.size() == 1) {
				vendorUser = vendorUsers.get(0);
		    }
			
			if (vendorUsers.size() > 1) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByPhoneandDtmUpd(request.getLoginId());
			}				
		} else {
			vendorUsers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserBySignerRegisteredEmail(request.getLoginId());
			
			if (vendorUsers.size() == 1) {
				vendorUser = vendorUsers.get(0);
		    }
			
			if (vendorUsers.size() > 1) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmailandDtmUpd(request.getLoginId());
			}	
			
		}
		return vendorUser;
	}
	
	private MsVendorRegisteredUser getVendorUserForSendOtpForgotPassword(ForgotPasswordRequest request, AuditContext audit) {
		List<MsVendorRegisteredUser> vendorUsers = null;
		MsVendorRegisteredUser vendorUser = null;
		
		if (StringUtils.isNumeric(request.getLoginId())) {
			vendorUsers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByPhone(request.getLoginId());
			
			if (vendorUsers.size() == 1) {
				vendorUser = vendorUsers.get(0);
		    }
			
			if (vendorUsers.size() > 1) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByPhoneandDtmUpd(request.getLoginId());
			}
			
		} else {
			vendorUsers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserBySignerRegisteredEmail(request.getLoginId());
			
			if (vendorUsers.size() == 1) {
				vendorUser = vendorUsers.get(0);
		    }
			
			if (vendorUsers.size() > 1) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmailandDtmUpd(request.getLoginId());
			}	
		}

		return vendorUser;
		
	}

	private MsVendorRegisteredUser getVendorUserForValidateOtpForgotPassword(ResetCodeVerificationRequest request, AuditContext audit) {
		List<MsVendorRegisteredUser> vendorUsers = null;
		MsVendorRegisteredUser vendorUser = null;
		
		if (StringUtils.isNumeric(request.getLoginId())) {
			vendorUsers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByPhone(request.getLoginId());
			
			if (vendorUsers.size() == 1) {
				vendorUser = vendorUsers.get(0);
		    }
			
			if (vendorUsers.size() > 1) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByPhoneandDtmUpd(request.getLoginId());
			}
			
		} else {
			vendorUsers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserBySignerRegisteredEmail(request.getLoginId());
			
			if (vendorUsers.size() == 1) {
				vendorUser = vendorUsers.get(0);
		    }
			
			if (vendorUsers.size() > 1) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmailandDtmUpd(request.getLoginId());
			}	
		}

		return vendorUser;
		
	}

	@Override
	public ActivationStatusResetPasswordResponse getUserActivationStatusForgetPassword(ActivationStatusResetPasswordRequest request, AuditContext audit) {
		
		MsVendorRegisteredUser vendorUser = validateGetUserActivationStatusForgetPasswordDetailRequest(request, audit);
		

		if (null == vendorUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit), ReasonUser.USER_NOT_FOUND);
		}
		
		AmMsuser user = vendorUser.getAmMsuser();
		
		if ("1".equals(user.getIsDormant())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIN_USER_DORMANT_1, null, audit), ReasonUser.INACTIVE_USER);
		}
		
		if (user.getPassword().equals(REGISTER_BY_INVITATION_PASSWORD)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_DOES_NOT_HAVE_ANY_DOC, null, audit),
					ReasonUser.USER_DOES_NOT_HAVE_ANY_DOCUMENT);
		}
		
		MsTenant msTenant = null;
		TrDocumentD trDocumentD = daoFactory.getDocumentDao().getLatestDocumentDetailBySentDateSigner(vendorUser.getAmMsuser());
		
		
		if (null == trDocumentD) {
			MsUseroftenant userOfTenant = daoFactory.getUseroftenantDao().getLatestUserTenant(vendorUser.getSignerRegisteredEmail());
			msTenant = userOfTenant.getMsTenant();
		} else {
			msTenant = trDocumentD.getMsTenant();
		}
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_OTP_SIGN_BY_EMAIL);
		
		List<LovBean> msLov = daoFactory.getLovDao().getCodeBalanceVendorOfTenantByIdMsTenant(msTenant.getIdMsTenant());
		
		List<String> otpSendingOptionsList = new ArrayList<>();

		for (int i = 0; i < msLov.size(); i++) {
		    LovBean lovItem = msLov.get(i);
		    if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(lovItem.getCode()) || GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(lovItem.getCode())) {
		        otpSendingOptionsList.add(lovItem.getCode());
		    }
		}
		String defaultSendingPoint = null;
		if (msTenant.getLovDefaultOtpSendingOption() != null) {
			if (msTenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS) || msTenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA) || msTenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL)) {
				defaultSendingPoint = msTenant.getLovDefaultOtpSendingOption().getCode();
			} else {
				defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
			}
	
		} else {
			defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
		}
		
		ActivationStatusResetPasswordResponse response = new ActivationStatusResetPasswordResponse();
		
		if (null != vendorUser.getEmailService()) {
			if (null != tenantSettings && tenantSettings.getSettingValue().equals("1") && vendorUser.getEmailService().equals("0")) {
				response.setLatestRecipientEmail(vendorUser.getSignerRegisteredEmail());
				otpSendingOptionsList.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
			}
		} 
		
		if (null != vendorUser.getPhoneBytea()) {
			String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			response.setLatestRecepientPhone(phoneNumber);
		} else {
			response.setLatestRecipientEmail(vendorUser.getSignerRegisteredEmail());
			otpSendingOptionsList.clear();
			otpSendingOptionsList.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
		}

		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		response.setListAvailableOptionSendingPoint(otpSendingOptionsList);
		response.setDefaultAvailableOptionSendingPoint(defaultSendingPoint);
		response.setTenantCode(msTenant.getTenantCode());
		
		return response;
	}

	private boolean userCanReregister(String nik, String phone, String email, String vendorCode) {
		TrReregistrationUser reregistrationUser = daoFactory.getReregistrationUserDao().getRegistrationUser(nik, phone,
				email, vendorCode);
		return null != reregistrationUser;
	}

	private boolean emailPhoneRegisteredInVendor(String email, String phone, String vendorCode) {
		if (StringUtils.isNotBlank(phone)) {
			List<MsVendorRegisteredUser> phoneVendorUser = daoFactory.getVendorRegisteredUserDao().getListVendorUserByPhone(phone, vendorCode);
			if (CollectionUtils.isNotEmpty(phoneVendorUser)) {
				return true;
			}
		}
		if (StringUtils.isNotBlank(email)) {
			List<MsVendorRegisteredUser> emailVendorUser = daoFactory.getVendorRegisteredUserDao().getListVendorUserByEmail(email, vendorCode);
			if (CollectionUtils.isNotEmpty(emailVendorUser)) {
				return true;
			}
		}
		return false;
	}

	private RegisterResponse processTekenAjaRegisterResponse(MsTenant tenant, MsVendor vendor,
			TekenAjaRegisterRequest request, TekenAjaRegisterApiResponse registerResponse,
			RegistrationType registrationType, boolean isReregister, AuditContext audit) {

		cutTekenAjaVerifBalance(tenant, vendor, request.getUserData(), audit);

		if (TekenAjaConstant.RESPONSE_STATUS_OK.equals(registerResponse.getStatus())) {
			insertTekenAjaUser(request.getUserData(), tenant, vendor, true, false, isReregister, registrationType,
					audit);
			return new RegisterResponse();
		}

		RegisterResponse response = new RegisterResponse();

		if (TekenAjaConstant.REGISTER_RESPONSE_CODE_USER_EXISTS.equals(registerResponse.getCode())
				&& TekenAjaConstant.REGISTER_RESPONSE_MSG_USER_EXISTS.equals(registerResponse.getMessage())) {
			if (!isReregister) {
				Status status = new Status();
				status.setCode(StatusCode.USER_CAN_REREGISTER);
				status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_CAN_REREGISTER,
						new String[] { vendor.getVendorName() }, audit));
				response.setReregistrationAvailable("1");
				response.setStatus(status);
				return response;
			}

			TknajRegisterCekResponse resendResponse = tekenAjaLogic.registerCek(request.getUserData().getIdKtp(),
					TekenAjaConstant.REGCEK_ACTION_RESEND_EMAIL, vendor, tenant, audit);
			if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_VERIFIED.equals(resendResponse.getCode())) {
				insertTekenAjaUser(request.getUserData(), tenant, vendor, true, true, isReregister, registrationType,
						audit);

				UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(request.getUserData().getIdKtp(),
						vendor.getIdMsVendor());
				QueuePublisher.queueUpdErrHistRerunProcess(bean);

				Status status = new Status();
				status.setCode(StatusCode.TEKEN_AJA_ERROR);
				status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_WAIT_FOR_SIGN, null, audit));
				response.setStatus(status);
				return response;
			}

			insertTekenAjaUser(request.getUserData(), tenant, vendor, true, false, isReregister, registrationType,
					audit);
			return new RegisterResponse();
		}

		String message = tekenAjaLogic.buildRegisterErrorMessage(registerResponse);
		Status status = new Status();
		status.setCode(StatusCode.TEKEN_AJA_ERROR);
		status.setMessage(message);
		response.setStatus(status);
		return response;
	}

	private RegisterResponse registerTekenAja(TekenAjaRegisterRequest request, RegistrationType registrationType,
			boolean isReregister, AuditContext audit) {
		userValidatorLogic.validateTekenAjaRegisterRequest(request, registrationType, audit);
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);

		if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			throw new VendorException(messageSource.getMessage(MSG_VENDORCODE_INVALID,
					new String[] { request.getVendorCode() }, retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}

		if (RegistrationType.INVITATION_SMS == registrationType) {
			CreateSingleEmailRequest emailRequest = new CreateSingleEmailRequest(tenant,
					request.getUserData().getNama(), request.getUserData().getTglLahir(),
					request.getUserData().getIdKtp());
			CreateSingleEmailResponse emailResponse = emailLogic.createEmail(emailRequest, audit);

			request.getUserData().setEmail(StringUtils.upperCase(emailResponse.getEmail()));
			request.getUserData().setIdEmailHosting(emailResponse.getIdEmailHosting());
			request.getUserData().setEmailService("1");
		}

		RegisterResponse response = new RegisterResponse();

		if (isReregister) {
			TekenAjaRegisterApiResponse registerResponse = tekenAjaLogic.registerUser(request, audit);
			return processTekenAjaRegisterResponse(tenant, vendor, request, registerResponse, registrationType, true,
					audit);
		}

		TknajRegisterCekResponse nikCheckResponse = tekenAjaLogic.registerCek(request.getUserData().getIdKtp(),
				TekenAjaConstant.REGCEK_ACTION_CHECK_NIK, vendor, tenant, audit);
		if ((TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_VERIFIED.equals(nikCheckResponse.getCode())
				&& TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_VERIFIED.equals(nikCheckResponse.getMessage()))
				|| (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_UNVERIFIED.equals(nikCheckResponse.getCode())
						&& TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_UNVERIFIED
								.equals(nikCheckResponse.getMessage()))
				|| (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_CERT_EXPIRED.equals(nikCheckResponse.getCode())
						&& TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_CERT_EXPIRED
								.equals(nikCheckResponse.getMessage()))) {

			Status status = new Status();
			status.setCode(StatusCode.USER_CAN_REREGISTER);
			status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_CAN_REREGISTER,
					new String[] { vendor.getVendorName() }, audit));
			response.setReregistrationAvailable("1");
			response.setStatus(status);
			return response;
		}

		TekenAjaRegisterApiResponse registerResponse = tekenAjaLogic.registerUser(request, audit);
		return processTekenAjaRegisterResponse(tenant, vendor, request, registerResponse, registrationType, false,
				audit);
	}

	@Override
	public RegisterResponse registerDigisign(DigisignRegisterRequest request, RegistrationType registrationType, MsLov lovUserType, AuditContext audit) {

		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);

		if (!GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			throw new VendorException(getMessage(MSG_VENDORCODE_INVALID, 
					new String[] { request.getVendorCode() }, audit), ReasonVendor.VENDOR_CODE_INVALID);
		}

		if (RegistrationType.INVITATION_SMS == registrationType) {
			CreateSingleEmailRequest emailRequest = new CreateSingleEmailRequest(tenant,
					request.getUserData().getUserName(), request.getUserData().getUserDob(),
					request.getUserData().getIdNo());
			CreateSingleEmailResponse emailResponse = emailLogic.createEmail(emailRequest, audit);

			request.getUserData().setEmail(emailResponse.getEmail());
			request.getUserData().setIdEmailHosting(emailResponse.getIdEmailHosting());
			request.getUserData().setEmailService("1");
		}

		prepareDataRegisterV2(request, audit);
		RegisterDigisignResponseBean resultBean = digisignLogic.sendDataRegisterV2(request, audit);

		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultBean.getResult())
				&& "Email sudah terdaftar, namun belum melakukan aktivasi. Silahkan untuk melakukan aktivasi sebelum data dihapus dari daftar aktivasi."
						.equals(resultBean.getNotif())) {
			// ESG-933: Handling response sudah terdaftar, tapi belum aktivasi
			insertDigisignUser(request, lovUserType, registrationType, true, false, false, audit);

			Status status = new Status();
			status.setCode(0);
			status.setMessage(getMessage(MSG_DIGI_REGISTERED_NOTACTIVATED, null, audit));

			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}

		handleDigisignRegisterResponseV2(resultBean, request.getUserData().getUserPhone(),
				request.getUserData().getEmail(), registrationType.toString(), request.getVendorCode(), audit);

		// Format dob untuk Digisign diubah ke dd-MM-yyyy, perlu ubah kembali ke
		// yyyy-MM-dd untuk eSign
		Date dob = MssTool.formatStringToDate(request.getUserData().getUserDob(), GlobalVal.DATE_FORMAT_DASH_IN);
		request.getUserData().setUserDob(MssTool.formatDateToStringIn(dob, GlobalVal.DATE_FORMAT));

		if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultBean.getResult())
				&& !GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(resultBean.getNotif())
				&& !GenericDigisignLogic.DIGI_REGISTERED_MSG.equalsIgnoreCase(resultBean.getNotif())) {
			cutFailedVerifBalanceV2(resultBean, request, audit);
			return digisignLogic.buildRegisterResponse(resultBean, request, audit);
		}

		boolean isRegistered = true;
		boolean isActivated = false;
		boolean isEmailChanged = false;

		if (GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(resultBean.getNotif())) {

			LOG.info("Digisign: Change email from {} to {} (registered on Digisign)", request.getUserData().getEmail(),
					resultBean.getEmailRegistered());
			isActivated = true;
			isEmailChanged = true;
			request.getUserData().setEmail(resultBean.getEmailRegistered());

			// Panggil ulang API register Digisign dengan email yang sudah terdaftar
			Date userDob = MssTool.formatStringToDate(request.getUserData().getUserDob(), GlobalVal.DATE_FORMAT);
			String userDobDigisign = MssTool.formatDateToStringIn(userDob, GlobalVal.DATE_FORMAT_DASH_IN);
			request.getUserData().setUserDob(userDobDigisign);
			digisignLogic.sendDataRegisterV2(request, audit);

		} else if (GenericDigisignLogic.DIGI_REGISTERED_MSG.equals(resultBean.getNotif())) {

			isActivated = true;
			UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(request.getUserData().getIdNo(),
					vendor.getIdMsVendor());
			QueuePublisher.queueUpdErrHistRerunProcess(bean);

		}

		insertDigisignUser(request, lovUserType, registrationType, isRegistered, isActivated, isEmailChanged, audit);
		return digisignLogic.buildRegisterResponse(resultBean, request, audit);
	}

	private void insertDigisignUser(DigisignRegisterRequest request, MsLov lovUserType, RegistrationType registrationType,
			boolean isRegistered, boolean isActivated, boolean isEmailChanged, AuditContext audit) {

		// Set role
		String roleCode;
		if (null != lovUserType && GlobalVal.CODE_LOV_USER_TYPE_EMPLOYEE.equals(lovUserType.getCode())) {
			roleCode = GlobalVal.ROLE_BM_MF;
		} else {
			roleCode = GlobalVal.ROLE_CUSTOMER;
		}

		UserBean userData = request.getUserData();
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		userValidatorLogic.removeUnnecessaryRegisterParam(userData, tenant);
		
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(GlobalVal.VENDOR_CODE_DIGISIGN, true, audit);
		MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(roleCode, tenant.getTenantCode());

		// Set password
		String hashedPassword = StringUtils.EMPTY;
		String password = StringUtils.EMPTY;
		if (RegistrationType.INVITATION_EMAIL == registrationType
				|| RegistrationType.INVITATION_SMS == registrationType) {
			password = REGISTER_BY_INVITATION_PASSWORD;
			hashedPassword = password;
		} else if (RegistrationType.EMBED == registrationType) {
			password = generateRandomPassword();
			hashedPassword = PasswordHash.createHash(password);
		}

		String hashedPhone = MssTool.getHashedString(userData.getUserPhone());
		String hashedIdNo = MssTool.getHashedString(userData.getIdNo());

		String emailService;
		MsEmailHosting emailHosting;
		if ("1".equals(userData.getEmailService()) || isEmailChanged) {
			emailHosting = daoFactory.getEmailDao().getEmailHostingById(userData.getIdEmailHosting());
			emailService = "1";
		} else {
			emailHosting = null;
			emailService = "0";
		}

		boolean isNewUser = false;

		// Insert am_msuser
		AmMsuser user = userValidatorLogic.validateGetUserByNik(userData.getIdNo(), false, audit);
		if (null == user) {
			isNewUser = true;

			// Memastikan No HP belum digunakan sebelum insert
			AmMsuser phoneUser = userValidatorLogic.validateGetUserByPhone(userData.getUserPhone(), false, audit);
			if (null != phoneUser) {
				throw new UserException(
						messageSource.getMessage(MSG_USER_PHONE_USEDBYOTHER,
								new String[] { userData.getUserPhone() }, retrieveLocaleAudit(audit)),
						ReasonUser.PHONE_NO_ALREADY_EXIST);
			}

			// Memastikan email belum digunakan sebelum insert
			AmMsuser emailUser = daoFactory.getUserDao().getUserByLoginId(userData.getEmail());
			if (null != emailUser) {
				throw new UserException(
						messageSource.getMessage("businesslogic.user.emailuserbyother",
								new String[] { userData.getEmail() }, retrieveLocaleAudit(audit)),
						ReasonUser.LOGIN_ID_NOT_UNIQUE);
			}

			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setFullName(StringUtils.upperCase(userData.getUserName()));
			String[] separatedName = userData.getUserName().split(" ");
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword(hashedPassword);
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("1");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedIdNo(hashedIdNo);
			user.setHashedPhone(hashedPhone);
			daoFactory.getUserDao().insertUser(user);
		} else {
			user.setIsDormant("0");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUser(user);
		}

		// Insert am_userpwdhistory
		if (!REGISTER_BY_INVITATION_PASSWORD.equals(hashedPassword) && isNewUser) {
			MsLov pwdChangeType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE,
					GlobalVal.CODE_LOV_PWD_CHANGE_TYPE_NEW);
			AmUserpwdhistory passwordHistory = new AmUserpwdhistory();
			passwordHistory.setUsrCrt(audit.getCallerId());
			passwordHistory.setDtmCrt(new Date());
			passwordHistory.setAmMsuser(user);
			passwordHistory.setPassword(hashedPassword);
			passwordHistory.setMsLov(pwdChangeType);
			daoFactory.getUserDao().insertUserPwdhistory(passwordHistory);
		}

		// Insert am_user_personal_data
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);

		ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
		zipcodeCityBean.setProvinsi(StringUtils.upperCase(userData.getProvinsi()));
		zipcodeCityBean.setKota(StringUtils.upperCase(userData.getKota()));
		zipcodeCityBean.setKecamatan(StringUtils.upperCase(userData.getKecamatan()));
		zipcodeCityBean.setKelurahan(StringUtils.upperCase(userData.getKelurahan()));
		zipcodeCityBean.setZipcode(userData.getZipcode());

		Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
		String gender = "";
		if (userData.getUserGender().equals(GlobalVal.CODE_DIGISIGN_MALE)) {
			gender = GlobalVal.CODE_LOV_MALE;
		} else if (userData.getUserGender().equals(GlobalVal.CODE_DIGISIGN_FEMALE)) {
			gender = GlobalVal.CODE_LOV_FEMALE;
		}

		if (null == personalData || null == personalData.getUserPersonalData()) {
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(gender);
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);

			if (StringUtils.isNotBlank(userData.getSelfPhoto())) {
				byte[] photo = MssTool.imageStringToByteArray(userData.getSelfPhoto());
				personalDataBean.setSelfPhotoRaw(photo);
			}
			if (StringUtils.isNotBlank(userData.getIdPhoto())) {
				byte[] photo = MssTool.imageStringToByteArray(userData.getIdPhoto());
				personalDataBean.setPhotoIdRaw(photo);
			}

			personalDataBean.setIdNoRaw(userData.getIdNo());
			personalDataBean.setPhoneRaw(userData.getUserPhone());
			personalDataBean.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
		} else {
			personalData.getUserPersonalData().setZipcodeBean(zipcodeCityBean);
			personalData.getUserPersonalData().setGender(StringUtils.upperCase(gender));
			personalData.getUserPersonalData().setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			personalData.getUserPersonalData()
					.setDateOfBirth(MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT));
			personalData.getUserPersonalData().setEmail(StringUtils.upperCase(userData.getEmail()));
			personalData.setIdNoRaw(StringUtils.upperCase(userData.getIdNo()));
			personalData.setPhoneRaw(userData.getUserPhone());
			personalData.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));

			if (StringUtils.isNotBlank(userData.getIdPhoto())) {
				byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto());
				personalData.setPhotoIdRaw(idPhoto);
			}

			if (StringUtils.isNotBlank(userData.getSelfPhoto())) {
				byte[] selfPhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto());
				personalData.setSelfPhotoRaw(selfPhoto);
			}

			personalData.getUserPersonalData().setDtmUpd(new Date());
			personalData.getUserPersonalData().setUsrUpd(audit.getCallerId());
			daoFactory.getUserDao().updateUserPersonalData(personalData);
		}

		// Insert ms_useroftenant
		MsUseroftenant tenantUser = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(),
				tenant.getTenantCode());
		if (null == tenantUser) {
			tenantUser = new MsUseroftenant();
			tenantUser.setAmMsuser(user);
			tenantUser.setMsTenant(tenant);
			tenantUser.setDtmCrt(new Date());
			tenantUser.setUsrCrt(audit.getCallerId());
			daoFactory.getUserDao().insertUseroftenant(tenantUser);
		}

		// Insert am_memberofrole
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofrole(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsuser(user);
			userRole.setAmMsrole(role);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRole(userRole);
		}

		String isActive = isActivated ? "1" : "0";
		String isRegist = isRegistered ? "1" : "0";

		// Insert ms_vendor_registered_user
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		if (null == vendorUser) {
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive(isActive);
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);

			if (isActivated) {
				Date activatedDate = new Date();
				Date expiredDate = DateUtils.addYears(activatedDate, 1);
				vendorUser.setActivatedDate(activatedDate);
				vendorUser.setCertExpiredDate(expiredDate);
			}

			vendorUser.setIsRegistered(isRegist);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);

			byte[] phoneBytea = personalDataEncLogic.encryptFromString(userData.getUserPhone());
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUser(vendorUser);
		} else {
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive(isActive);
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);

			if (isActivated) {
				Date activatedDate = new Date();
				Date expiredDate = DateUtils.addYears(activatedDate, 1);
				vendorUser.setActivatedDate(activatedDate);
				vendorUser.setCertExpiredDate(expiredDate);
			}

			vendorUser.setIsRegistered(isRegist);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);

			byte[] phoneBytea = personalDataEncLogic.encryptFromString(userData.getUserPhone());
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vendorUser);
		}

		if (RegistrationType.EMBED == registrationType && isNewUser) {
			sendAccountInfoEmail(userData.getUserName(), userData.getEmail(), password);
		}
	}

	@Override
	public RegisterResponse registerEmbedDigisign(DigisignRegisterEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = embedValidatorLogic.validateEmbedMessage(request.getMsg(), audit);
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(msgBean.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(msgBean.getOfficeCode(),
				msgBean.getTenantCode());

		if (!GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			throw new VendorException(messageSource.getMessage(MSG_VENDORCODE_INVALID,
					new String[] { request.getVendorCode() }, retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}

		if (null == office) {
			office = new MsOffice();
			office.setOfficeCode(StringUtils.upperCase(msgBean.getOfficeCode()));
			office.setIsActive("1");
			office.setOfficeName(msgBean.getOfficeName());
			office.setMsTenant(tenant);
			office.setUsrCrt(audit.getCallerId());
			office.setDtmCrt(new Date());
			daoFactory.getOfficeDao().insertOffice(office);
		}

		UserBean userData = request.getUserData();

		DigisignRegisterRequest regisRequest = new DigisignRegisterRequest();
		regisRequest.setUserData(userData);
		regisRequest.setTenantCode(tenant.getTenantCode());
		regisRequest.setVendorCode(vendor.getVendorCode());
		
		MsLov lovUserType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_USER_TYPE, GlobalVal.CODE_LOV_USER_TYPE_EMPLOYEE);

		if (!emailPhoneRegisteredInVendor(userData.getEmail(), userData.getUserPhone(), vendor.getVendorCode())) {
			return registerDigisign(regisRequest, RegistrationType.EMBED, lovUserType, audit);
		}

		if (!"1".equals(vendor.getReregistAvailable())) {
			throw new UserException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_ALREADY_REGISTERED,
							new String[] { vendor.getVendorName() }, retrieveLocaleAudit(audit)),
					ReasonUser.ALREADY_REGISTERED);
		}

		// Registrasi Digisign harusnya ga akan sampai sini
		if (!userCanReregister(userData.getIdNo(), userData.getUserPhone(), userData.getEmail(),
				vendor.getVendorCode())) {
			RegisterResponse response = new RegisterResponse();
			response.setReregistrationAvailable("1");
			return response;
		}

		// Registrasi Digisign harusnya ga akan sampai sini
		return registerDigisign(regisRequest, RegistrationType.EMBED, lovUserType, audit);
	}

	@Override
	public UpdateReregisResponse updateReregisterEmbed(UpdateReregisRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = embedValidatorLogic.validateEmbedMessage(request.getMsg(), audit);
		UpdateReregisRequest req = new UpdateReregisRequest();
		req.setEmail(request.getEmail());
		req.setIdNo(request.getIdNo());
		req.setPhone(request.getPhone());
		req.setVendorCode(request.getVendorCode());
		req.setTenantCode(msgBean.getTenantCode());
		return this.updateReregister(req, audit);
	}

	@Override
	public SaveUserActivationResponse updateActivationStatusV2(SaveActivationDecryptedResultBean decryptedBean,String tenantCode,
			AuditContext audit) throws IOException {
		SaveUserActivationResponse response = new SaveUserActivationResponse();
		Status status = new Status();
		if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(decryptedBean.getResult())) {
			status.setMessage(decryptedBean.getNotif());
			response.setStatus(status);
			return response;
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(decryptedBean.getEmailUser(), true, audit);
		PersonalDataBean pdBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		response.setFullName(user.getFullName());
		response.setIdNo(pdBean.getIdNoRaw());
		
		MsVendorRegisteredUser vRUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), GlobalVal.VENDOR_CODE_DIGISIGN);
		vRUser.setIsActive("1");
		vRUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

		Calendar cal = Calendar.getInstance();
		Date date = cal.getTime();
		vRUser.setActivatedDate(date);
		vRUser.setDtmUpd(date);

		cal.add(Calendar.YEAR, 1);
		Date nextYear = cal.getTime();
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vRUser);

		response.setPhoneNum(personalDataEncLogic.decryptToString(vRUser.getPhoneBytea()));
		response.setEmail(vRUser.getSignerRegisteredEmail());
		
		TrDocumentH docH = daoFactory.getDocumentDao().getEarliestAgreement(user);
		SignDocumentDigisignResponseBean documentDigisign = new SignDocumentDigisignResponseBean();
		if (null != docH) {

			List<ActivationDocumentBean> lDocBean = daoFactory.getDocumentDao().getActivationDocumentByDocHAndUser(docH,
					user);

			if (lDocBean.size() == 1) {
				SignDocumentRequest signDocReq = new SignDocumentRequest();
				signDocReq.setDocumentId(lDocBean.get(0).getDocumentId());
				signDocReq.setEmail(user.getLoginId());
				documentDigisign = digisignLogic.signDocument(signDocReq, null, audit);
			}

			if (lDocBean.size() > 1) {
				response.setDocs(lDocBean);
			} else if (lDocBean.size() == 1) {
				if (documentDigisign.getLink() != null) {
					response.setSignLink(documentDigisign.getLink());
				} else {
					if (documentDigisign.getNotif() == null) {
						documentDigisign.setNotif("Tidak ada Agreement");
					}
				}
			} else if (lDocBean.isEmpty()) {
				response.setSignLink("");
				ActivationDocumentBean bean = new ActivationDocumentBean();
				bean.setDocumentId("");
				lDocBean.add(bean);
				response.setDocs(lDocBean);
			}

		} else {
			response.setSignLink("");
			ActivationDocumentBean bean = new ActivationDocumentBean();
			bean.setDocumentId("");
			List<ActivationDocumentBean> lDocBean = new ArrayList<>();
			lDocBean.add(bean);
			response.setDocs(lDocBean);
		}

		TrInvitationLink invLink = daoFactory.getInvitationLinkDao()
				.getInvitationLinkByRecieverDetailV2(user.getLoginId(), GlobalVal.VENDOR_CODE_DIGISIGN);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(tenantCode);
		if (invLink == null) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
			invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetailV2(personalData.getPhoneRaw(),
					GlobalVal.VENDOR_CODE_DIGISIGN);
		}

		if (invLink != null) {
			invLink.setIsActive("0");
			invLink.setGender(null);
			invLink.setKelurahan(null);
			invLink.setKecamatan(null);
			invLink.setKota(null);
			invLink.setZipCode(null);
			invLink.setPlaceOfBirth(null);
			invLink.setDateOfBirth(null);
			invLink.setProvinsi(null);
			invLink.setEmail(null);
			invLink.setPhotoSelf(null);
			invLink.setPhotoId(null);
			invLink.setIdNo(null);
			invLink.setUsrUpd(audit.getCallerId());
			invLink.setPhone(null);
			invLink.setAmMsuser(null);
			invLink.setAddress(null);
			invLink.setDtmUpd(new Date());
			daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		}

		// Add to queue for updating tr_error_history.rerun_process later
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(decryptedBean.getNik(), vendor.getIdMsVendor());
		QueuePublisher.queueUpdErrHistRerunProcess(bean);

		status.setMessage(documentDigisign.getNotif());
		MsLov lovCallbackType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_CALLBACK_TYPE, GlobalVal.CODE_LOV_CALLBACK_TYPE_ACTIVATION_COMPLETE);
		callbackLogic.executeCallbackToClient(tenant, lovCallbackType, vRUser, null, CALLBACK_ACTIVATION_COMPLETE_MESSAGE, audit);
		if (tenant.getClientActivationRedirectUrl() != null) {
			response.setActivationRedirectClientURL(tenant.getClientActivationRedirectUrl());
		}
		response.setStatus(status);

		return response;
	}

	@Override
	public CheckRegisterStatusResponse checkRegisterStatus(CheckRegisterStatusRequest request, AuditContext audit) {
		CheckRegisterStatusResponse response = new CheckRegisterStatusResponse();
		Status status = new Status();
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] { CONST_TENANT }, retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		if (StringUtils.isBlank(request.getVendorCode())) {
			throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_EMPTY);
		}

		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TYPE_CODE_INVALID, null,
					retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}

		MsVendorRegisteredUser userEmail = null;
		MsVendorRegisteredUser userPhone = null;

		if ("1".equals(tenant.getEmailService())) {
			userPhone = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(request.getPhoneNum(),
					request.getVendorCode());

			if (null != userPhone) {
				if (userPhone.getIsRegistered().equals("0")) {
					userPhone = null;
				}
			}

			if (StringUtils.isBlank(request.getPhoneNum())) {
				status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
				status.setMessage("No telepon tidak boleh kosong!");
				response.setStatus(status);
				return response;
			}

			if (null == userPhone) {
				status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
				status.setMessage(CONST_USER_NOT_FOUND);
				response.setStatus(status);
				return response;
			}
			
			if ("0".equals(userPhone.getEmailService()) && StringUtils.isBlank(request.getEmail())) {
				status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
				String phoneNumber = personalDataEncLogic.decryptToString(userPhone.getPhoneBytea());
				status.setMessage("Untuk no telepon (" + phoneNumber + ") wajib menuliskan email!");
				response.setStatus(status);
				return response;
			}

			if (!StringUtils.isBlank(request.getEmail())) {
				userEmail = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(
						request.getEmail(), request.getVendorCode());

				if (null != userEmail && "0".equals(userEmail.getIsRegistered())) {
					userEmail = null;
				}

				if (null == userEmail) {
					status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
					status.setMessage("Email dan No Telp sudah terdaftar pada kedua akun yang berbeda!");
					response.setStatus(status);
					return response;
				}

				if (userEmail.getIdMsVendorRegisteredUser() != userPhone.getIdMsVendorRegisteredUser()) {
					status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
					status.setMessage("Email dan No Telp sudah terdaftar pada kedua akun yang berbeda!");
					response.setStatus(status);
					return response;
				}
			}

			status.setCode(0);
			status.setMessage("Data user sudah sesuai");
			response.setStatus(status);
		} else if ("0".equals(tenant.getEmailService())) {

			if (StringUtils.isBlank(request.getEmail())) {
				status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
				status.setMessage("Email tidak boleh kosong!");
				response.setStatus(status);
				return response;
			}

			userEmail = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(
					request.getEmail(), request.getVendorCode());

			if (null != userEmail && "0".equals(userEmail.getIsRegistered())) {
				userEmail = null;
			}

			if (null == userEmail) {
				status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
				status.setMessage(CONST_USER_NOT_FOUND);
				response.setStatus(status);
				return response;
			}

			status.setCode(0);
			status.setMessage("Data user sudah sesuai");
			response.setStatus(status);
		}
		return response;
	}

	@Override
	public DataUserOptionalBean getDataUserOpsionalDataEncrypt(long idMsUser, long idMsVendorRegisteredUser,
			boolean getNIK, boolean getPhone, boolean getAddress, boolean getPhotoKTP, boolean getPhotoSelfie,
			AuditContext audit) {
		DataUserOptionalBean dataUser = new DataUserOptionalBean();

		MsVendorRegisteredUser mvu = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsVendorRegisteredUser(idMsVendorRegisteredUser);
		if (null == mvu) {
			throw new UserException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND, null,
							this.retrieveLocaleAudit(audit)),
					ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}
		dataUser.setName(mvu.getAmMsuser().getFullName());
		dataUser.setEmail(mvu.getSignerRegisteredEmail());

		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserOptional(idMsUser, getNIK,
				getPhone, getAddress, getPhotoKTP, getPhotoSelfie);
		if (null == personalData) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_PERSONAL_DATA_NOT_FOUND,
					null, this.retrieveLocaleAudit(audit)), ReasonUser.PERSONAL_DATA_NOT_FOUND);
		}

		dataUser.setIdNoRaw(personalData.getIdNoRaw());
		dataUser.setPhoneRaw(personalData.getPhoneRaw());
		dataUser.setAddressRaw(personalData.getAddressRaw());
		dataUser.setPhotoIdRaw(personalData.getPhotoIdRaw());
		dataUser.setSelfPhotoRaw(personalData.getSelfPhotoRaw());

		return dataUser;
	}

	@Override
	public CheckRegisterAutoFillResponse checkRegisterAutoFill(CheckRegisterAutoFillRequest request,
			AuditContext audit) {
		CheckRegisterAutoFillResponse response = new CheckRegisterAutoFillResponse();
		Status status = new Status();

		if (StringUtils.isBlank(request.getVendorCode())) {
			throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_EMPTY);
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getCheckRegisterDetail(),
						request.getVendorCode());
		if (null == vendorUser) {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(
					request.getCheckRegisterDetail(), request.getVendorCode());
		}

		if (null == vendorUser) {
			status.setCode(StatusCode.CHECK_REGISTER_NOT_MATCHED);
			status.setMessage(CONST_USER_NOT_FOUND);
			response.setStatus(status);
			return response;
		}

		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
		response.setEmailUser(vendorUser.getSignerRegisteredEmail());
		response.setFullName(vendorUser.getAmMsuser().getFullName());
		response.setPhoneNumUser(phoneNumber);

		return response;
	}

	@Override
	public GetAllUserResponse getAllUserData() {
		GetAllUserResponse response = new GetAllUserResponse();
		List<UserDecryptedBean> userDataBeanList = new ArrayList<>();

		List<Map<String, Object>> userList = daoFactory.getUserDao().getAllUser();
		Iterator<Map<String, Object>> itr = userList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			UserDecryptedBean bean = new UserDecryptedBean();
			BigInteger bigIdMsUser = (BigInteger) map.get("d0");
			PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(bigIdMsUser.longValue(), false);

			bean.setEmail((String) map.get("d1"));
			bean.setName((String) map.get("d2"));
			bean.setPhoneNumber(personalDataBean.getPhoneRaw());
			bean.setIdNumber(personalDataBean.getIdNoRaw());

			userDataBeanList.add(bean);
		}
		response.setListUser(userDataBeanList);

		return response;
	}

	@Override
	public UpdateDataUserResponse updateDataUser(UpdateDataUserRequest request, AuditContext audit) {

		String email = request.getLoginId();
		boolean checkIsUserUpdated = false;
		boolean checkIsUserPersonalUpdated = false;
		boolean checkIsMsVendorRegisteredUserUpdated = false;
		String messageValidation = "";
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit);
		commonValidatorLogic.validateNotNull(user, messageValidation, StatusCode.LOGIN_ID_NOT_EXISTS);
		
		StringBuilder changes = new StringBuilder();
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao()
				.getEveryVendorRegisteredByLoginIdAndVendorCode(email, request.getVendorCode());
		
		commonValidatorLogic.validateNotNull(msVendorRegisteredUser, messageValidation, StatusCode.LOGIN_ID_NOT_EXISTS);
		
		MsVendorRegisteredUser msVendorRegisteredUserOld = new MsVendorRegisteredUser(msVendorRegisteredUser);
		AmMsuser userOld = new AmMsuser(user);
		
		UpdateDataUserResponse response = new UpdateDataUserResponse();

		if (StringUtils.isNotBlank(request.getNewEmail())) {

			Pattern pattern = Pattern.compile(regexEmail);
			Matcher matcher = pattern.matcher(request.getNewEmail());

			if (!matcher.matches()) {
				throw new UserManagementException(
						getMessage("businesslogic.usermanagement.emailisnotvalid", null, audit),
						ReasonUserManagement.EMAIL_IS_NOT_VALID);
			}
			AmMsuser newEmailUser = userValidatorLogic.validateGetUserByEmailv2(request.getNewEmail(), false, audit);
			
			messageValidation = getMessage("businesslogic.usermanagement.loginidexist",
					new Object[] { request.getNewEmail() }, audit);
			commonValidatorLogic.validateMustNull(newEmailUser, messageValidation, StatusCode.USER_ALREADY_REGISTERED);

			user.setLoginId(StringUtils.upperCase(request.getNewEmail()));
			user.setEmailService("0");

			msVendorRegisteredUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getNewEmail()));
			msVendorRegisteredUser.setEmailService("0");
			checkIsUserUpdated = true;
			email = request.getNewEmail();
			changes.append(CONST_EMAIL);
		}

		AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(user);
		AmUserPersonalData userPersonalDataOld = new AmUserPersonalData(userPersonalData);
		
		PersonalDataBean userPersonalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		ZipcodeCityBean zipCodeCityBean = userPersonalData.getZipcodeBean();

		if (StringUtils.isNotBlank(request.getNoPhone())) {
			String phoneRegex = commonLogic.getGeneralSettingValueByCode("AM_PHONE_FORMAT", audit);
			Pattern pattern = Pattern.compile(phoneRegex);
			Matcher matcher = pattern.matcher(request.getNoPhone());
			
			if (!matcher.matches()) {
				throw new UserManagementException(
						getMessage("businesslogic.usermanagement.nophoneisnotvalid", null, audit),
						ReasonUserManagement.NO_PHONE_NOT_VALID);
			}
			
			// validasi tambahan no hp sudah terpakai apa belum
			BigInteger idMsUser = daoFactory.getUserDao().getIdMsUserByPhoneNo(request.getNoPhone());
			if(idMsUser != null && msVendorRegisteredUser.getAmMsuser().getIdMsUser() != (idMsUser.longValue())) {
				throw new UserException(this.messageSource.getMessage(MSG_USER_PHONE_USEDBYOTHER,
						new String[] { request.getNoPhone() }, this.retrieveLocaleAudit(audit)), ReasonUser.PHONE_NO_ALREADY_EXIST);
			}
			
			byte[] phoneNo = personalDataEncLogic.encryptFromString(request.getNoPhone());
			userPersonalDataBean.setPhoneRaw(request.getNoPhone());
			msVendorRegisteredUser.setPhoneBytea(phoneNo);
			msVendorRegisteredUser.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getNoPhone()));
			userPersonalData.setPhoneBytea(phoneNo);
			user.setHashedPhone(MssTool.getHashedString(request.getNoPhone()));
			checkIsUserPersonalUpdated = true;
			checkIsMsVendorRegisteredUserUpdated = true;
			changes.append(" No. Telp");
		}
		
		userPersonalData.setEmail(StringUtils.upperCase(email));
		userPersonalData.setZipcodeBean(zipCodeCityBean);
		userPersonalData.setAmMsuser(user);
		userPersonalDataBean.setUserPersonalData(userPersonalData);
		daoFactory.getUserDao().updateUserPersonalData(userPersonalDataBean);

		if (StringUtils.isNotEmpty(request.getIsActive())) {
			msVendorRegisteredUser.setIsActive(request.getIsActive());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(msVendorRegisteredUser);
			checkIsMsVendorRegisteredUserUpdated = true;
			changes.append(" Status Aktivasi");
		}

		if (StringUtils.isNotEmpty(request.getIsRegist())) {
			msVendorRegisteredUser.setIsRegistered(request.getIsRegist());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(msVendorRegisteredUser);
			checkIsMsVendorRegisteredUserUpdated = true;
			changes.append(" Status Registrasi");
		}

		if (checkIsUserUpdated) {
			user.setDtmUpd(new Date());
			user.setUsrUpd(audit.getCallerId());
			daoFactory.getUserDao().updateUser(user);
		}
		
		if (checkIsMsVendorRegisteredUserUpdated) {
			msVendorRegisteredUser.setDtmUpd(new Date());
			msVendorRegisteredUser.setUsrUpd(audit.getCallerId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(msVendorRegisteredUser);
		}
		
		if (checkIsUserPersonalUpdated) {
			userPersonalData.setDtmUpd(new Date());
			userPersonalData.setUsrUpd(audit.getCallerId());
			daoFactory.getUserDao().updateUserPersonalData(userPersonalDataBean);
		}
		
		String requestDetail = GlobalVal.DATAACCESS_LOG_REQDETAIL_EDIT_SIGNER_DATA + ": " + (changes.toString().isEmpty() ? "No Changes" : changes.toString());
		AmMsuser callerUser = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), false, audit);
		MsLov lovActionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_EDIT_SIGNER_DATA);
		TrUserDataAccessLog log =  insertUserDataAccessLogNewTrx(callerUser, user, lovActionType, request.getIpAddress(), requestDetail, audit);
		this.addVendorRegisteredUserHistory(msVendorRegisteredUserOld, log);
		this.addUserHistory(userOld, log);
		this.addUserPersonalDataHistory(userPersonalDataOld, log);
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(CALLBACK_ACTIVATION_COMPLETE_MESSAGE);
		response.setStatus(status);
		return response;
	}

	@Override
	public EditActivationStatusResponse editActivationStatus(EditActivationStatusRequest request, AuditContext audit) {

		String email = request.getLoginId();
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getActiveVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, request.getVendorCode());
		if (StringUtils.isNotEmpty(request.getIsActive()) && !msVendorRegisteredUser.getIsActive().equals(request.getIsActive())) {
			msVendorRegisteredUser.setIsActive(request.getIsActive());
			msVendorRegisteredUser.setDtmUpd(new Date());
			msVendorRegisteredUser.setUsrUpd(audit.getCallerId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(msVendorRegisteredUser);
		}
		
		AmMsuser requester = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_EDIT_ACT_STATUS);
		
		TrUserDataAccessLog log = new TrUserDataAccessLog();
		log.setUserRequest(requester);
		log.setUserDataAccessed(msVendorRegisteredUser.getAmMsuser());
		log.setAccessDate(new Date());
		log.setLovActionType(actionType);
		log.setIpAddress(StringUtils.isNotBlank(request.getIpAddress()) ? request.getIpAddress() : GlobalVal.IP_ADDRESS_LOCAL);
		log.setRequestDetails("Edit Activation Status");
		log.setUsrCrt(audit.getCallerId());
		log.setDtmCrt(new Date());
		daoFactory.getUserDataAccessLogDao().insertUserDataAccessLog(log);
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(CALLBACK_ACTIVATION_COMPLETE_MESSAGE);
		
		EditActivationStatusResponse response = new EditActivationStatusResponse();
		response.setStatus(status);
		return response;
	}

	@Override
	public CheckPasswordComplexityResponse checkPasswordComplexity(CheckPasswordComplexityRequest request,
			AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);

		CheckPasswordComplexityResponse response = new CheckPasswordComplexityResponse();

		if (StringUtils.isBlank(request.getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordcannotbeemtpy", null, audit),
					ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}

		if (request.getPassword().length() < 8) {
			throw new CheckPasswordComplexityException(getMessage("businesslogic.user.minlengthpassword", null, audit),
					ReasonCheckPasswordComplexity.INVALID_PASSWORD_LENGTH);
		}

		if (!isNewPasswordValid(request.getPassword(), audit)) {
			throw new CheckPasswordComplexityException(
					getMessage("businesslogic.user.invalidpasswordcomplexity", null, audit),
					ReasonCheckPasswordComplexity.INVALID_PASSWORD_COMPLEXITY);
		}

		Status status = new Status();
		status.setCode(0);
		status.setMessage(CALLBACK_ACTIVATION_COMPLETE_MESSAGE);
		response.setStatus(status);
		return response;
	}

	@Override
	public GetSignerDetailResponse getSignerDetail(GetSignerDetailRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		String messageValidation ="";
		
		messageValidation = getMessage("businesslogic.user.signeremailempty", null, audit);
		commonValidatorLogic.validateNotNull(request.getEmail(), messageValidation, StatusCode.SIGNER_EMAIL_EMPTY);
		
		messageValidation = getMessage("businesslogic.user.vendorcodeempty", null, audit);
		commonValidatorLogic.validateNotNull(request.getVendorCode(), messageValidation, StatusCode.VENDOR_CODE_EMPTY);
		
		BigInteger signerIdMsUser = daoFactory.getUserDao().getIdMsUserBySignerRegisteredEmail(request.getEmail());
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL, new Object[] { request.getEmail() }, audit);
		commonValidatorLogic.validateNotNull(signerIdMsUser, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_EMAIL);

		MsVendorRegisteredUser signerDetail = daoFactory.getUserDao().getSignerDetail(signerIdMsUser.longValue(), request.getVendorCode());
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit);
		commonValidatorLogic.validateNotNull(signerDetail, messageValidation, StatusCode.USER_NOT_REGISTERED_IN_VENDOR);
		
		boolean isNoPassword = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_SIGNING);
		
		AmMsuser msUser = daoFactory.getUserDao().getUserByIdMsUser(signerIdMsUser.longValue());
		GetSignerDetailResponse response = new GetSignerDetailResponse();
		response.setIsNoPassword(isNoPassword ? "1" : "0");
		response.setMaxLivenessFaceCompareAttempt("0");
		if (signerDetail.getPhoneBytea() != null) {
			response.setPhoneNo(personalDataEncLogic.decryptToString(signerDetail.getPhoneBytea()));
		}
		
		if (msUser.getLivenessFacecompareValidationDate() != null && DateUtils.isSameDay(msUser.getLivenessFacecompareValidationDate(), new Date()) ) {
			AmGeneralsetting maxLivenessValidationLimit = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT);
			if (msUser.getLivenessFacecompareValidationNum() != null && msUser.getLivenessFacecompareValidationNum() >= Short.valueOf(maxLivenessValidationLimit.getGsValue()) ) {
				response.setMaxLivenessFaceCompareAttempt("1");
			}	
		}
		
		if (msUser.getLivenessFacecompareRequestDate() != null && DateUtils.isSameDay(msUser.getLivenessFacecompareRequestDate(), new Date()) ) {
			AmGeneralsetting maxLivenessLimit = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT);
			if (msUser.getLivenessFacecompareRequestNum() != null && msUser.getLivenessFacecompareRequestNum() >= Short.valueOf(maxLivenessLimit.getGsValue()) ) {
				response.setMaxLivenessFaceCompareAttempt("1");
			}	
		}
		
		
		response.setEmail(signerDetail.getSignerRegisteredEmail());
		return response;
	}

	@Override
	public GetUserActDataResponse getUserActData(GetUserActDataRequest request, AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);

		MsTenant tenant = invLink.getMsTenant();
		MsVendor msVendor = invLink.getMsVendor();
		String vendorCode = msVendor.getVendorCode();
		BigInteger idMsUser;

		if (StringUtils.isNumeric(invLink.getReceiverDetail())) {
			String phone = invLink.getReceiverDetail();
			idMsUser = daoFactory.getUserDao().getIdMsUserByPhoneNo(phone);
		} else {
			String email = invLink.getReceiverDetail();
			idMsUser = daoFactory.getUserDao().getIdMsUserBySignerRegisteredEmail(email);
		}

		if (idMsUser == null) {
			throw new VendorException(getMessage("businesslogic.vendor.vendorregisteredusernotfound", null, audit),
					ReasonVendor.USER_NOT_FOUND);
		}

		MsVendorRegisteredUser vendorRegisteredUserData = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(idMsUser.longValue(), vendorCode);

		if (null == vendorRegisteredUserData) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		if ("0".equals(vendorRegisteredUserData.getIsRegistered())) {
			throw new UserException(getMessage("businesslogic.user.userhasnotregistered", null, audit),
					ReasonUser.USER_HAS_NOT_REGISTERED);
		}

		AmMsuser getUserByIdMsUser = vendorRegisteredUserData.getAmMsuser();
		String fullName = getUserByIdMsUser.getFullName();

		String emailUser = vendorRegisteredUserData.getSignerRegisteredEmail();
		String phoneNo = personalDataEncLogic.decryptToString(vendorRegisteredUserData.getPhoneBytea());
		
		MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "ALLOW_NO_PASSWORD_FOR_ACTIVATION");
		String isNoPassword = null == ts ? "0" : ts.getSettingValue();
		GetUserActDataResponse response = new GetUserActDataResponse();

		response.setFullName(fullName);
		response.setEmail(emailUser);
		response.setPhoneNo(phoneNo);
		response.setIsNoPassword(isNoPassword);

		return response;
	}

	@Override
	public SignerDataVerificationResponse signerDataVerification(SignerDataVerificationRequest request,
			AuditContext audit) {

		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), true, audit);
		
		if (StringUtils.isBlank(request.getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordcannotbeemtpy", null, audit),
					ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		
		if (user.getPassword().equals(REGISTER_BY_INVITATION_PASSWORD)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_DOES_NOT_HAVE_ANY_DOC, null, audit),
					ReasonUser.USER_DOES_NOT_HAVE_ANY_DOCUMENT);
		}
		
		if (!PasswordHash.validatePassword(request.getPassword(), user.getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordnotmatch", null, audit),
					ReasonUser.PASSWORD_NOT_MATCH);
		}

		SignerDataVerificationResponse response = new SignerDataVerificationResponse();

		Status status = new Status();
		status.setCode(0);
		status.setMessage(CALLBACK_ACTIVATION_COMPLETE_MESSAGE);
		response.setStatus(status);
		return response;
	}
	
	private short getOtpActivationDefaultMaxAttempt(AuditContext audit) {
		try {
			String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_OTP_ACTIVATION_USER_DAILY, audit);
			return Short.parseShort(gsValue);
		} catch (Exception e) {
			return 3;
		}
	}
	
	private short getOtpActivationMaxAttempt(MsTenant tenant, AuditContext audit) {
		return tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_OTP_ACTIVATION_MAX_ATTEMPTS, getOtpActivationDefaultMaxAttempt(audit));
	}
	
	private short getOtpSignMaxAttempt(MsTenant tenant, AuditContext audit) {
		// Default value SENGAJA menggunakan getOtpActivationDefaultMaxAttempt(audit) supaya sesuai setting sebelumnya
		return tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_OTP_SIGN_MAX_ATTEMPTS, getOtpActivationDefaultMaxAttempt(audit));
	}

	@Override
	public SentOtpActivationUserResponse sentOtpActivationUser(SentOtpActivationUserRequest request, AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		MsTenant tenant = invLink.getMsTenant();

		if (StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_EMPTY, null, audit), ReasonUser.PHONE_NO_EMPTY);
		}

		if (!request.getPhoneNo().equals(invLink.getPhone())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_INV_NOT_MATCH, null, audit), ReasonUser.PHONE_NOT_MATCH_WITH_INVITATION);
		}
		
		if ( StringUtils.isBlank(request.getSendingPointOption())) {
			throw new TenantException(this.messageSource.getMessage("service.global.emptyparam",
					new String[] {CONST_SENDING_POINT_OPTION}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_API_KEY_EMPTY);
		}
		
		String sendingPointOption = request.getSendingPointOption();
		if(!GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(sendingPointOption) && !GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption) ) {
				throw new UserException(getMessage("service.global.notvalid", new Object[] { CONST_SENDING_POINT_OPTION }, audit), ReasonUser.UNKNOWN);
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), false, audit);
		if (null == user) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND, new Object[] { request.getPhoneNo() }, audit), ReasonUser.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
		}
		
		Date date = new Date();
		Short resetCodeRequestNum = user.getResetCodeRequestNum();

		if (null == user.getResetCodeRequestDate() || !DateUtils.isSameDay(date, user.getResetCodeRequestDate())) {
			resetCodeRequestNum = (short) 0;
		}
		
		Short maxOtpActivationUserRequest = getOtpActivationMaxAttempt(tenant, audit);

		MsVendor msVendor = invLink.getMsVendor();
		String vendorCode = msVendor.getVendorCode();

		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendorCode);
		if (null == msVendorRegisteredUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		if ("1".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage("businesslogic.user.useralreadyactivated", null, audit), ReasonUser.USER_ALREADY_ACTIVATED);
		}

		if (null != resetCodeRequestNum && resetCodeRequestNum >= maxOtpActivationUserRequest) {
			throw new UserException(getMessage("businesslogic.user.maxotpactivationuserreached", null, audit), ReasonUser.MAX_OTP_ACTIVATION_USER_REACHED);
		}

		if (null != resetCodeRequestNum) {
			Short value = (short) (resetCodeRequestNum + 1);
			user.setResetCodeRequestNum(value);
		} else {
			Short value = 1;
			user.setResetCodeRequestNum(value);
		}
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		
		
		MsNotificationtypeoftenant notificationType = daoFactory.getNotificationtypeoftenantDao().getNotificationType(tenant, NotificationSendingPoint.OTP_ACT.toString());
		String notesBalmutSMSOtp = request.getPhoneNo() + " : Send OTP SMS Activation Request";
		String sendingPointSMSGateway = "";
		String sendingPointWAGateway = "";
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_ACTIVATION);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP);

		if (notificationType != null) {
			sendingPointSMSGateway = notificationType.getLovSmsGateway() == null ? GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST :  notificationType.getLovSmsGateway().getCode();
		} else {
			sendingPointSMSGateway = tenant.getLovSmsGateway() == null ? GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST :  tenant.getLovSmsGateway().getCode();
		}
		
		
		if (notificationType != null) {
			sendingPointWAGateway = notificationType.getLovWaGateway() == null ? GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS :  notificationType.getLovWaGateway().getCode();
		} else {
			sendingPointWAGateway = GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS;
		}
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setEmail(user.getLoginId());
		auditTrailBean.setInvLink(invLink);
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setOtpCode(otpCode);
		auditTrailBean.setPhone(request.getPhoneNo());
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(user);
		auditTrailBean.setVendorPsre(vendor);
		
	
		
		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption)) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP, tenant, vendor, audit);
			if (sendingPointWAGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP)) {
				this.sendOtpWaActivation(request.getPhoneNo(), otpCode, invLink, user, audit,auditTrailBean);
			} else {
				this.sendOtpWaHalosisActivation(request.getPhoneNo(), otpCode, invLink, user, audit,auditTrailBean);
			}
		} else {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, tenant, vendor, audit);
			if (sendingPointSMSGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS)) {
				this.sendOtpSmsJatis(request.getPhoneNo(), otpCode, notesBalmutSMSOtp, invLink.getRefNumber(), tenant, user, invLink.getMsOffice(), invLink.getMsBusinessLine(), audit,auditTrailBean);	
			} else {
				this.sendActivationOtpSmsVfirst(request.getPhoneNo(), otpCode, invLink, user, audit,auditTrailBean);
			}
		}
		
		user.setResetCodeRequestDate(date);
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(date);
		daoFactory.getUserDao().updateUser(user);
		
		int durationResend = 0;
		String gsVal = daoFactory.getGeneralSettingDao().getGsValueByCode("DURATION_RESEND_OTP");
		durationResend = tenantSettingsLogic.getSettingValue(tenant, "DURATION_RESEND_OTP_ACTIVATION", Integer.valueOf(gsVal));
		
		SentOtpActivationUserResponse response = new SentOtpActivationUserResponse();
		response.setDurationResendOTP(durationResend);
		
		return response;
	}
	

	private void sendActivationOtpSmsVfirst(String phoneNo, String otpCode, TrInvitationLink invLink, AmMsuser user, AuditContext audit,SigningProcessAuditTrailBean auditTrailBean) {
		
		MsTenant tenant = invLink.getMsTenant();
		
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		
		MsMsgTemplate template = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		} else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}
		
		
		
		SendSmsResponse responseSms = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, template.getBody(), tenant);
		if (gs.getGsValue().equals("1")) {
			responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean,auditTrailBean);
		} else {
			LOG.info("Send SMS OTP activation success");
			responseSms.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		String notesBalmutSMSOtp = phoneNo + " : Send OTP SMS Activation Request";
		if (responseSms.getErrorCode() == null || 
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682) 
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), invLink.getRefNumber(),
					-1, String.valueOf(responseSms.getTrxNo()), user, notesBalmutSMSOtp, responseSms.getGuid(), invLink.getMsOffice(), invLink.getMsBusinessLine(), audit);
		} else {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), invLink.getRefNumber(),
					0, String.valueOf(responseSms.getTrxNo()), user, notesBalmutSMSOtp + GlobalVal.BALMUT_ERROR + responseSms.getErrorCode(),
					responseSms.getGuid(), invLink.getMsOffice(), invLink.getMsBusinessLine(), audit);
		}
	}
	
	@Override
	public VerifyOtpActivationUserResponse verifyOtpActivationUser(VerifyOtpActivationUserRequest request, AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);

		if (StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_EMPTY, null, audit), ReasonUser.PHONE_NO_EMPTY);
		}

		if (!request.getPhoneNo().equals(invLink.getPhone())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_INV_NOT_MATCH, null, audit), ReasonUser.PHONE_NO_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getOtpCode())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, new Object[] { "OTP Code" }, audit), ReasonUser.OTP_EMPTY);
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), false, audit);
		if (null == user) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND,
					new Object[] { request.getPhoneNo() }, audit), ReasonUser.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
		}
		
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_ACTIVATION);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP);
		
		if (invLink.getMsTenant().getOtpActiveDuration() != null && invLink.getMsTenant().getOtpActiveDuration() > 0 && user.getOtpCode() != null && user.getResetCodeRequestDate() != null ) {
			
				Date nowDate = new Date();
				Date requestDate = user.getResetCodeRequestDate();
				long milliSecondsDiff = nowDate.getTime() - requestDate.getTime();
				double minutesDifff =  (double)  milliSecondsDiff / DateUtils.MILLIS_PER_MINUTE;
				
				LOG.info("tenant otp Active Duration : {} | verify OTP activation duration : {}",invLink.getMsTenant().getOtpActiveDuration(),minutesDifff);
				if (minutesDifff > (double) invLink.getMsTenant().getOtpActiveDuration()) {
					user.setUsrUpd("SYSTEM");
					user.setDtmUpd(new Date());
					daoFactory.getUserDao().updateUser(user);
					
					Status stts = new Status();
					stts.setCode(StatusCode.OTP_EXPIRED);
					stts.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_EXPIRED_OTP_CODE, null, audit));
					
					VerifyOtpActivationUserResponse response = new VerifyOtpActivationUserResponse();
					response.setStatus(stts);
					return response;
				
				}
		}
		
		if (!request.getOtpCode().equals(user.getOtpCode())) {
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
			auditTrail.setEmail(user.getLoginId());
			auditTrail.setAmMsUser(user);
			auditTrail.setMsTenant(invLink.getMsTenant());
			auditTrail.setMsVendor(invLink.getMsVendor());
			auditTrail.setLovSendingPoint(sendingPointLov);
			auditTrail.setLovProcessType(signingProcessTypeLov);
			auditTrail.setOtpCode(request.getOtpCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(user.getLoginId());
			auditTrail.setTrInvitationLink(invLink);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null, audit), ReasonUser.OTP_INVALID);
		}

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
		auditTrail.setEmail(user.getLoginId());
		auditTrail.setAmMsUser(user);
		auditTrail.setMsTenant(invLink.getMsTenant());
		auditTrail.setMsVendor(invLink.getMsVendor());
		auditTrail.setLovSendingPoint(sendingPointLov);
		auditTrail.setLovProcessType(signingProcessTypeLov);
		auditTrail.setOtpCode(request.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(user.getLoginId());
		auditTrail.setTrInvitationLink(invLink);
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
		Short resetCodeRequestNum = 0;

		user.setOtpCode(null);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		user.setResetCodeRequestNum(resetCodeRequestNum);
		daoFactory.getUserDao().updateUser(user);
		return new VerifyOtpActivationUserResponse();
	}

	@Override
	public SentOtpSigningVerificationResponse sentOtpSigningVerification(SentOtpSigningVerificationRequest request, AuditContext audit) {

		if (StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_EMPTY, null, audit), ReasonUser.PHONE_NO_EMPTY);
		}
		if (StringUtils.isBlank(request.getVendorCode())) {
			throw new VendorException(getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY, null, audit), 
					ReasonVendor.VENDOR_CODE_EMPTY);
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), true, audit);
		
		if (user.getPassword().equals(REGISTER_BY_INVITATION_PASSWORD)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_DOES_NOT_HAVE_ANY_DOC, null, audit),
					ReasonUser.USER_DOES_NOT_HAVE_ANY_DOCUMENT);
		}
		
		MsTenant msTenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		if (StringUtils.isBlank(request.getSendingPointOption())) {
			throw new TenantException(this.messageSource.getMessage("service.global.emptyparam",
					new String[] {CONST_SENDING_POINT_OPTION}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_API_KEY_EMPTY);
		}
		
		String sendingPointOption = request.getSendingPointOption();
		if(!GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(sendingPointOption) && !GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption) && !GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equals(sendingPointOption)  ) {
				throw new UserException(getMessage("service.global.notvalid", new Object[] { CONST_SENDING_POINT_OPTION }, audit), ReasonUser.UNKNOWN);
		}
		
		Date date = new Date();
		
		List<String> listDocument = request.getDocumentId();
		if (request.getDocumentId() == null) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
					ReasonDocument.EMPTY_DOCUMENT_ID);
		}
		
		List<TrDocumentD> docDs = new ArrayList<>();		
		TrDocumentD docDNewest = new TrDocumentD();
		
		for (String docId : listDocument) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(docId);
			if (null == docD) {
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.documentnotfound",new Object[] {docId},audit), ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
			}
			
			docDs.add(docD);
			
			if (null == docDNewest.getRequestDate() || docD.getRequestDate().after(docDNewest.getRequestDate())  ) {
				docDNewest = docD;
			}
		}
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());
		if (null == msVendorRegisteredUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit), ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		if ("0".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_HASNOT_ACTIVATED, null, audit), ReasonUser.USER_HAS_NOT_ACTIVATED);
		}
		
		if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { request.getVendorCode() }, audit), ReasonUser.USER_CERTIFICATE_EXPIRED);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), msTenant.getTenantCode());
		if (null == useroftenant) {
			throw new UserException(getMessage("businesslogic.user.usernotregisteredintenant", null, audit), ReasonUser.USER_NOT_REGISTERED_IN_TENANT);
		}

		Short resetCodeRequestNum = user.getResetCodeRequestNum();

		if (null == user.getResetCodeRequestDate() || !DateUtils.isSameDay(date, user.getResetCodeRequestDate())) {
			resetCodeRequestNum = (short) 0;
		}

		user.setResetCodeRequestDate(date);
		
		Short maxOtpRequest = getOtpSignMaxAttempt(msTenant, audit);

		if (null != resetCodeRequestNum && resetCodeRequestNum >= maxOtpRequest) {
			throw new UserException(getMessage("businesslogic.user.maxotpsigningverificationreached", null, audit), ReasonUser.MAX_OTP_ACTIVATION_USER_REACHED);
		}

		if (null != resetCodeRequestNum) {
			Short value = (short) (resetCodeRequestNum + 1);
			user.setResetCodeRequestNum(value);
		} else {
			Short value = 1;
			user.setResetCodeRequestNum(value);
		}
		
		MsVendor vendor = null;
		String vendorCodeUpper = StringUtils.upperCase(request.getVendorCode());
		
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_NORMAL);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP);
		MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());

		String durationResendOtp = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_DEFAULT_DURATION_RESEND_OTP);
		int resendOtpDuration = tenantSettingsLogic.getSettingValue(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_DURATION_DURATION_RESEND_OTP_SIGNING, Integer.parseInt(durationResendOtp));
		
		if (vendorCodeUpper.equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			vendor = daoFactory.getVendorDao().getVendorByCode(vendorCodeUpper);
		} else {
			vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendorCodeUpper)) {
			
			SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
			
			auditTrailBean.setDocumentDs(docDs);
			auditTrailBean.setEmail(msVendorRegisteredUser.getSignerRegisteredEmail());
			auditTrailBean.setLovProcessType(signingProcessTypeLov);
			auditTrailBean.setLovSendingPoint(sendingPointLov);
			auditTrailBean.setNotes("Request OTP Signing");
			auditTrailBean.setPhone(request.getPhoneNo());
			auditTrailBean.setTenant(msTenant);
			auditTrailBean.setUser(user);
			auditTrailBean.setVendorPsre(vendor);
			
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, msTenant, vendor, audit);
			this.sendOtpPrivyGeneral(request.getPhoneNo(), msVendorRegisteredUser, user, msTenant, vendor, request.getDocumentId(), auditTrailBean, audit);
			
			user.setOtpCode(null);
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(date);
			daoFactory.getUserDao().updateUser(user);

			SentOtpSigningVerificationResponse response = new SentOtpSigningVerificationResponse();
			response.setOtpByEmail("0");
			response.setDurationResendOTP(resendOtpDuration);
			return response;
		}
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		SentOtpSigningVerificationResponse response = new SentOtpSigningVerificationResponse();
		
		MsNotificationtypeoftenant notificationType = daoFactory.getNotificationtypeoftenantDao().getNotificationType(msTenant, NotificationSendingPoint.OTP_SIGN_NORMAL.toString());
		String sendingPointSMSGateway = "";
		String sendingPointWAGateway = "";
		
		if (notificationType != null) {
			sendingPointSMSGateway = notificationType.getLovSmsGateway() == null ? GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST :  notificationType.getLovSmsGateway().getCode();
		} else {
			sendingPointSMSGateway = msTenant.getLovSmsGateway() == null ? GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST :  msTenant.getLovSmsGateway().getCode();
		}
		
		if (notificationType != null) {
			sendingPointWAGateway = notificationType.getLovWaGateway() == null ? GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS :  notificationType.getLovWaGateway().getCode();
		} else {
			sendingPointWAGateway = GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS;
		}
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		
		auditTrailBean.setDocumentDs(docDs);
		auditTrailBean.setEmail(user.getLoginId());
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setNotes("Request OTP Signing");
		auditTrailBean.setOtpCode(otpCode);
		auditTrailBean.setPhone(request.getPhoneNo());
		auditTrailBean.setTenant(msTenant);
		auditTrailBean.setUser(user);
		auditTrailBean.setVendorPsre(vendorPsre);
		
		String notes = null;
		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption)){
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP, msTenant, vendor, audit);
			notes = request.getPhoneNo() + GlobalVal.SEND_OTP_WA_SIGNING;
			if (GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP.equals(sendingPointWAGateway)) {
				this.sendOtpWa(request.getPhoneNo(), otpCode, msTenant, user, audit, docDNewest.getTrDocumentH(), docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(), notes, auditTrailBean);
				LOG.info("Send OTP WA : {} ",request.getPhoneNo());
			} else {
				this.sendOtpWaHaloSis(request.getPhoneNo(), otpCode, msTenant, user, audit, docDNewest.getTrDocumentH(), docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(), notes, auditTrailBean);
				LOG.info("Send OTP WA Halosis : {} ",request.getPhoneNo());
			}
		} else if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equals(sendingPointOption)){
			if (msVendorRegisteredUser.getEmailService().equals("1")) {
				throw new UserException(getMessage("businesslogic.user.userregisterwithoutemail", null, audit), ReasonUser.USER_REGISTERED_WITHOUT_EMAIL);
			} else {
				Map<String, Object> templateParameters = new HashMap<>();
				Map<String, Object> userMap = new HashMap<>();
				userMap.put(FULLNAME, user.getFullName());
				userMap.put("otp", otpCode);
				
				MsMsgTemplate template = null;
				if (msTenant.getOtpActiveDuration() != null && msTenant.getOtpActiveDuration() > 0 ) {
					userMap.put(GlobalVal.DURATION, msTenant.getOtpActiveDuration());
					templateParameters.put("user", userMap);
					template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_VERIF_EMAIL_WITH_DURATION, templateParameters);
				} else {
					templateParameters.put("user", userMap);
					template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP, templateParameters);
				}
				response.setOtpByEmail("1");
				EmailInformationBean emailInfo = new EmailInformationBean();
				emailInfo.setFrom(fromEmailAddr);
				emailInfo.setTo(new String[] { msVendorRegisteredUser.getSignerRegisteredEmail() });
				emailInfo.setSubject(template.getSubject());
				emailInfo.setBodyMessage(template.getBody());
				try {
					emailSenderLogic.sendEmail(emailInfo, null, auditTrailBean);
					LOG.info("Send OTP EMAIL : {}",msVendorRegisteredUser.getSignerRegisteredEmail());
				} catch (MessagingException e) {
					throw new EmailException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMAIL_SENDER, null,
							this.retrieveLocaleAudit(audit)), ReasonEmail.SEND_EMAIL_ERROR);
				}
			}
			
		} else {
			notes = request.getPhoneNo() + GlobalVal.SEND_OTP_SMS_SIGNING;
			if (sendingPointSMSGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS)) {
				balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, msTenant, vendor, audit);
				this.sendOtpSmsJatis(request.getPhoneNo(), otpCode, notes, null, msTenant, user, docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(), audit, auditTrailBean);
				response.setOtpByEmail("0");
			} else {
				balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, msTenant, vendor, audit);
				this.sendOtpSms(request.getPhoneNo(), otpCode, notes, msTenant, user,docDNewest.getTrDocumentH(),docDNewest.getTrDocumentH().getMsOffice(),docDNewest.getTrDocumentH().getMsBusinessLine(),docDNewest.getTrDocumentH().getRefNumber(), audit, auditTrailBean);
				response.setOtpByEmail("0");
			}
			
		}
		response.setDurationResendOTP(resendOtpDuration);
		
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(date);
		daoFactory.getUserDao().updateUser(user);
		
		return response;
	}
	
	public String getNotificationVendor(String sendingPointSMSGateway, String sendingPointWAGateway, String sendingPointOption) {
		String notificationVendor = null;
		
		if (sendingPointSMSGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST) && sendingPointOption.equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS)) {
			notificationVendor = GlobalVal.NOTIFICATION_VENDOR_NAME_SMS_VFIRST;
		} else if (sendingPointSMSGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS) && sendingPointOption.equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS)) {
			notificationVendor = GlobalVal.NOTIFICATION_VENDOR_NAME_SMS_JATIS;
		} else if (sendingPointWAGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS) && sendingPointOption.equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA)) {
			notificationVendor = GlobalVal.NOTIFICATION_VENDOR_NAME_WHATSAPP_HALOSIS;
		} else if (sendingPointWAGateway.equals(GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP) && sendingPointOption.equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA)) {
			notificationVendor = GlobalVal.NOTIFICATION_VENDOR_NAME_WHATSAPP_JATIS;
		}
		return notificationVendor;
	}

	@Transactional(noRollbackFor={PrivyException.class})
	@Override
	public VerifyOtpSigningVerificationResponse verifyOtpSigningVerification(VerifyOtpSigningVerificationRequest request, AuditContext audit) {
		
		String messageValidation = null;

		if (StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_EMPTY, null, audit),
					ReasonUser.PHONE_NO_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "OTP Code" }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getVendorCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "Vendor Code" }, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_CODE_EMPTY);
		}
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "Tenant Code" }, this.retrieveLocaleAudit(audit)), ReasonUser.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		
		if (null == tenant) {
			throw new TenantException(getMessage("businesslogic.tenant.tenantnotfound", new Object[] {request.getTenantCode()}, audit), 
					ReasonTenant.TENANT_NOT_FOUND);
		}
		
		boolean checkUserExistence = false;
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), checkUserExistence, audit);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND, new Object[] { request.getPhoneNo() }, audit);

		commonValidatorLogic.validateNotNull(user, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
		
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_NORMAL);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP);

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());

		if ( (!request.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) && tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0 && user.getOtpCode() != null && user.getResetCodeRequestDate() != null ) {
			
			Date nowDate = new Date();
			Date requestDate = user.getResetCodeRequestDate();
			long milliSecondsDiff = nowDate.getTime() - requestDate.getTime();
			double minutesDiff =  (double)  milliSecondsDiff / DateUtils.MILLIS_PER_MINUTE;
			
			LOG.info("Tenant OTP Active Duration : {} | Verify OTP Activation Duration : {}", tenant.getOtpActiveDuration(), minutesDiff);
			if (minutesDiff > (double) tenant.getOtpActiveDuration()) {
				user.setUsrUpd("SYSTEM");
				user.setDtmUpd(new Date());
				daoFactory.getUserDao().updateUser(user);
				
				Status status = new Status();
				status.setCode(StatusCode.OTP_EXPIRED);
				status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_EXPIRED_OTP_CODE,
						null, audit));
				VerifyOtpSigningVerificationResponse response = new VerifyOtpSigningVerificationResponse();
				response.setStatus(status);
				return response;
			
			}
		}
		
		if (request.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID))	{			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit);

			commonValidatorLogic.validateNotNull(vendor, messageValidation, StatusCode.VENDOR_NOT_FOUND);

			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit);

			commonValidatorLogic.validateNotNull(request.getDocumentId(), messageValidation, StatusCode.EMPTY_DOCUMENT_ID);
			
			
			TrPsreSigningConfirmation tpsc = daoFactory.getDocumentDao().getTrPsreSigningConfirmationByIdMsUser(user.getIdMsUser());
			
			messageValidation = getMessage("businesslogic.user.requestotp", null, audit);

			commonValidatorLogic.validateNotNull(tpsc, messageValidation, StatusCode.OTP_INVALID);
			
			List<TrDocumentD> docD = new ArrayList<>();
			
			for (String documentId : request.getDocumentId()) {
				
				TrDocumentD documentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
				
				docD.add(documentD);
			}
			
			SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
			
			auditTrailBean.setPhone(request.getPhoneNo());
			auditTrailBean.setUser(user);
			auditTrailBean.setTenant(tenant);
			auditTrailBean.setVendorPsre(vendor);
			auditTrailBean.setDocumentDs(docD);
			auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
			
			PrivyGeneralOtpValidationResponse privyConfirmOtp = privyGeneralLogic.otpValidation(request.getOtpCode(), tpsc, tenant, vendor, auditTrailBean, audit);
			
			if (privyConfirmOtp.getError() != null) {
				String errorMessage = privyGeneralLogic.buildOtpValidationErrorMessage(privyConfirmOtp, audit);
				
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
				auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendor);
				auditTrail.setLovSendingPoint(sendingPointLov);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setOtpCode(request.getOtpCode());
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				
				throw new PrivyException(errorMessage);
			}
			if(tpsc != null) {
				daoFactory.getDocumentDao().deletePsreSigningConfirmation(tpsc);
			}
			
//			privy core
//			String token = daoFactory.getVendorRegisteredUserDao().getVendorAccessTokenByVendorCodeAndidMsUser(vendor.getVendorCode(), user.getIdMsUser());
//			
//			PrivyConfirmOtpRequest confirmOtp = new PrivyConfirmOtpRequest();
//			confirmOtp.setCode(request.getOtpCode());
//			confirmOtp.setToken(token);
//			
//			PrivyConfirmOtpResponse privyConfirmOtp = privyLogic.confirmOtp(confirmOtp, tenant, vendor, audit);
//			
//			if(privyConfirmOtp.getCode() != 201) {
//				throw new PrivyException(privyConfirmOtp.getMessage());
//			}
		}
		else {
			if (!request.getOtpCode().equals(user.getOtpCode())) {
				
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
				auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendor);
				auditTrail.setLovSendingPoint(sendingPointLov);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setOtpCode(request.getOtpCode());
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				
				throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
						this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
			}
		}
		
		Short resetCodeRequestNum = 0;

		user.setOtpCode(null);
		user.setLivenessFacecompareRequestNum(resetCodeRequestNum);
		user.setLivenessFacecompareValidationNum(resetCodeRequestNum);
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
		auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
		auditTrail.setAmMsUser(user);
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendor);
		auditTrail.setLovSendingPoint(sendingPointLov);
		auditTrail.setLovProcessType(signingProcessTypeLov);
		auditTrail.setOtpCode(request.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
		
		return new VerifyOtpSigningVerificationResponse();
	}

	@Override
	public UpdateActivationUserResponse updateActivationUser(UpdateActivationUserRequest request, AuditContext audit) {
		
		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		String messageValidation = ""; 
		
		messageValidation = getMessage("businesslogic.user.phonenoisempty", null, audit);
		commonValidatorLogic.validateNotNull(request.getPhoneNo(), messageValidation, StatusCode.PHONE_NUM_EMPTY);
		
		if (!request.getPhoneNo().equals(invLink.getPhone())) {
			throw new UserException(getMessage("businesslogic.user.phonenotmatchwithinvitation", null, audit), ReasonUser.PHONE_NOT_MATCH_WITH_INVITATION);
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), true, audit);
		
		MsTenant tenant = invLink.getMsTenant();
		MsVendor msVendor = invLink.getMsVendor();
		String vendorCode = msVendor.getVendorCode();
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendorCode);
	
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit);
		commonValidatorLogic.validateNotNull(msVendorRegisteredUser, messageValidation, StatusCode.USER_NOT_REGISTERED_IN_VENDOR);
		
		
		if ("0".equals(msVendorRegisteredUser.getIsRegistered())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED, new String[] { "User" }, this.retrieveLocaleAudit(audit)), ReasonUser.NOT_REGISTERED);
		}
		
		if ("1".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage("businesslogic.user.useralreadyactivated", null, audit), ReasonUser.USER_ALREADY_ACTIVATED);
		}
		
		msVendorRegisteredUser.setIsActive("1");
		msVendorRegisteredUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));
		msVendorRegisteredUser.setDtmUpd(new Date());
		
		Date activationDate = new Date();
		msVendorRegisteredUser.setActivatedDate(activationDate);
		
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(msVendorRegisteredUser);
		
		invLink.setIsActive("0");
		invLink.setGender(null);
		invLink.setKelurahan(null);
		invLink.setKecamatan(null);
		invLink.setKota(null);
		invLink.setZipCode(null);
		invLink.setPlaceOfBirth(null);
		invLink.setDateOfBirth(null);
		invLink.setProvinsi(null);
		invLink.setEmail(null);
		invLink.setPhotoSelf(null);
		invLink.setPhotoId(null);
		invLink.setIdNo(null);
		invLink.setUsrUpd(audit.getCallerId());
		invLink.setPhone(null);
		invLink.setAddress(null);
		invLink.setDtmUpd(new Date());
		daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		
		if (!tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_ACTIVATION)) {
			String hashedPassword = PasswordHash.createHash(request.getPassword());
			user.setPassword(hashedPassword);
			user.setChangePwdLogin("0");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUser(user);
			
			MsLov passwordChangeType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE, GlobalVal.CODE_LOV_PWD_CHANGE_TYPE_NEW);
			AmUserpwdhistory newUserPwdhistory = new AmUserpwdhistory();
			newUserPwdhistory.setAmMsuser(user);
			newUserPwdhistory.setMsLov(passwordChangeType);
			newUserPwdhistory.setUsrCrt(audit.getCallerId());
			newUserPwdhistory.setDtmCrt(new Date());
			newUserPwdhistory.setPassword(hashedPassword);
			daoFactory.getUserDao().insertUserPwdhistory(newUserPwdhistory);
		} else {
			user.setPassword("noPassword");
			user.setChangePwdLogin("0");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUser(user);
		}
			
		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);

		UpdErrHistRerunProcessBean bean = new UpdErrHistRerunProcessBean(personalDataBean.getIdNoRaw(), invLink.getMsVendor().getIdMsVendor());
		QueuePublisher.queueUpdErrHistRerunProcess(bean);

		String phoneNo = personalDataEncLogic.decryptToString(msVendorRegisteredUser.getPhoneBytea());
		UpdateActivationUserResponse response = new UpdateActivationUserResponse();
		response.setFullName(user.getFullName());
		response.setIdNo(personalDataBean.getIdNoRaw());
		response.setPhoneNo(phoneNo);
		response.setEmail(msVendorRegisteredUser.getSignerRegisteredEmail());
		
		String regisDate  = MssTool.formatDateToStringIn(msVendorRegisteredUser.getDtmCrt(), "dd MMMM YYYY HH:mm:ss");
		response.setRegisteredDate(regisDate);
		String redirectUrlTenant = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_REDIRECT_URL_AFTER_ACTIVATION,"");
		
		if (StringUtils.isNotBlank(redirectUrlTenant)){
			response.setRedirectUrl(redirectUrlTenant);
			String countDownGenset = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_TIME_TO_REDIRECT_AFTER_ACTIVATION);
			int redirectCountDown = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_TIME_TO_REDIRECT, Integer.parseInt(countDownGenset));
			response.setRedirectCountDown(redirectCountDown);
		} else if ("1".equals(invLink.getIsEmbed())) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("officeCode", user.getMsOffice().getOfficeCode());
	        jsonObject.put("email", msVendorRegisteredUser.getSignerRegisteredEmail());
	        Date now = new Date();
	        // Format the date and time
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        String formattedDate = sdf.format(now);
	        jsonObject.put("timestamp", formattedDate);

	        String encryptedMsg = commonLogic.encryptMessageToString(jsonObject.toString(),tenant.getAesEncryptKey(), audit);
	        String urlEmbedUser = embedDashboardUrl + encryptedMsg + GlobalVal.CONST_URL_PATH_EMBED_DASHBOARD_SETTING;
	        String countDown = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_TIME_TO_REDIRECT_AFTER_ACTIVATION);
			int redirectCountDown = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_TIME_TO_REDIRECT, Integer.parseInt(countDown));
			response.setRedirectCountDown(redirectCountDown);
			response.setRedirectUrl(urlEmbedUser);

		} else if ("1".equals(invLink.getIsRedirectUrl())) {
			String countDown = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_TIME_TO_REDIRECT_AFTER_ACTIVATION);
			int redirectCountDown = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_TIME_TO_REDIRECT, Integer.parseInt(countDown));
			response.setRedirectCountDown(redirectCountDown);
			response.setRedirectUrl(baseEsignUrl);
		}
		
		MsLov process = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_ECERT_NOTIF);
		MsLov sendPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.CERT_NOTIF.getLovCode());
		
		TrSigningProcessAuditTrail auditTrailUpdate = new TrSigningProcessAuditTrail();
		auditTrailUpdate.setUsrCrt(audit.getCallerId());
		auditTrailUpdate.setDtmCrt(new Date());
		auditTrailUpdate.setEmail(msVendorRegisteredUser.getSignerRegisteredEmail());
		auditTrailUpdate.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
		auditTrailUpdate.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
		auditTrailUpdate.setMsTenant(tenant);
		auditTrailUpdate.setMsVendor(msVendor);
		auditTrailUpdate.setLovProcessType(process);
		auditTrailUpdate.setResultStatus("1");
		auditTrailUpdate.setLovSendingPoint(sendPoint);
		auditTrailUpdate.setAmMsUser(user);
		auditTrailUpdate.setTrInvitationLink(invLink);
		
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendorCode)) {
			NotificationType notifType = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.CERT_NOTIF, msVendorRegisteredUser.getEmailService());
			if (NotificationType.EMAIL == notifType || !"1".equals(tenant.getSendCertNotifBySms())) {
				auditTrailUpdate.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
				this.sendEmailCertificateNotification(user.getFullName(), msVendorRegisteredUser.getSignerRegisteredEmail(), audit);
			} else if (NotificationType.SMS_JATIS == notifType) {
				auditTrailUpdate.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);
				auditTrailUpdate.setNotificationVendor(gateway.getDescription());
				this.sendSmsCertNotifJatis(phoneNo, tenant, user, invLink, auditTrailUpdate, audit);
			} else {
				auditTrailUpdate.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
				auditTrailUpdate.setNotificationVendor(gateway.getDescription());
				this.sendSmsCertificateNotification(phoneNo, tenant, user, invLink, auditTrailUpdate, audit);
			}
		}
		
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrailUpdate);
		MsLov lovCallbackType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_CALLBACK_TYPE, GlobalVal.CODE_LOV_CALLBACK_TYPE_ACTIVATION_COMPLETE);
		callbackLogic.executeCallbackToClient(tenant, lovCallbackType, msVendorRegisteredUser, null, CALLBACK_ACTIVATION_COMPLETE_MESSAGE, audit);
		
		return response;
	}

	@Override
	public VerifyLivenessFaceCompareResponse verifyLivenessFaceCompare(VerifyLivenessFaceCompareRequest request, AuditContext audit) {
		
		VerifyLivenessFaceCompareResponse response = new VerifyLivenessFaceCompareResponse();
		List<String> listDocument = request.getDocumentId();
		TrDocumentD docDNewest = new TrDocumentD();

		String messageValidation = "";
		
		for (int i = 0; i < listDocument.stream().count(); i++ ) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocument.get(i));
			
			messageValidation = getMessage("businesslogic.document.documentnotfound", new Object[] {listDocument.get(i)}, audit);

			commonValidatorLogic.validateNotNull(docD, messageValidation, StatusCode.DOCUMENT_NOT_FOUND);
			
			if (null == docDNewest.getRequestDate() || docD.getRequestDate().after(docDNewest.getRequestDate())  ) {
				docDNewest = docD;
			}
				
		}
		
		if (StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_EMAIL, null, audit), ReasonUser.EMAIL_EMPTY);
		}
		
		boolean checkUserExistence = false;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), checkUserExistence, audit);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL, new Object[] { request.getEmail() }, audit);

		commonValidatorLogic.validateNotNull(user, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_EMAIL);
		
		AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(user);
		PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		String nik = personalDataEncLogic.decryptToString(userPersonalData.getIdNoBytea());
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit);

		commonValidatorLogic.validateNotNull(msVendorRegisteredUser, messageValidation, StatusCode.USER_NOT_REGISTERED_IN_VENDOR);
		
		if ("0".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_HASNOT_ACTIVATED, null, audit),
					ReasonUser.USER_HAS_NOT_ACTIVATED);
		}
		
		if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { request.getVendorCode() } , audit),
					ReasonUser.USER_CERTIFICATE_EXPIRED);		
		}
		
		MsUseroftenant msUserOfTenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), request.getTenantCode());
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_TENANT_NOT_FOUND, new String[] { request.getEmail(), request.getTenantCode() }, audit);

		commonValidatorLogic.validateNotNull(msUserOfTenant, messageValidation, StatusCode.USER_TENANT_NOT_FOUND);
		
		CheckLivenessFaceCompareServiceRequest checkLivenessFaceCompareServiceRequest = new CheckLivenessFaceCompareServiceRequest();
		checkLivenessFaceCompareServiceRequest.setTenantCode(request.getTenantCode());
		
		CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareService = tenantLogic.checkLivenessFaceCompareService(checkLivenessFaceCompareServiceRequest, audit);
		
		if (checkLivenessFaceCompareService.getLivenessFacecompareServicesStatus().equals("0")) {
			throw new TenantException(getMessage("businesslogic.tenant.tenantlivenessfacecompareservicesnotactive", new Object[] {request.getTenantCode()}, audit), 
					ReasonTenant.TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		String splitLivenessFaceCompareBill = tenant.getSplitLivenessFaceCompareBill();
		
		MsLov livenessFaceCompareBalanceType = new MsLov();
		MsLov livenessFaceCompareTrxType = new MsLov();
		String livenessFaceCompareNotes = null;
		BigInteger livenessFaceCompareBalance = null;
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode("ESG");
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String refNo = docDNewest.getTrDocumentH().getRefNumber();
		int qty = -1;
		
		MsLov livenessBalanceType = new MsLov();
		MsLov livenessTrxType = new MsLov();
		String livenessNotes = null;
		BigInteger livenessBalance = null;
		MsLov faceCompareBalanceType = new MsLov();
		MsLov faceCompareTrxType = new MsLov();
		String faceCompareNotes = null;
		BigInteger faceCompareBalance = null;
		
		
		if (splitLivenessFaceCompareBill.equals("0")) {
			livenessFaceCompareBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
					GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS_FACECOMPARE);
			livenessFaceCompareTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, 
					GlobalVal.CODE_LOV_TRX_TYPE_ULIVENESS_FACECOMPARE);
			livenessFaceCompareNotes = "Signing Liveness Face Compare Success";
			
			livenessFaceCompareBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, livenessFaceCompareBalanceType);
			
			if (livenessFaceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {livenessFaceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
			
		} else {
			livenessBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
					GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS);
			livenessTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, 
					GlobalVal.CODE_LOV_TRX_TYPE_ULIVENESS);
			livenessNotes = "Signing Liveness Success";
			
			livenessBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, livenessBalanceType);
			
			faceCompareBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
					GlobalVal.CODE_LOV_BALANCE_TYPE_FACECOMPARE);
			faceCompareTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, 
					GlobalVal.CODE_LOV_TRX_TYPE_UFACECOMPARE);
			faceCompareNotes = "Signing Face Compare Success";
			
			faceCompareBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, faceCompareBalanceType);
			
			if (livenessBalance.longValue() < 1 && faceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage("businesslogic.saldo.balancesarenotenough", new Object[] {livenessBalanceType.getDescription(), faceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			} else if (livenessBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {livenessBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			} else if (faceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {faceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
		}
		
		Short livenessFacecompareRequestNum = user.getLivenessFacecompareRequestNum();
		Short livenessFacecompareValidationNum = user.getLivenessFacecompareValidationNum();
		Date date = new Date();
		
		if (null == user.getLivenessFacecompareRequestDate() || !DateUtils.isSameDay(date, user.getLivenessFacecompareRequestDate())) {
			livenessFacecompareRequestNum = (short) 0;
		}
		
		if (null == user.getLivenessFacecompareValidationDate() || !DateUtils.isSameDay(date, user.getLivenessFacecompareValidationDate())) {
			livenessFacecompareValidationNum = (short) 0;
		}
		
		user.setLivenessFacecompareRequestDate(date);
		
		MsTenant msTenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());

		String gsLimitRequest = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, audit);
		
		short maxLimitLivenessFacecompareRequest = tenantSettingsLogic.getSettingValue(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT, Short.parseShort(gsLimitRequest));
		
		String gsLimitValidation = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, audit);
		
		short maxLimitLivenessFacecompareValidation = tenantSettingsLogic.getSettingValue(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, Short.parseShort(gsLimitValidation));
		
		if ((null != livenessFacecompareRequestNum && livenessFacecompareRequestNum >= maxLimitLivenessFacecompareRequest) || (null != livenessFacecompareValidationNum && livenessFacecompareValidationNum >= maxLimitLivenessFacecompareValidation)) {
			throw new UserException(getMessage("businesslogic.user.maxlivenessfacecomparereached", null, audit),
					ReasonUser.MAX_LIVENESS_FACECOMPARE_REACHED);
		}
		
		String key = tenant.getApiKey();
		String livenessFaceCompareTenantCode = GlobalVal.TENANT_CODE_LIVENESS_FACECOMPARE + "-" + request.getTenantCode();
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_VIDA_SELFPHOTO_NOT_FOUND, null, audit);

		commonValidatorLogic.validateNotNull(userPersonalData.getPhotoSelf(), messageValidation, StatusCode.PHOTO_NOT_FOUND);
		
		String cutImg = MssTool.cutImageStringPrefix(request.getImg1());
		byte[] img1 =  Base64.getDecoder().decode(cutImg);
		byte[] img2 = pd.getSelfPhotoRaw();
		

		MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_LIVENESS_FACECOMPARE);
		
		LivenessFaceCompareRequest livenessFaceCompareRequest = new LivenessFaceCompareRequest(user.getLoginId(), livenessFaceCompareTenantCode, request.getVendorCode(), nik, key, img1, img2);
		LivenessFaceCompareResponse livenessFaceCompareResponse =  new LivenessFaceCompareResponse();
		
		String generalSetting = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_BYPASS_LIVENESS_FACE_COMPARE_CHECK);

		Status status = new Status();

		if ("0".equals(generalSetting) || StringUtils.isBlank(generalSetting)) {
			try {
				livenessFaceCompareResponse =  livenessFaceCompare(livenessFaceCompareRequest, audit);
			} catch (VidaException e) {
				
				if (null != livenessFacecompareValidationNum) {
					Short value = (short) (livenessFacecompareValidationNum + 1);
					user.setLivenessFacecompareValidationNum(value);
				} else {
					Short value = 1;
					user.setLivenessFacecompareValidationNum(value);
				}
				
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
	
				auditTrail.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
				auditTrail.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
				auditTrail.setEmail(user.getLoginId());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendorPsre);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(user.getLoginId());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				
				String textFilename = String.valueOf(auditTrail.getIdSigningProcessAuditTrail()) + GlobalVal.EXTENTION_PICTURE_JPEG;
				
				byte[] zipImg1 = ZipCompressionUtils.zipBase64Data(textFilename, request.getImg1());
				
				String filename = cloudStorageLogic.storeLivenessFaceComparePhoto(auditTrail.getIdSigningProcessAuditTrail(), zipImg1);
				
				auditTrail.setNotes(filename);
				daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrail(auditTrail);
	
				for (String docId : listDocument) {
					TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(docId);
	
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(user.getLoginId());
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
				}
				
				user.setLivenessFacecompareValidationDate(new Date());
				user.setUsrCrt(audit.getCallerId());
				user.setDtmUpd(date);
				daoFactory.getUserDao().updateUser(user);	
			
				status = new Status();
				status.setCode(e.getErrorCode());
				status.setMessage(e.getMessage());
				response.setStatus(status);
				return  response;
				
			}
			
			status.setCode(0);	
			
			String faceCompareResult = livenessFaceCompareResponse.getResult().get(0).getFaceCompare().getCompare();
			String faceLivenessResult = livenessFaceCompareResponse.getResult().get(0).getFaceLiveness().getLive();
			String service = livenessFaceCompareResponse.getService();
			String resultStatus = "0";
			
			if(faceCompareResult.equals("True")  && faceLivenessResult.equals("True")) {
				user.setLivenessFacecompareRequestNum((short) 0);
				user.setLivenessFacecompareValidationNum((short) 0);
				user.setResetCodeRequestNum((short) 0);
				user.setDtmUpd(new Date());
				user.setUsrUpd(audit.getCallerId());
				resultStatus = "1";
			} else {
				if (faceLivenessResult.equals("False")) {
					status.setCode(StatusCode.LIVENESS_FAILED);
					status.setMessage("Verifikasi Liveness gagal. Harap mengambil Foto Selfie langsung, Pastikan wajah anda terlihat jelas tidak tertutup oleh aksesoris.");
					livenessNotes = CONST_SIGNING_LIVENESS_FAILED;
				} else if (faceCompareResult.equals("False") && faceLivenessResult.equals("True")) {
					status.setCode(StatusCode.FACE_COMPARE_FAILED);
					status.setMessage("Verifikasi user gagal. Foto Diri tidak sesuai.");
					faceCompareNotes = CONST_SIGNING_FACE_COMPARE_FAILED;
				} else if (faceCompareResult.equals("False") && faceLivenessResult.equals("False")) {
					status.setCode(StatusCode.LIVENESS_AND_FACE_COMPARE_FAILED);
					status.setMessage("Verify Liveness Face Compare Gagal");
					livenessNotes = CONST_SIGNING_LIVENESS_FAILED;
					faceCompareNotes = CONST_SIGNING_FACE_COMPARE_FAILED;
				}
				livenessFaceCompareNotes = "Signing Liveness Face Compare Failed";
				
				if (null != livenessFacecompareRequestNum) {
					Short value = (short) (livenessFacecompareRequestNum + 1);
					user.setLivenessFacecompareRequestNum(value);
				} else {
					Short value = 1;
					user.setLivenessFacecompareRequestNum(value);
				}
			}
			
			user.setUsrCrt(audit.getCallerId());
			user.setDtmUpd(date);
			daoFactory.getUserDao().updateUser(user);
			
			if (splitLivenessFaceCompareBill.equals("0")) {
				saldoLogic.insertBalanceMutation(null, null, null, livenessFaceCompareBalanceType, livenessFaceCompareTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessFaceCompareNotes, null,docDNewest.getTrDocumentH().getMsOffice(),docDNewest.getTrDocumentH().getMsBusinessLine(), audit);
			} else if (splitLivenessFaceCompareBill.equals("1") && service.equals("1")){
				saldoLogic.insertBalanceMutation(null, null, null, livenessBalanceType, livenessTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessNotes, null,docDNewest.getTrDocumentH().getMsOffice(),docDNewest.getTrDocumentH().getMsBusinessLine(), audit);
			} else if (splitLivenessFaceCompareBill.equals("1") && service.equals("2")) {
				saldoLogic.insertBalanceMutation(null, null, null, livenessBalanceType, livenessTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessNotes, null,docDNewest.getTrDocumentH().getMsOffice(),docDNewest.getTrDocumentH().getMsBusinessLine(), audit);
				
				nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
				
				saldoLogic.insertBalanceMutation(null, null, null, faceCompareBalanceType, faceCompareTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, faceCompareNotes, null,docDNewest.getTrDocumentH().getMsOffice(),docDNewest.getTrDocumentH().getMsBusinessLine(), audit);
			}
			
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
	
			auditTrail.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
			auditTrail.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
			auditTrail.setEmail(user.getLoginId());
			auditTrail.setAmMsUser(user);
			auditTrail.setMsTenant(tenant);
			auditTrail.setMsVendor(vendorPsre);
			auditTrail.setLovProcessType(signingProcessTypeLov);
			auditTrail.setResultStatus(resultStatus);
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(user.getLoginId());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
					
			String textFilename = String.valueOf(auditTrail.getIdSigningProcessAuditTrail()) + GlobalVal.EXTENTION_PICTURE_JPEG;
			
			byte[] zipImg1 = ZipCompressionUtils.zipBase64Data(textFilename, request.getImg1());
			
			String filename = cloudStorageLogic.storeLivenessFaceComparePhoto(auditTrail.getIdSigningProcessAuditTrail(), zipImg1);
			
			auditTrail.setNotes(filename);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
	
			for (String docId : listDocument) {
				TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(docId);
	
				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(docD);
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(user.getLoginId());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);
			}
		} else {
			status.setCode(0);
		}

		response.setStatus(status);
		return response;
	}

	@Override
	public VerifyLivenessFaceCompareFullApiResponse verifyLivenessFaceCompareFullApi(VerifyLivenessFaceCompareFullApiRequest request, SigningProcessAuditTrailBean auditTrailBean,
			AuditContext audit) {
		VerifyLivenessFaceCompareFullApiResponse response = new VerifyLivenessFaceCompareFullApiResponse();
		
		if (StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_EMAIL, null, audit),
					ReasonUser.EMAIL_EMPTY);
		}
		
		boolean checkUserExistence = false;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), checkUserExistence, audit);
		
		if (null == user) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL, 
					new Object[] { request.getEmail() }, audit), ReasonUser.USER_NOT_FOUND_WITH_THAT_EMAIL);
		}
		
		AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(user);
		PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		String nik = personalDataEncLogic.decryptToString(userPersonalData.getIdNoBytea());
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());
		
		if (null == msVendorRegisteredUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}
		
		if ("0".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_HASNOT_ACTIVATED, null, audit),
					ReasonUser.USER_HAS_NOT_ACTIVATED);
		}
		
		MsUseroftenant msUserOfTenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), request.getTenantCode());
		
		if (null == msUserOfTenant) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_TENANT_NOT_FOUND,
					new String[] { request.getEmail(), request.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonUser.USER_TENANT_NOT_FOUND);
		}
		
		//get document details
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		if (null == docD ){
			throw new SignConfirmationDocumentException(getMessage("businesslogic.document.documentidnotfound",new Object[] {request.getDocumentId()},audit),
				ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
		}
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentDetailByDocId(docD.getDocumentId()).getTrDocumentH();
		
		CheckLivenessFaceCompareServiceRequest checkLivenessFaceCompareServiceRequest = new CheckLivenessFaceCompareServiceRequest();
		checkLivenessFaceCompareServiceRequest.setTenantCode(request.getTenantCode());
		
		CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareService = tenantLogic.checkLivenessFaceCompareServiceFullApi(checkLivenessFaceCompareServiceRequest, audit);
		
		if (checkLivenessFaceCompareService.getLivenessFacecompareServicesStatus().equals("0")) {
			throw new TenantException(getMessage("businesslogic.tenant.tenantlivenessfacecompareservicesnotactive", new Object[] {request.getTenantCode()}, audit), 
					ReasonTenant.TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		String splitLivenessFaceCompareBill = tenant.getSplitLivenessFaceCompareBill();
		
		MsLov livenessFaceCompareBalanceType = new MsLov();
		MsLov livenessFaceCompareTrxType = new MsLov();
		String livenessFaceCompareNotes = null;
		BigInteger livenessFaceCompareBalance = null;
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode("ESG");
		List<String> trxNo = new ArrayList<>();
		int trxNoNum = 0;
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		trxNo.add(trxNoNum, String.valueOf(nextTrxNo));
		String refNo = docH.getRefNumber();
		int qty = -1;
		
		MsLov livenessBalanceType = new MsLov();
		MsLov livenessTrxType = new MsLov();
		String livenessNotes = null;
		BigInteger livenessBalance = null;
		MsLov faceCompareBalanceType = new MsLov();
		MsLov faceCompareTrxType = new MsLov();
		String faceCompareNotes = null;
		BigInteger faceCompareBalance = null;
				
		if (splitLivenessFaceCompareBill.equals("0")) {
			livenessFaceCompareBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
					GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS_FACECOMPARE);
			livenessFaceCompareTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, 
					GlobalVal.CODE_LOV_TRX_TYPE_ULIVENESS_FACECOMPARE);
			livenessFaceCompareNotes = "Signing Liveness Face Compare Success";
			
			livenessFaceCompareBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, livenessFaceCompareBalanceType);
			
			if (livenessFaceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {livenessFaceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
			
		} else {
			livenessBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
					GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS);
			livenessTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, 
					GlobalVal.CODE_LOV_TRX_TYPE_ULIVENESS);
			livenessNotes = "Signing Liveness Success";
			
			livenessBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, livenessBalanceType);
			
			faceCompareBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
					GlobalVal.CODE_LOV_BALANCE_TYPE_FACECOMPARE);
			faceCompareTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, 
					GlobalVal.CODE_LOV_TRX_TYPE_UFACECOMPARE);
			faceCompareNotes = "Signing Face Compare Success";
			
			faceCompareBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, faceCompareBalanceType);
			
			if (livenessBalance.longValue() < 1 && faceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage("businesslogic.saldo.balancesarenotenough", new Object[] {livenessBalanceType.getDescription(), faceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			} else if (livenessBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {livenessBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			} else if (faceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {faceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
		}
		
		Short livenessFacecompareRequestNum = user.getLivenessFacecompareRequestNum();
		Short livenessFacecompareValidationNum = user.getLivenessFacecompareValidationNum();
		Date date = new Date();
		
		if (null == user.getLivenessFacecompareRequestDate() || !DateUtils.isSameDay(date, user.getLivenessFacecompareRequestDate())) {
			livenessFacecompareRequestNum = (short) 0;
		}
		
		if (null == user.getLivenessFacecompareValidationDate() || !DateUtils.isSameDay(date, user.getLivenessFacecompareValidationDate())) {
			livenessFacecompareValidationNum = (short) 0;
		}
		
		user.setLivenessFacecompareRequestDate(date);
		String gsLimitRequest = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT, audit);
		short maxLimitLivenessFacecompareRequest = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT, Short.parseShort(gsLimitRequest));
		String gsLimitValidation = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, audit);
		short maxLimitLivenessFacecompareValidation = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, Short.parseShort(gsLimitValidation));
		
		
		if ((null != livenessFacecompareRequestNum && livenessFacecompareRequestNum >= maxLimitLivenessFacecompareRequest) || (null != livenessFacecompareValidationNum && livenessFacecompareValidationNum >= maxLimitLivenessFacecompareValidation)) {
			throw new UserException(getMessage("businesslogic.user.maxlivenessfacecomparereached", null, audit),
					ReasonUser.MAX_LIVENESS_FACECOMPARE_REACHED);
		}
	
				
		String key = tenant.getApiKey();
		String livenessFaceCompareTenantCode = GlobalVal.TENANT_CODE_LIVENESS_FACECOMPARE + "-" + request.getTenantCode();
		
		if (userPersonalData.getPhoneBytea() == null) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VIDA_SELFPHOTO_NOT_FOUND, null, audit),
					ReasonUser.PHOTO_NOT_FOUND);
		}
		
		String cutImg = MssTool.cutImageStringPrefix(request.getImg1());
		byte[] img1 =  Base64.getDecoder().decode(cutImg);
		byte[] img2 = pd.getSelfPhotoRaw();
		
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_LIVENESS_FACECOMPARE);

		LivenessFaceCompareRequest livenessFaceCompareRequest = new LivenessFaceCompareRequest(user.getLoginId(), livenessFaceCompareTenantCode, request.getVendorCode(), nik, key, img1, img2);
		LivenessFaceCompareResponse livenessFaceCompareResponse =  new LivenessFaceCompareResponse();

		String generalSetting = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_BYPASS_LIVENESS_FACE_COMPARE_CHECK);

		Status status = new Status();

		if ("0".equals(generalSetting) || generalSetting.isEmpty()) {
			try {
				livenessFaceCompareResponse =  livenessFaceCompare(livenessFaceCompareRequest, audit);
			} catch(VidaException e){
				if (null != livenessFacecompareValidationNum) {
					Short value = (short) (livenessFacecompareValidationNum + 1);
					user.setLivenessFacecompareValidationNum(value);
				} else {
					Short value = 1;
					user.setLivenessFacecompareValidationNum(value);
				}
	
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
	
				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
				auditTrail.setEmail(auditTrailBean.getEmail());
				auditTrail.setAmMsUser(auditTrailBean.getUser());
				auditTrail.setMsTenant(auditTrailBean.getTenant());
				auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(auditTrailBean.getEmail());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				
				String textFilename = String.valueOf(auditTrail.getIdSigningProcessAuditTrail()) + GlobalVal.EXTENTION_PICTURE_JPEG;
				
				byte[] zipImg1 = ZipCompressionUtils.zipBase64Data(textFilename, request.getImg1());
				
				String filename = cloudStorageLogic.storeLivenessFaceComparePhoto(auditTrail.getIdSigningProcessAuditTrail(), zipImg1);
				
				auditTrail.setNotes(filename);
				daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrail(auditTrail);
	
				for (TrDocumentD documentD : auditTrailBean.getDocumentDs()) {
	
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(documentD);
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
				}
	
				user.setLivenessFacecompareValidationDate(new Date());
				user.setUsrCrt(audit.getCallerId());
				user.setDtmUpd(date);
				daoFactory.getUserDao().updateUser(user);
				
				status.setCode(e.getErrorCode());
				status.setMessage(e.getMessage());
				response.setStatus(status);
				return response;
				
			}
			
			status.setCode(0);	
			
			String faceCompareResult = livenessFaceCompareResponse.getResult().get(0).getFaceCompare().getCompare();
			String faceLivenessResult = livenessFaceCompareResponse.getResult().get(0).getFaceLiveness().getLive();
			String service = livenessFaceCompareResponse.getService();
			String resultStatus = "0";
	
			if(faceCompareResult.equals("True")  && faceLivenessResult.equals("True")) {
				user.setLivenessFacecompareRequestNum((short) 0);
				resultStatus = "1";
			} else {
				if (faceLivenessResult.equals("False")) {
					status.setCode(StatusCode.LIVENESS_FAILED);
					status.setMessage("Verifikasi Liveness gagal. Harap mengambil Foto Selfie langsung, Pastikan wajah anda terlihat jelas tidak tertutup oleh aksesoris.");
					livenessNotes = CONST_SIGNING_LIVENESS_FAILED;
				} else if (faceCompareResult.equals("False") && faceLivenessResult.equals("True")) {
					status.setCode(StatusCode.FACE_COMPARE_FAILED);
					status.setMessage("Verifikasi user gagal. Foto Diri tidak sesuai.");
					faceCompareNotes = CONST_SIGNING_FACE_COMPARE_FAILED;
				} else if (faceCompareResult.equals("False") && faceLivenessResult.equals("False")) {
					status.setCode(StatusCode.LIVENESS_AND_FACE_COMPARE_FAILED);
					status.setMessage("Verify Liveness Face Compare Gagal");
					livenessNotes = CONST_SIGNING_LIVENESS_FAILED;
					faceCompareNotes = CONST_SIGNING_FACE_COMPARE_FAILED;
				}
				livenessFaceCompareNotes = "Signing Liveness Face Compare Failed";
				
				if (null != livenessFacecompareRequestNum) {
					Short value = (short) (livenessFacecompareRequestNum + 1);
					user.setLivenessFacecompareRequestNum(value);
				} else {
					Short value = 1;
					user.setLivenessFacecompareRequestNum(value);
				}
			}
			
			user.setUsrCrt(audit.getCallerId());
			user.setDtmUpd(date);
			daoFactory.getUserDao().updateUser(user);
			
			if (splitLivenessFaceCompareBill.equals("0")) {
				saldoLogic.insertBalanceMutation(null, docH, docD, livenessFaceCompareBalanceType, livenessFaceCompareTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessFaceCompareNotes, null, audit);
			} else if (splitLivenessFaceCompareBill.equals("1") && service.equals("1")){
				saldoLogic.insertBalanceMutation(null, docH, docD, livenessBalanceType, livenessTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessNotes, null, audit);
			} else if (splitLivenessFaceCompareBill.equals("1") && service.equals("2")) {
				saldoLogic.insertBalanceMutation(null, docH, docD, livenessBalanceType, livenessTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessNotes, null, audit);
				
				nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
				trxNoNum = trxNoNum + 1;
				trxNo.add(trxNoNum, String.valueOf(nextTrxNo));
				
				saldoLogic.insertBalanceMutation(null, docH, docD, faceCompareBalanceType, faceCompareTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, faceCompareNotes, null, audit);
			}
	
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
	
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovProcessType(signingProcessTypeLov);
			auditTrail.setResultStatus(resultStatus);
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			
			String textFilename = String.valueOf(auditTrail.getIdSigningProcessAuditTrail()) + GlobalVal.EXTENTION_PICTURE_JPEG;
			
			byte[] zipImg1 = ZipCompressionUtils.zipBase64Data(textFilename, request.getImg1());
			
			String filename = cloudStorageLogic.storeLivenessFaceComparePhoto(auditTrail.getIdSigningProcessAuditTrail(), zipImg1);
			
			auditTrail.setNotes(filename);
			daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrail(auditTrail);
	
			for (TrDocumentD documentD : auditTrailBean.getDocumentDs()) {
	
				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(documentD);
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
			}
			
			response.setTrxNo(trxNo);
		} else {
			status.setCode(0);
		}


		response.setStatus(status);
		return response;
	}
	
	private void logLivenessFaceCompareRequest(LivenessFaceCompareRequest request) {
		
		String img1Filename = GlobalVal.PREFIX_PHOTO_SELFIE_FILE_NAME + request.getLoginId() + GlobalVal.EXTENTION_PICTURE_JPEG;
		String img2Filename = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + request.getLoginId() + GlobalVal.EXTENTION_PICTURE_JPEG;
		
		Map<String, Object> requestParam = new HashMap<>();
		requestParam.put("tenant_code", request.getTenantCode());
		requestParam.put("key", request.getKey());
		requestParam.put("nik", request.getNik());
		requestParam.put("img1", img1Filename);
		requestParam.put("img2", img2Filename);
		
		LOG.info("Liveness face compare request img1 size: {}, img2 size:{}, body: {}", request.getImg1().length, request.getImg2().length, requestParam);
	}
	
	private LivenessFaceCompareResponse livenessFaceCompare(LivenessFaceCompareRequest request, AuditContext audit) {
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
	
		mapHeader.add(headerContentType, headerMultipart);
		
		WebClient client = WebClient.create(urlVerification + urlLivenessFacecompare).headers(mapHeader);
		
		LOG.info("url", String.valueOf(urlVerification));
		LOG.info("url", String.valueOf(urlLivenessFacecompare));
		
		List<Attachment> atts = new LinkedList<>();
		
		ContentDisposition cdTenant = new ContentDisposition("form-data; name=\"tenant_code\"");
		ContentDisposition cdNik = new ContentDisposition("form-data; name=\"nik\"");
		ContentDisposition cdKey = new ContentDisposition("form-data; name=\"key\"");
		
		atts.add(new Attachment("tenant_code", new ByteArrayInputStream(request.getTenantCode().getBytes()), cdTenant));
		atts.add(new Attachment("nik", new ByteArrayInputStream(request.getNik().getBytes()), cdNik));
		atts.add(new Attachment("key", new ByteArrayInputStream(request.getKey().getBytes()), cdKey));
		
		try {
			String imageSelfieFileName = GlobalVal.PREFIX_PHOTO_SELFIE_FILE_NAME + request.getLoginId();
			ContentDisposition cdSelf = new ContentDisposition("form-data; name=\"img1\"; filename=\""+imageSelfieFileName+".jpeg\"");
			byte[] dataImageSelfiePhoto = request.getImg1();
			atts.add(new Attachment("img1", new ByteArrayInputStream(dataImageSelfiePhoto), cdSelf));
		} catch (Exception e) {
			throw new VidaException(messageSource.getMessage("businesslogic.vida.errorprocessingselfie",
					null, retrieveLocaleAudit(audit)));
			
		}
		
		try {
			String imageSelfFileName =  GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + request.getLoginId();
			ContentDisposition cdDiri = new ContentDisposition("form-data; name=\"img2\"; filename=\""+imageSelfFileName+".jpeg\"");
			byte[] dataImageSelfPhoto = request.getImg2();
			atts.add(new Attachment("img2", new ByteArrayInputStream(dataImageSelfPhoto), cdDiri));
		} catch (Exception e) {
			throw new VidaException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VIDA_SELFPHOTO_NOT_FOUND,
					null, retrieveLocaleAudit(audit)));
		}
		
		logLivenessFaceCompareRequest(request);
		
		long startTime = System.currentTimeMillis();
		MultipartBody body = new MultipartBody(atts);
		Response response = client.post(body);
		long endTime = System.currentTimeMillis(); 
		

		long durationInMillis = endTime - startTime;
		
		
		if (durationInMillis >= 5000) {
			LOG.info("Liveness face compare duration more than 5 sec : {} ms", String.valueOf(durationInMillis));
		} else if (durationInMillis >= 10000) {
			LOG.info("Liveness face compare duration more than 10 sec: {} ms", String.valueOf(durationInMillis));
		}
		 
		
		LOG.info("Liveness face compare response code: {} {}", response.getStatusInfo().getStatusCode(), response.getStatusInfo().getReasonPhrase());

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String result = StringUtils.EMPTY;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Liveness face compare error", e);
		}
		
		LOG.info("{}: {}", GlobalVal.CONST_LIVENESS_FACECOMPARE_RESPONSE,result);
		
		LivenessFaceCompareResponse livenessFaceCompResponse = gson.fromJson(result, LivenessFaceCompareResponse.class);
		
		LOG.info("{} : {}", GlobalVal.CONST_LIVENESS_FACECOMPARE_RESPONSE, livenessFaceCompResponse.getError());
		
		LOG.info("{} : {}", GlobalVal.CONST_LIVENESS_FACECOMPARE_RESPONSE, response.getStatusInfo().getStatusCode());

		

		
		if (response.getStatusInfo().getStatusCode() == 500) {
			if (livenessFaceCompResponse.getError().equals("Face not detected")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_NOT_DETECTED, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("More than one face detected")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_MORE_THAN_ONE_FACE_DETECTED, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Face out of position")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_OUT_OF_POSITION, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Face too close")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_CLOSE, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Face too far")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_FAR, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Eye glasses detected")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_EYE_GLASSES_DETECTED, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Face too bright")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_BRIGHT, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Face too dark")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_DARK, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Masking failed")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_MASKING_FAILED, null, audit));
			} else if (livenessFaceCompResponse.getError().equals("Backlight detected. Retake the photo with no lights behind.")) {
				throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_BACKLIGHT_DETECTED, null, audit));
			} else {
				throw new VidaException(livenessFaceCompResponse.getError());
			}
			
		}
		return livenessFaceCompResponse;
	}

	@Override
	public CheckRegistrationResponse checkRegistration(CheckRegistrationRequest request,String apiKey, AuditContext audit) {
		
		tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		boolean numeric = request.getUserData().matches("\\d+");
		CheckRegistrationResponse response = new CheckRegistrationResponse();
		
		AmMsuser user = null;
		
		
//		pencarian menggunakan email
		if("EMAIL".equals(StringUtils.upperCase(request.getDataType()))) {
			
			if (StringUtils.isNotBlank(request.getUserData())) {

				Pattern pattern = Pattern.compile(regexEmail);
				Matcher matcher = pattern.matcher(request.getUserData());

				if (!matcher.matches()) {
					throw new FormatException(getMessage("businesslogic.external.emailinvalid", null, audit),
							ReasonFormat.INVALID_FORMAT);
				}
			}
			if(request.getUserData() == null || request.getUserData().equals("") ) {
				throw new UserManagementException(getMessage("businesslogic.external.emailempty", null, audit),
						ReasonUserManagement.LOGIN_ID_IS_NOT_FOUND);
			}
			
			String email = request.getUserData();
			user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
			if (null == user) {
				throw new UserException(getMessage("businesslogic.external.emailnotfound", new Object[] {request.getUserData()}, audit),
						ReasonUser.USER_NOT_FOUND_WITH_THAT_EMAIL);
			}
		}
//		pencarian menggunakan NIK
		else if("NIK".equals(StringUtils.upperCase(request.getDataType()))) {
			if (StringUtils.isNotBlank(request.getUserData())) {
				if (!numeric) {
					throw new UserException(
							getMessage("businesslogic.external.idnumber", null, audit),
							ReasonUser.NIK_NOT_NUMBER);
				}
				else if (request.getUserData().length() != 16) {
					throw new UserException(
							getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_NIK_LENGTH, null, audit),
							ReasonUser.INVALID_NIK_LENGTH);
				}
				
			}
			else if(request.getUserData() == null || request.getUserData().equals("") ) {
				throw new UserManagementException(getMessage("businesslogic.external.idempty", null , audit),
						ReasonUserManagement.LOGIN_ID_IS_NOT_FOUND);
			}
//			kalo berhasil
			user = daoFactory.getUserDao().getUserByIdNo(request.getUserData());
			if (null == user) {
				throw new UserManagementException(getMessage("businesslogic.external.idnotfound", new Object[] {request.getUserData()}, audit),
						ReasonUserManagement.USER_NOT_FOUND_WITH_THAT_ID_NO);
			}
			
		}
//		pencarian menggunakan nohp
		else if("PHONE".equals(StringUtils.upperCase(request.getDataType()))) {
			
			if (StringUtils.isNotBlank(request.getUserData())) {
				if (!numeric) {
					throw new UserManagementException(
							getMessage("businesslogic.external.phonenumber", null, audit),
							ReasonUserManagement.NO_KTP_IS_NOT_VALID);
				}
				String phoneRegex = commonLogic.getGeneralSettingValueByCode("AM_PHONE_FORMAT", audit);
				Pattern pattern = Pattern.compile(phoneRegex);
				Matcher matcher = pattern.matcher(request.getUserData());
				if (!matcher.matches()) {
					throw new FormatException(
							getMessage("businesslogic.user.invalidphonenoformat", null, audit),
							ReasonFormat.INVALID_FORMAT);
				}
				
			}
			else if(request.getUserData() == null || request.getUserData().equals("") ) {
				throw new UserManagementException(getMessage("businesslogic.external.phoneempty", null , audit),
						ReasonUserManagement.LOGIN_ID_IS_NOT_FOUND);
			}
			user = daoFactory.getUserDao().getActiveUserByPhone(request.getUserData());
			if (null == user) {
				throw new UserException(getMessage("businesslogic.external.phonenotfound", new Object[] {request.getUserData()}, audit),
						ReasonUser.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
			}
			
		}
		else {
			throw new UserManagementException(getMessage("businesslogic.external.datatypeinvalid", null , audit),
					ReasonUserManagement.DATA_TYPE_INVALID);
		}
		
		List<Map<String,Object>> vendor2 = daoFactory.getVendorDao().getListVendorByIdMsUser(user.getIdMsUser());
		List<MsVendorRegisteredUser> vendorregistered = new ArrayList<>();
		
		for (Map<String, Object> msVendorRegisteredUser : vendor2) {
			MsVendorRegisteredUser vendorregistered1 = daoFactory.getUserDao().getVendorRegisteredUser(user.getIdMsUser(), (String) msVendorRegisteredUser.get("d0"));
			vendorregistered.add(vendorregistered1);
		}

		List<CheckRegistrationBean> listCheck = new ArrayList<>();
		
		Iterator<MsVendorRegisteredUser> itr = vendorregistered.iterator();
		Iterator<Map<String,Object>> itr2 = vendor2.iterator();
		while(itr.hasNext()) {
			MsVendorRegisteredUser map = itr.next();
			 Map<String,Object> map2 = itr2.next();
			 
			CheckRegistrationBean listRegis = new CheckRegistrationBean();
			
			if (map == null) {
				listRegis.setVendor((String) map2.get("d1"));
				listRegis.setRegistrationStatus("0");
			}
			else {
				listRegis.setVendor(map.getMsVendor().getVendorName());
				if (map.getIsActive().equals("1") && map.getIsRegistered().equals("1")) {
					listRegis.setRegistrationStatus("2");
				} else if(map.getIsActive().equals("0") && map.getIsRegistered().equals("1")) {
					listRegis.setRegistrationStatus("1");
				} else if(map.getIsActive().equals("0") && map.getIsRegistered().equals("0")) {
					listRegis.setRegistrationStatus("0");
				}
				if (map.getCertExpiredDate() != null ) {
					if (!GlobalVal.VENDOR_CODE_VIDA.equals(map.getMsVendor().getVendorCode()) || !userValidatorLogic.isCertifExpiredForInquiry(map) ) {
						listRegis.setCertificateActiveStatus("1");
					} else {
						listRegis.setCertificateActiveStatus("0");
					}
				}
				
			}
			
			listCheck.add(listRegis);
			
		}
		
		response.setRegistrationData(listCheck);
		
		Status status = new Status();
		status.setCode(0);	
		response.setStatus(status);
		
		return response;
	}

	@Override
	public GetSignerDetailWebviewResponse getSignerDetailWebview(GetSignerDetailWebviewRequest request,
			AuditContext audit) {
		List<TrSignLinkRequest> signLinkRequest = signLinkValidatorLogic.validateGetSignLinkRequestsByEncryptedMsg(request.getMsg(), audit);
		
		AmMsuser amMsuser = signLinkRequest.get(0).getAmMsuser();
		
		if (null == amMsuser) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		
		TrDocumentD trDocumentD = signLinkRequest.get(0).getTrDocumentD();
		
		if (null == trDocumentD) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, null, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}
		
		MsVendor vendor = trDocumentD.getMsVendor();
		
		MsVendorRegisteredUser signerDetail = daoFactory.getUserDao().getSignerDetail(amMsuser.getIdMsUser(), vendor.getVendorCode());
		
		if (null == signerDetail) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}
		
		GetSignerDetailWebviewResponse response = new GetSignerDetailWebviewResponse();
		if (signerDetail.getPhoneBytea() != null) {
			response.setPhoneNo(personalDataEncLogic.decryptToString(signerDetail.getPhoneBytea()));
		}
		response.setEmail(signerDetail.getSignerRegisteredEmail());
		return response;
	}

	private String sendOtpWa(String phoneNo, String otpCode, MsTenant tenant, AmMsuser user, AuditContext audit, TrDocumentH docH, MsOffice office, MsBusinessLine businessLine, String notes, SigningProcessAuditTrailBean auditTrailBean) {
		
		MsMsgTemplate templateWa = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage(GlobalKey.MESSAGE_TEMPLATE_WITHOUT_ACTIVE_DURATION, new Object[] { tenant.getOtpActiveDuration() }, audit), 
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}
		
		SendWhatsAppRequest sendWhatsappRequest = new SendWhatsAppRequest();
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			sendWhatsappRequest.setTemplate(templateWa);
			sendWhatsappRequest.setBodyTexts(bodyTexts);
			sendWhatsappRequest.setButtonText(buttonText);
			sendWhatsappRequest.setReservedTrxNo(trxNo);
			sendWhatsappRequest.setPhoneNumber(phoneNo);
			sendWhatsappRequest.setAmMsuser(user);
			sendWhatsappRequest.setMsTenant(tenant);
			sendWhatsappRequest.setRemoveHeader(true);
			sendWhatsappRequest.setTrDocumentH(docH);
			sendWhatsappRequest.setMsOffice(office);
			sendWhatsappRequest.setMsBusinessLine(businessLine);
			sendWhatsappRequest.setNotes(notes);
			sendWhatsappRequest.setIsOtp(true);
			whatsAppLogic.sendMessage(sendWhatsappRequest, auditTrailBean, audit);
		}
		else {
			LOG.info("Send WA OTP Signing success");
		}
		return trxNo;
	}
	

	private String sendOtpWaHaloSis(String phoneNo, String otpCode, MsTenant tenant, AmMsuser user, AuditContext audit, TrDocumentH docH, MsOffice office, MsBusinessLine businessLine, String notes, SigningProcessAuditTrailBean auditTraiBean) {
		
		MsMsgTemplate templateWa = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage(GlobalKey.MESSAGE_TEMPLATE_WITHOUT_ACTIVE_DURATION, new Object[] { tenant.getOtpActiveDuration() }, audit), 
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}
		
		HalosisSendWhatsAppRequestBean sendWhatsappHalosisRequest = new HalosisSendWhatsAppRequestBean();
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			sendWhatsappHalosisRequest.setTemplate(templateWa);
			sendWhatsappHalosisRequest.setBodyTexts(bodyTexts);
			sendWhatsappHalosisRequest.setButtonText(buttonText);
			sendWhatsappHalosisRequest.setReservedTrxNo(trxNo);
			sendWhatsappHalosisRequest.setPhoneNumber(phoneNo);
			sendWhatsappHalosisRequest.setAmMsuser(user);
			sendWhatsappHalosisRequest.setMsTenant(tenant);
			sendWhatsappHalosisRequest.setTrDocumentH(docH);
			sendWhatsappHalosisRequest.setMsOffice(office);
			sendWhatsappHalosisRequest.setMsBusinessLine(businessLine);
			sendWhatsappHalosisRequest.setNotes(notes);
			sendWhatsappHalosisRequest.setIsOtp(true);	
			whatsAppHalosisLogic.sendMessage(sendWhatsappHalosisRequest, auditTraiBean, audit);
		}
		else {
			LOG.info("Send WA OTP HALOSIS  Signing success");
		}
		return trxNo;
	}
	
	private String sendOtpWaActivation(String phoneNo, String otpCode, TrInvitationLink invLink, AmMsuser user, AuditContext audit,SigningProcessAuditTrailBean auditTrailBean) {
		
		MsMsgTemplate templateWa = null;
		MsTenant tenant = invLink.getMsTenant();
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage(GlobalKey.MESSAGE_TEMPLATE_WITHOUT_ACTIVE_DURATION, new Object[] { tenant.getOtpActiveDuration() }, audit), 
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}
		
		
		SendWhatsAppRequest sendWhatsappRequest = new SendWhatsAppRequest();
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			sendWhatsappRequest.setTemplate(templateWa);
			sendWhatsappRequest.setBodyTexts(bodyTexts);
			sendWhatsappRequest.setButtonText(buttonText);
			sendWhatsappRequest.setReservedTrxNo(trxNo);
			sendWhatsappRequest.setPhoneNumber(phoneNo);
			sendWhatsappRequest.setAmMsuser(user);
			sendWhatsappRequest.setMsTenant(tenant);
			sendWhatsappRequest.setRemoveHeader(true);
			sendWhatsappRequest.setMsOffice(invLink.getMsOffice());
			sendWhatsappRequest.setMsBusinessLine(invLink.getMsBusinessLine());
			sendWhatsappRequest.setRefNo(invLink.getRefNumber());
			sendWhatsappRequest.setNotes(phoneNo + " : Send OTP WhatsApp Activation Request");
			sendWhatsappRequest.setIsOtp(true);
			whatsAppLogic.sendMessage(sendWhatsappRequest,auditTrailBean, audit);
		} else {
			LOG.info("Send WA OTP  activation success");
		}
		return trxNo;
	}
	
	private String sendOtpWaHalosisActivation(String phoneNo, String otpCode, TrInvitationLink invLink, AmMsuser user, AuditContext audit,SigningProcessAuditTrailBean auditTrailBean) {
		
		MsMsgTemplate templateWa = null;
		MsTenant tenant = invLink.getMsTenant();
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage(GlobalKey.MESSAGE_TEMPLATE_WITHOUT_ACTIVE_DURATION, new Object[] { tenant.getOtpActiveDuration() }, audit), 
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}
		
		HalosisSendWhatsAppRequestBean sendWhatsappHalosisRequest = new HalosisSendWhatsAppRequestBean();
		
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			sendWhatsappHalosisRequest.setTemplate(templateWa);
			sendWhatsappHalosisRequest.setBodyTexts(bodyTexts);
			sendWhatsappHalosisRequest.setButtonText(buttonText);
			sendWhatsappHalosisRequest.setReservedTrxNo(trxNo);
			sendWhatsappHalosisRequest.setPhoneNumber(phoneNo);
			sendWhatsappHalosisRequest.setAmMsuser(user);
			sendWhatsappHalosisRequest.setMsTenant(tenant);
			sendWhatsappHalosisRequest.setMsOffice(invLink.getMsOffice());
			sendWhatsappHalosisRequest.setMsBusinessLine(invLink.getMsBusinessLine());
			sendWhatsappHalosisRequest.setRefNo(invLink.getRefNumber());
			sendWhatsappHalosisRequest.setNotes(phoneNo + " : Send OTP WhatsApp Activation Request");
			sendWhatsappHalosisRequest.setIsOtp(true);
			whatsAppHalosisLogic.sendMessage(sendWhatsappHalosisRequest,auditTrailBean, audit);
		} else {
			LOG.info("Send WA Halosis OTP activation success");
		}
		return trxNo;
	}
	
	private void sendEmailCertificateNotification(String fullname, String email, AuditContext audit) {
		Map<String, Object> userMap = new HashMap<>();
		userMap.put(FULLNAME, fullname);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userMap);
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_EMAIL_PENERBITAN_SERTIFIKAT_ELEKTRONIK,
				templateParameters);

		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setSubject(template.getSubject());
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(new String[] { email });
		try {
			emailSenderLogic.sendEmail(emailInfo, null);
		} catch (MessagingException e) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMAIL_SENDER, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.SEND_EMAIL_CERTIFICATE_NOTIFICATION_ERROR);
		}
	}
	
	private void sendSmsCertificateNotification(String phoneNo, MsTenant tenant, AmMsuser user,TrInvitationLink invLink, TrSigningProcessAuditTrail auditTrail, AuditContext audit) {

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_SMS, GlobalVal.TEMPLATE_SMS_PENERBITAN_SERTIFIKAT_ELEKTRONIK);
		
		SendSmsResponse responseSms = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, template.getBody(), tenant);
		if (gs.getGsValue().equals("1")) {
			responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean);
		} else {
			LOG.info("Send SMS Penerbitan Sertifikat Elektronik Success");
			responseSms.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = phoneNo + " : Send SMS Penerbitan Sertifikat Elektronik";
		
		if (responseSms.getErrorCode() == null || 
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), invLink.getRefNumber(),
					-1, String.valueOf(responseSms.getTrxNo()), user, notes, responseSms.getGuid(),invLink.getMsOffice(),invLink.getMsBusinessLine(), audit);
		} else {
			auditTrail.setResultStatus("0");
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), invLink.getRefNumber(),
					0, String.valueOf(responseSms.getTrxNo()), user, notes + GlobalVal.BALMUT_ERROR + responseSms.getErrorCode(),
					responseSms.getGuid(),invLink.getMsOffice(),invLink.getMsBusinessLine(), audit);
		}
	}
	
	private void sendSmsCertNotifJatis(String phoneNo, MsTenant tenant, AmMsuser user,TrInvitationLink invLink, TrSigningProcessAuditTrail auditTrail, AuditContext audit) {
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_SMS, GlobalVal.TEMPLATE_SMS_PENERBITAN_SERTIFIKAT_ELEKTRONIK);
		
		SigningProcessAuditTrailBean bean = new SigningProcessAuditTrailBean();
		bean.setEmail(auditTrail.getEmail());
		bean.setInvLink(invLink);
		bean.setLovProcessType(auditTrail.getLovProcessType());
		bean.setLovSendingPoint(auditTrail.getLovSendingPoint());
		bean.setNotes(auditTrail.getNotes());
		bean.setPhone(phoneNo);
		bean.setTenant(tenant);
		bean.setUser(user);
		bean.setVendorPsre(auditTrail.getMsVendor());
		
		MsOffice office = invLink.getMsOffice();
		MsBusinessLine businessLine = invLink.getMsBusinessLine();
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		String refNo = invLink.getRefNumber();
		String notes = phoneNo + " : Send SMS Penerbitan Sertifikat Elektronik";
		JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, office, businessLine, phoneNo, template.getBody(), trxNo, refNo, false);
		jatisSmsLogic.sendSmsAndCutBalance(request, null, null, user, notes, audit, bean);
	}
	
	private void sendOtpSmsJatis(String phoneNo, String otpCode, String notes, String refNo, MsTenant tenant, AmMsuser user, MsOffice office, MsBusinessLine businessLine, AuditContext audit,SigningProcessAuditTrailBean auditTrailBeans) {
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		
		
		MsMsgTemplate template = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		} else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}
		
		if (gs.getGsValue().equals("1")) {
			long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, office, businessLine, phoneNo, template.getBody(), String.valueOf(trxNo), refNo, true);
			jatisSmsLogic.sendSmsAndCutBalance(request, null, null, user, notes, audit, auditTrailBeans);
		} else {
			LOG.info("Send SMS OTP Jatis Success");
		}		
	}
	
	private void sendOtpPrivyGeneral(String phoneNo, MsVendorRegisteredUser msVendorRegisteredUser, AmMsuser user, MsTenant tenant, MsVendor vendor, List<String> documentId, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		
		if (StringUtils.isBlank(msVendorRegisteredUser.getVendorRegistrationId())) {
			throw new PrivyException(getMessage(GlobalKey.MESSAGE_ERROR_PRIVY_IDNOTFOUND, null, audit));
		}
		
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		TrDocumentD docDNewest = new TrDocumentD();
		
		List<String> documentIdsOtpRequestPrivy = new ArrayList<>();
		for (int i = 0; i < documentId.size(); i++ ) {
			  
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId.get(i));
			if (null == docDNewest.getRequestDate() || docD.getRequestDate().after(docDNewest.getRequestDate())  ) {
				docDNewest = docD;
			}
			
			String documentIdAlphanumeric = documentId.get(i).replace("-", "");
			documentIdsOtpRequestPrivy.add(documentIdAlphanumeric);
			
		}
		
		String documentIds = String.join(", ", documentIdsOtpRequestPrivy);
		LOG.info("documentIds : " + documentIds);
		
		TrPsreSigningConfirmation psreSigningConfirmation = new TrPsreSigningConfirmation(user, documentIds, null);
		psreSigningConfirmation.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		psreSigningConfirmation.setDtmCrt(new Date());
		
		daoFactory.getDocumentDao().insertPsreSigningConfirmation(psreSigningConfirmation);

		MsLov lovNotificationMedia = new MsLov();
		lovNotificationMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
		
		PrivyGeneralOtpRequestSigningResponse otpRequestSigning = privyGeneralLogic.otpRequestSigning(msVendorRegisteredUser.getVendorRegistrationId(), tenant, vendor, documentIdsOtpRequestPrivy, audit);
		if (otpRequestSigning.getError() != null) {
			
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
			auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
			auditTrail.setOtpCode(auditTrailBean.getOtpCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
			auditTrail.setNotes(auditTrailBean.getNotes());
			auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
			auditTrail.setNotificationVendor(GlobalVal.VENDOR_CODE_PRIVY_ID);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			if (auditTrailBean.getDocumentDs() != null ) {
				for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);;
				}
			}
			
			String errorMessage = privyGeneralLogic.buildOtpRequestSigningErrorMessage(otpRequestSigning, audit);
			throw new PrivyException(errorMessage);
		}
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(auditTrailBean.getEmail());
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
		auditTrail.setNotificationVendor(GlobalVal.VENDOR_CODE_PRIVY_ID);
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		if (auditTrailBean.getDocumentDs() != null ) {
			for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(docD);
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);;
			}
		}
		
		psreSigningConfirmation.setTransactionId(otpRequestSigning.getData().getTransactionId());
		daoFactory.getDocumentDao().updatePsreSigningConfirmation(psreSigningConfirmation);
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		
		String notes = phoneNo + GlobalVal.SEND_OTP_SMS_SIGNING;
		
		saldoLogic.insertBalanceMutation(null, docDNewest.getTrDocumentH(), null, balanceType, trxType, tenant, vendor, new Date(), docDNewest.getTrDocumentH().getRefNumber(),
				-1, trxNo, user, notes, null,docDNewest.getTrDocumentH().getMsOffice(),docDNewest.getTrDocumentH().getMsBusinessLine(), audit);
	}
	
	@Override
	public DownloadUserCertificateResponse downloadUserCertificate(DownloadUserCertificateRequest request,
			String xApiKey, AuditContext audit) {
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		boolean checkExistence = true;
		
		if (StringUtils.isBlank(request.getPhone())) {
			throw new UserException(getMessage("businesslogic.user.phonenocannotbeempty", null, audit),
					ReasonUser.EMAIL_EMPTY);
		}
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhone(), checkExistence, audit);
		if (StringUtils.isNotBlank(request.getEmail())) {
			AmMsuser userEmail = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), checkExistence, audit);
			if (user != userEmail) {
				throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_REGISTERED_WITH_EMAIL, new Object[] {request.getPhone(), request.getEmail()}, this.retrieveLocaleAudit(audit)),
						ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
			}
		}

		MsVendor vendor;
		if(StringUtils.isNotBlank(request.getPsreCode())) {
			vendor = vendorValidatorLogic.validateGetVendor(request.getPsreCode(), checkExistence, audit);
		} else {
			vendor = vendorValidatorLogic.validateGetMainDefaultVendor(tenant.getTenantCode(), checkExistence, audit);
		}
		
		if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			throw new VendorException(getMessage("businesslogic.vendor.vendornotsupportdownloadcert", new Object[] {vendor.getVendorCode()}, audit), 
					ReasonVendor.VENDOR_NOT_SUPPORT_DOWNLOAD_CERT);
		}
		
		
		MsUseroftenant userOfTenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), tenant.getTenantCode());
		if (null == userOfTenant) {
			throw new UserException(getMessage("businesslogic.user.usernotregisteredintenant", null, audit), 
					ReasonUser.USER_NOT_REGISTERED_IN_TENANT);
		}
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		
		if (null == msVendorRegisteredUser) {
			throw new UserException(getMessage("businesslogic.user.usernotregisteredinvendor", null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}
		
		if ("0".equals(msVendorRegisteredUser.getEmailService()) && StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage("businesslogic.user.emailcannotbeempty", null, audit),
					ReasonUser.EMAIL_EMPTY);
			
		}
		
		byte[] certByte = cloudStorageLogic.getCertificateTknAj(msVendorRegisteredUser.getVendorRegistrationId());
		if (ArrayUtils.isEmpty(certByte)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_CERT_NOTFOUND, null, audit),
					ReasonDocument.CERT_EMPTY);
		}
		DownloadUserCertificateResponse response = new DownloadUserCertificateResponse();
		Status status = new Status();
		status.setCode(0);
		
		response.setUserCertificate(Base64.getEncoder().encodeToString(certByte));
		response.setStatus(status);
		return response;
	}
	
	
	@Override
	public GetUrlForwarderResponse getUrlForwarder(GetUrlForwarderRequest request, AuditContext audit) {
		GetUrlForwarderResponse response = new GetUrlForwarderResponse();
		String urlCode;
		try {
			urlCode = commonLogic.decryptMessageToString(request.getUrlCode(), audit);
		} catch (Exception e) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_URL_FORWARDER_CODE_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.URL_FORWARDER_CODE_INVALID);
			
		}
		
		TrUrlForwarder urlForwarder = daoFactory.getUrlForwarderDao().getUrlForwarderByCode(urlCode);
		if (urlForwarder == null )	{
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_URL_FORWARDER_CODE_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.URL_FORWARDER_CODE_INVALID);
		}
		
		if (StringUtils.isBlank(urlForwarder.getUrlLink()) ) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_URL_FORWARDER_EXPIRED, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.URL_FORWARDER_EXPIRED);
		}
		
		response.setUrlLink(urlForwarder.getUrlLink());
		return response;
	}

	@Override
	public ListUserViewOtpResponse getListUserViewOtp(ListUserViewOtpRequest request, AuditContext audit) {
		ListUserViewOtpResponse response = new ListUserViewOtpResponse();
		List<UserViewOtpBean> userList = new ArrayList<>();
		List<Map<String, Object>> idUserList = daoFactory.getUserDao()
				.getListUserViewOtpWithId(request.getId());
		Iterator<Map<String, Object>> itr = idUserList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BigInteger idMsvendorRegisteredUser = (BigInteger) map.get("d0");

			MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByIdMsVendorRegisteredUser(idMsvendorRegisteredUser.longValue());
			if (vuser == null) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND,
								new String[] { "User" }, this.retrieveLocaleAudit(audit)),
						ReasonUser.PERSONAL_DATA_NOT_FOUND);
			}
			AmMsuser user = vuser.getAmMsuser();
			
			UserViewOtpBean bean = new UserViewOtpBean();
			bean.setEmail(vuser.getSignerRegisteredEmail());
			bean.setName(user.getFullName());
			bean.setVendor(vuser.getMsVendor().getVendorName());
			bean.setStatus("0".equals(vuser.getIsRegistered()) ? ACT_STATUS_NOT_REGIS :"1".equals(vuser.getIsActive()) ? ACT_STATUS_HAS_ACT : ACT_STATUS_NOT_ACT);
			
			userList.add(bean);
		}
		
		response.setUsers(userList);
		
		return response;
	}
	
	@Override
	public ViewOtpResponse viewOtp(ViewOtpRequest request, AuditContext audit) {
		ViewOtpResponse response = new ViewOtpResponse();
		
		if (StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {CONST_EMAIL}, audit), ReasonUser.LOGIN_ID_EMPTY);
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), true, audit);
		
		if (StringUtils.isBlank( user.getOtpCode())) {
			throw new UserException(this.messageSource.getMessage("businesslogic.user.requestotp",
					null, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
		}
		
		response.setOtpCode(user.getOtpCode());
		
		AmMsuser userRequest = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_VIEW_OTP);
		if (actionType == null ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {GlobalVal.CODE_LOV_LOG_ACTION_TYPE_VIEW_OTP}, audit), ReasonParam.INVALID_CONDITION);
			
		}
		
		this.insertUserDataAccessLog(user, userRequest, actionType , request.getIpAddress(), actionType.getDescription(), audit);
		
		return response;
	}
	
	
	public void insertUserDataAccessLog(AmMsuser userRequest, AmMsuser userDataAccessed, MsLov actionType,String ipAddress,String requestDetails, AuditContext audit) {
		if (userRequest == null) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {"user Request"}, audit), ReasonParam.INVALID_CONDITION);
		}
		
		if (userDataAccessed == null) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {"user accessed"}, audit), ReasonParam.INVALID_CONDITION);
			
		}
		
		if (actionType == null) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {"action type"}, audit), ReasonParam.INVALID_CONDITION);
			
		}
		
		String ipAddressdefault;
		if (StringUtils.isBlank( ipAddress)) {
			ipAddressdefault = "127.0.01";
		} else {
			ipAddressdefault = ipAddress;
		}
		
		TrUserDataAccessLog accessLog = new TrUserDataAccessLog();
		accessLog.setUserRequest(userRequest);
		accessLog.setUserDataAccessed(userDataAccessed);
		accessLog.setLovActionType(actionType);
		accessLog.setAccessDate(new Date());
		accessLog.setIpAddress(ipAddressdefault);
		accessLog.setRequestDetails(requestDetails);
		accessLog.setUsrCrt(audit.getCallerId());
		accessLog.setDtmCrt(new Date());
		daoFactory.getUserDataAccessLogDao().insertUserDataAccessLog(accessLog);
		
	}

	public TrUserDataAccessLog insertUserDataAccessLogNewTrx(AmMsuser userRequest, AmMsuser userDataAccessed, MsLov actionType,String ipAddress,String requestDetails, AuditContext audit) {
		if (userRequest == null) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {"user Request"}, audit), ReasonParam.INVALID_CONDITION);
		}
		
		if (userDataAccessed == null) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {"user accessed"}, audit), ReasonParam.INVALID_CONDITION);
			
		}
		
		if (actionType == null) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new String[] {"action type"}, audit), ReasonParam.INVALID_CONDITION);
			
		}
		
		String ipAddressdefault;
		if (StringUtils.isBlank( ipAddress)) {
			ipAddressdefault = "127.0.01";
		} else {
			ipAddressdefault = ipAddress;
		}
		
		TrUserDataAccessLog accessLog = new TrUserDataAccessLog();
		accessLog.setUserRequest(userRequest);
		accessLog.setUserDataAccessed(userDataAccessed);
		accessLog.setLovActionType(actionType);
		accessLog.setAccessDate(new Date());
		accessLog.setIpAddress(ipAddressdefault);
		accessLog.setRequestDetails(requestDetails);
		accessLog.setUsrCrt(audit.getCallerId());
		accessLog.setDtmCrt(new Date());
		daoFactory.getUserDataAccessLogDao().insertUserDataAccessLogNewTrx(accessLog);
		return accessLog;
	}
	
	@Override
	public ViewResetCodeResponse viewResetCode(ViewResetCodeRequest request, AuditContext audit) {
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmail(request.getEmail());
		if (null == vru) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit), ReasonUser.USER_NOT_FOUND);
		}
		
		AmMsuser user = vru.getAmMsuser();
		ViewResetCodeResponse response = new ViewResetCodeResponse();
		response.setResetCode(user.getResetCode());
		
		if (StringUtils.isBlank(user.getResetCode())) {
			throw new UserException(getMessage("businesslogic.user.resetcodenotrequested", null, audit), ReasonUser.RESET_CODE_EMPTY);
		}
		
		MsVendorRegisteredUser caller = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmail(audit.getCallerId());
		AmMsuser callerUser = caller.getAmMsuser();
		MsLov lovActionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_VIEW_RESET_CODE);
		insertUserDataAccessLog(callerUser, user, lovActionType, request.getIpAddress(), GlobalVal.DATAACCESS_LOG_REQDETAIL_VIEW_RESET_CODE, audit);
		
		return response;
	}
	
	
	private MsVendorRegisteredUserHistory addVendorRegisteredUserHistory(MsVendorRegisteredUser msVendorRegisteredUser,TrUserDataAccessLog userDataAccessLog) {
		MsVendorRegisteredUserHistory msVendorRegisteredUserHistory = new MsVendorRegisteredUserHistory();

		msVendorRegisteredUserHistory.setTrUserDataAccessLog(userDataAccessLog);
		msVendorRegisteredUserHistory.setActivatedDate(msVendorRegisteredUser.getActivatedDate());
		msVendorRegisteredUserHistory.setAmMsuser(msVendorRegisteredUser.getAmMsuser());
		msVendorRegisteredUserHistory.setCertExpiredDate(msVendorRegisteredUser.getCertExpiredDate());
		msVendorRegisteredUserHistory.setDtmCrt(msVendorRegisteredUser.getDtmCrt());
		msVendorRegisteredUserHistory.setDtmUpd(msVendorRegisteredUser.getDtmUpd());
		msVendorRegisteredUserHistory.setEmailService(msVendorRegisteredUser.getEmailService());
		msVendorRegisteredUserHistory.setHashedSignerRegisteredPhone(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
		msVendorRegisteredUserHistory.setIsActive(msVendorRegisteredUser.getIsActive());
		msVendorRegisteredUserHistory.setIsExternalActivation(msVendorRegisteredUser.getIsExternalActivation());
		msVendorRegisteredUserHistory.setIsRegistered(msVendorRegisteredUser.getIsRegistered());
		msVendorRegisteredUserHistory.setMsEmailHosting(msVendorRegisteredUser.getMsEmailHosting());
		msVendorRegisteredUserHistory.setMsVendor(msVendorRegisteredUser.getMsVendor());
		msVendorRegisteredUserHistory.setPhoneBytea(msVendorRegisteredUser.getPhoneBytea());
		msVendorRegisteredUserHistory.setPoaId(msVendorRegisteredUser.getPoaId());
		msVendorRegisteredUserHistory.setSignerRegisteredEmail(msVendorRegisteredUser.getSignerRegisteredEmail());
		msVendorRegisteredUserHistory.setUsrCrt(msVendorRegisteredUser.getUsrCrt());
		msVendorRegisteredUserHistory.setUsrUpd(msVendorRegisteredUser.getUsrUpd());
		msVendorRegisteredUserHistory.setVendorAccessToken(msVendorRegisteredUser.getVendorAccessToken());
		msVendorRegisteredUserHistory.setVendorRegistrationId(msVendorRegisteredUser.getVendorRegistrationId());
		msVendorRegisteredUserHistory.setVendorUserAutosignCvv(msVendorRegisteredUser.getVendorUserAutosignCvv());
		msVendorRegisteredUserHistory.setVendorUserAutosignKey(msVendorRegisteredUser.getVendorUserAutosignKey());
		daoFactory.getVendorRegisteredUserHistoryDao().insertVendorRegisteredUserHistoryNewTran(msVendorRegisteredUserHistory);
		return msVendorRegisteredUserHistory;
	}
	
	private AmMsUserHistory addUserHistory(AmMsuser amMsUser,TrUserDataAccessLog userDataAccessLog) {
		AmMsUserHistory amMsUserHistory = new AmMsUserHistory();
		amMsUserHistory.setTrUserDataAccessLog(userDataAccessLog);
		amMsUserHistory.setActivationLink(amMsUser.getActivationLink());
		amMsUserHistory.setChangePwdLogin(amMsUser.getChangePwdLogin());
		amMsUserHistory.setDataChangeRequest(amMsUser.getDataChangeRequest());
		amMsUserHistory.setDtmCrt(amMsUser.getDtmCrt());
		amMsUserHistory.setDtmUpd(amMsUser.getDtmUpd());
		amMsUserHistory.setEmailService(amMsUser.getEmailService());
		amMsUserHistory.setFailCount(amMsUser.getFailCount());
		amMsUserHistory.setFullName(amMsUser.getFullName());
		amMsUserHistory.setHashedIdNo(amMsUser.getHashedIdNo());
		amMsUserHistory.setHashedPhone(amMsUser.getHashedPhone());
		amMsUserHistory.setInitialName(amMsUser.getInitialName());
		amMsUserHistory.setIsActive(amMsUser.getIsActive());
		amMsUserHistory.setIsDeleted(amMsUser.getIsDeleted());
		amMsUserHistory.setIsDormant(amMsUser.getIsActive());
		amMsUserHistory.setIsLocked(amMsUser.getIsLocked());
		amMsUserHistory.setIsLoggedIn(amMsUser.getIsLoggedIn());
		amMsUserHistory.setLastDormant(amMsUser.getLastDormant());
		amMsUserHistory.setLastExpired(amMsUser.getLastExpired());
		amMsUserHistory.setLastLocked(amMsUser.getLastLocked());
		amMsUserHistory.setLastLoggedFail(amMsUser.getLastLoggedFail());
		amMsUserHistory.setLastLoggedIn(amMsUser.getLastLoggedIn());
		amMsUserHistory.setLastRequestOut(amMsUser.getLastRequestOut());
		amMsUserHistory.setLivenessFacecompareRequestDate(amMsUser.getLivenessFacecompareRequestDate());
		amMsUserHistory.setLivenessFacecompareRequestNum(amMsUser.getLivenessFacecompareValidationNum());
		amMsUserHistory.setLivenessFacecompareValidationDate(amMsUser.getLivenessFacecompareValidationDate());
		amMsUserHistory.setLivenessFacecompareValidationNum(amMsUser.getLivenessFacecompareValidationNum());
		amMsUserHistory.setLoginId(amMsUser.getLoginId());
		amMsUserHistory.setLoginProvider(amMsUser.getLoginProvider());
		amMsUserHistory.setMsEmailHosting(amMsUser.getMsEmailHosting());
		amMsUserHistory.setMsOffice(amMsUser.getMsOffice());
		amMsUserHistory.setOtpCode(amMsUser.getOtpCode());
		amMsUserHistory.setPassword(amMsUser.getPassword());
		amMsUserHistory.setPrevLoggedFail(amMsUser.getPrevLoggedFail());
		amMsUserHistory.setPrevLoggedIn(amMsUser.getPrevLoggedIn());
		amMsUserHistory.setReregistrationLink(amMsUser.getReregistrationLink());
		amMsUserHistory.setResetCode(amMsUser.getResetCode());
		amMsUserHistory.setResetCodeRequestDate(amMsUser.getResetCodeRequestDate());
		amMsUserHistory.setUsrCrt(amMsUser.getUsrCrt());
		amMsUserHistory.setUsrUpd(amMsUser.getUsrUpd());
		amMsUserHistory.setVendorResetPassLink(amMsUser.getVendorResetPassLink());
		
		
		
		daoFactory.getUserHistoryDao().insertUserHistroyNewTran(amMsUserHistory);
		return amMsUserHistory;
	}
	
	private AmUserPersonalDataHistory addUserPersonalDataHistory(AmUserPersonalData amUserPersonalData,TrUserDataAccessLog userDataAccessLog) {
		AmUserPersonalDataHistory amUserPersonalDataHistory = new AmUserPersonalDataHistory();
		amUserPersonalDataHistory.setTrUserDataAccessLog(userDataAccessLog);
		amUserPersonalDataHistory.setAddressBytea(amUserPersonalData.getAddressBytea());
		amUserPersonalDataHistory.setAmMsuser(amUserPersonalData.getAmMsuser());
		amUserPersonalDataHistory.setDateOfBirth(amUserPersonalData.getDateOfBirth());
		amUserPersonalDataHistory.setDtmCrt(amUserPersonalData.getDtmCrt());
		amUserPersonalDataHistory.setDtmUpd(amUserPersonalData.getDtmUpd());
		amUserPersonalDataHistory.setEmail(amUserPersonalData.getEmail());
		amUserPersonalDataHistory.setGender(amUserPersonalData.getGender());
		amUserPersonalDataHistory.setIdNoBytea(amUserPersonalData.getIdNoBytea());
		amUserPersonalDataHistory.setPhoneBytea(amUserPersonalData.getPhotoIdBytea());
		amUserPersonalDataHistory.setPhotoSelf(amUserPersonalData.getPhotoSelf());
		amUserPersonalDataHistory.setPhotoIdBytea(amUserPersonalData.getPhotoIdBytea());
		amUserPersonalDataHistory.setPlaceOfBirth(amUserPersonalData.getPlaceOfBirth());
		amUserPersonalDataHistory.setUsrCrt(amUserPersonalData.getUsrCrt());
		amUserPersonalDataHistory.setUsrUpd(amUserPersonalData.getUsrUpd());
		amUserPersonalDataHistory.setZipcodeBean(amUserPersonalData.getZipcodeBean());
		

		daoFactory.getUserHistoryDao().insertUserPersonalDataHistroyNewTran(amUserPersonalDataHistory);
		return amUserPersonalDataHistory;
	}

	@Override
	public decryptUserDataResponse decryptKTP(decryptUserDataRequest request, AuditContext audit) {
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(request.getNikUser());

		PersonalDataBean dataBean = null;

		try {
			dataBean = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), true);
		} catch (Exception e) {
			throw new VendorException(getMessage("businesslogic.user.usernotfound", null, audit),
			ReasonVendor.USER_NOT_FOUND);
		}

		byte[] rawSelfiePhoto = dataBean.getSelfPhotoRaw();
        byte[] rawKtpPhoto = dataBean.getPhotoIdRaw();

		String selfieBase64 = null;
		String ktpBase64 = null;
		decryptUserDataResponse response = new decryptUserDataResponse();


        if (ArrayUtils.isNotEmpty(rawSelfiePhoto)) {
            selfieBase64 = Base64.getEncoder().encodeToString(rawSelfiePhoto);
			response.setBase64Selfie(selfieBase64);
        } else {
			response.setBase64Selfie("");
		}
        
        if (ArrayUtils.isNotEmpty(rawKtpPhoto)) {
            ktpBase64 = Base64.getEncoder().encodeToString(rawKtpPhoto);
			response.setBase64Ktp(ktpBase64);
        } else {
			response.setBase64Ktp("");
        }
		
		return response;
	}

	@Override
	public void insertUserActivityLog(AmMsuser user, MsTenant tenant, AmMsrole role, String loginId, MsLov activityType, AuditContext audit) {
		TrUserActivityLog userActivityLog = new TrUserActivityLog();
		userActivityLog.setIdMsuser(user);
		userActivityLog.setIdMsTenant(tenant);
		userActivityLog.setIdMsRole(role);
		userActivityLog.setLoginId(loginId);
		userActivityLog.setLovUserActivityLog(activityType);
		userActivityLog.setUsrCrt(audit.getCallerId());
		userActivityLog.setDtmCrt(new Date());

		daoFactory.getUserActivityLogDao().insertUserActivityLogNewTrx(userActivityLog);
	}
}